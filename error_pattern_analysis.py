#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测误差模式分析 - 识别模型薄弱环节
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import sys
from pathlib import Path

# 添加模型路径
sys.path.append(str(Path(__file__).parent / 'kongwen_power_control' / 'beta_version' / 'v6' / 'production_deployment' / 'src'))

try:
    from predict import VicePowerPredictor
    PREDICTOR_AVAILABLE = True
except ImportError as e:
    print(f"❌ 副功率预测器导入失败: {e}")
    PREDICTOR_AVAILABLE = False

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ErrorPatternAnalyzer:
    """预测误差模式分析器"""
    
    def __init__(self, data_path):
        """初始化"""
        self.df = pd.read_csv(data_path)
        self.predictor = None
        self.predictions_df = None
        
        if PREDICTOR_AVAILABLE:
            try:
                models_dir = str(Path(__file__).parent / 'kongwen_power_control' / 'beta_version' / 'v6' / 'production_deployment' / 'models')
                self.predictor = VicePowerPredictor(models_dir=models_dir)
                print("✅ 预测器初始化成功")
            except Exception as e:
                print(f"❌ 预测器初始化失败: {e}")
    
    def generate_predictions(self, sample_size=500):
        """生成预测结果"""
        if not self.predictor:
            print("预测器不可用")
            return
        
        print(f"正在生成 {sample_size} 个样本的预测结果...")
        
        # 随机采样
        sample_df = self.df.sample(n=min(sample_size, len(self.df)), random_state=42)
        
        predictions = []
        actual_values = []
        errors = []
        sample_indices = []
        
        for idx, row in sample_df.iterrows():
            try:
                result = self.predictor.predict_single(
                    weight_difference=row['weight_difference'],
                    silicon_thermal_energy_kwh=row['silicon_thermal_energy_kwh'],
                    process_type=row['feed_type']
                )
                
                if result['predicted_vice_power_kwh'] is not None:
                    pred = result['predicted_vice_power_kwh']
                    actual = row['vice_total_energy_kwh']
                    error = abs(pred - actual)
                    
                    predictions.append(pred)
                    actual_values.append(actual)
                    errors.append(error)
                    sample_indices.append(idx)
                    
            except Exception as e:
                continue
        
        # 创建预测结果DataFrame
        self.predictions_df = sample_df.loc[sample_indices].copy()
        self.predictions_df['predicted_vice_power'] = predictions
        self.predictions_df['actual_vice_power'] = actual_values
        self.predictions_df['absolute_error'] = errors
        self.predictions_df['relative_error'] = errors / (np.array(actual_values) + 1e-6) * 100
        
        print(f"成功生成 {len(predictions)} 个预测结果")
        return self.predictions_df
    
    def analyze_error_patterns(self):
        """分析误差模式"""
        if self.predictions_df is None:
            print("请先生成预测结果")
            return
        
        print("\n" + "="*60)
        print("预测误差模式分析")
        print("="*60)
        
        # 基本误差统计
        print(f"\n基本误差统计:")
        print(f"平均绝对误差: {self.predictions_df['absolute_error'].mean():.2f} kWh")
        print(f"误差标准差: {self.predictions_df['absolute_error'].std():.2f} kWh")
        print(f"最大误差: {self.predictions_df['absolute_error'].max():.2f} kWh")
        print(f"误差中位数: {self.predictions_df['absolute_error'].median():.2f} kWh")
        
        # 按工艺类型分析误差
        print(f"\n按工艺类型误差分析:")
        for feed_type in ['首投', '复投']:
            subset = self.predictions_df[self.predictions_df['feed_type'] == feed_type]
            if len(subset) > 0:
                print(f"{feed_type}: MAE={subset['absolute_error'].mean():.2f}, "
                      f"±10kWh准确率={(subset['absolute_error'] <= 10).mean()*100:.1f}%")
        
        # 识别高误差样本
        high_error_threshold = self.predictions_df['absolute_error'].quantile(0.9)
        high_error_samples = self.predictions_df[self.predictions_df['absolute_error'] > high_error_threshold]
        
        print(f"\n高误差样本分析 (误差 > {high_error_threshold:.1f} kWh):")
        print(f"高误差样本数: {len(high_error_samples)} ({len(high_error_samples)/len(self.predictions_df)*100:.1f}%)")
        
        # 分析高误差样本特征
        print(f"\n高误差样本特征分析:")
        print(f"首投比例: {(high_error_samples['feed_type'] == '首投').mean()*100:.1f}%")
        print(f"平均重量差异: {high_error_samples['weight_difference'].mean():.2f} kg")
        print(f"平均硅热能: {high_error_samples['silicon_thermal_energy_kwh'].mean():.2f} kWh")
        print(f"平均持续时间: {high_error_samples['duration_hours'].mean():.2f} h")
        
        return high_error_samples
    
    def analyze_error_by_ranges(self):
        """按数值范围分析误差"""
        if self.predictions_df is None:
            return
        
        print(f"\n按数值范围误差分析:")
        
        # 按副功率范围分析
        power_bins = [0, 200, 400, 600, 1000]
        power_labels = ['低功率(<200)', '中功率(200-400)', '高功率(400-600)', '超高功率(>600)']
        self.predictions_df['power_range'] = pd.cut(self.predictions_df['actual_vice_power'], 
                                                   bins=power_bins, labels=power_labels, include_lowest=True)
        
        print(f"\n按副功率范围:")
        for power_range in power_labels:
            subset = self.predictions_df[self.predictions_df['power_range'] == power_range]
            if len(subset) > 0:
                accuracy_10 = (subset['absolute_error'] <= 10).mean() * 100
                mae = subset['absolute_error'].mean()
                print(f"{power_range}: 样本数={len(subset)}, MAE={mae:.2f}, ±10kWh准确率={accuracy_10:.1f}%")
        
        # 按重量差异范围分析
        weight_bins = [0, 100, 300, 500, 1000]
        weight_labels = ['轻量(<100kg)', '中量(100-300kg)', '重量(300-500kg)', '超重(>500kg)']
        self.predictions_df['weight_range'] = pd.cut(self.predictions_df['weight_difference'], 
                                                    bins=weight_bins, labels=weight_labels, include_lowest=True)
        
        print(f"\n按重量差异范围:")
        for weight_range in weight_labels:
            subset = self.predictions_df[self.predictions_df['weight_range'] == weight_range]
            if len(subset) > 0:
                accuracy_10 = (subset['absolute_error'] <= 10).mean() * 100
                mae = subset['absolute_error'].mean()
                print(f"{weight_range}: 样本数={len(subset)}, MAE={mae:.2f}, ±10kWh准确率={accuracy_10:.1f}%")
    
    def identify_systematic_bias(self):
        """识别系统性偏差"""
        if self.predictions_df is None:
            return
        
        print(f"\n系统性偏差分析:")
        
        # 计算预测偏差
        bias = self.predictions_df['predicted_vice_power'] - self.predictions_df['actual_vice_power']
        self.predictions_df['bias'] = bias
        
        print(f"整体偏差: {bias.mean():.2f} kWh")
        print(f"偏差标准差: {bias.std():.2f} kWh")
        
        # 按工艺类型分析偏差
        for feed_type in ['首投', '复投']:
            subset_bias = bias[self.predictions_df['feed_type'] == feed_type]
            if len(subset_bias) > 0:
                print(f"{feed_type}偏差: {subset_bias.mean():.2f} kWh")
        
        # 按功率范围分析偏差
        print(f"\n按功率范围偏差分析:")
        for power_range in ['低功率(<200)', '中功率(200-400)', '高功率(400-600)', '超高功率(>600)']:
            subset = self.predictions_df[self.predictions_df['power_range'] == power_range]
            if len(subset) > 0:
                subset_bias = subset['bias'].mean()
                print(f"{power_range}: {subset_bias:.2f} kWh ({'高估' if subset_bias > 0 else '低估'})")
    
    def find_improvement_opportunities(self):
        """找出改进机会"""
        if self.predictions_df is None:
            return
        
        print(f"\n" + "="*60)
        print("改进机会识别")
        print("="*60)
        
        # 1. 首投工艺改进机会
        shouTou_subset = self.predictions_df[self.predictions_df['feed_type'] == '首投']
        if len(shouTou_subset) > 0:
            shouTou_accuracy = (shouTou_subset['absolute_error'] <= 10).mean() * 100
            print(f"\n1. 首投工艺改进机会:")
            print(f"   当前±10kWh准确率: {shouTou_accuracy:.1f}%")
            print(f"   样本数不足: {len(shouTou_subset)} (占比{len(shouTou_subset)/len(self.predictions_df)*100:.1f}%)")
            print(f"   建议: 增加首投样本数据，使用数据增强技术")
        
        # 2. 高功率场景改进机会
        high_power_subset = self.predictions_df[self.predictions_df['actual_vice_power'] > 500]
        if len(high_power_subset) > 0:
            high_power_accuracy = (high_power_subset['absolute_error'] <= 10).mean() * 100
            print(f"\n2. 高功率场景改进机会:")
            print(f"   当前±10kWh准确率: {high_power_accuracy:.1f}%")
            print(f"   建议: 优化高功率模型，调整特征权重")
        
        # 3. 特定范围改进机会
        worst_range = None
        worst_accuracy = 100
        
        for power_range in ['低功率(<200)', '中功率(200-400)', '高功率(400-600)', '超高功率(>600)']:
            subset = self.predictions_df[self.predictions_df['power_range'] == power_range]
            if len(subset) > 10:  # 至少10个样本
                accuracy = (subset['absolute_error'] <= 10).mean() * 100
                if accuracy < worst_accuracy:
                    worst_accuracy = accuracy
                    worst_range = power_range
        
        if worst_range:
            print(f"\n3. 最需要改进的范围:")
            print(f"   {worst_range}: ±10kWh准确率仅{worst_accuracy:.1f}%")
            print(f"   建议: 针对该范围设计专门的特征工程和模型")
    
    def run_complete_analysis(self):
        """运行完整的误差分析"""
        print("开始预测误差模式分析...")
        
        # 1. 生成预测结果
        self.generate_predictions(sample_size=500)
        
        if self.predictions_df is not None:
            # 2. 分析误差模式
            high_error_samples = self.analyze_error_patterns()
            
            # 3. 按范围分析误差
            self.analyze_error_by_ranges()
            
            # 4. 识别系统性偏差
            self.identify_systematic_bias()
            
            # 5. 找出改进机会
            self.find_improvement_opportunities()
            
            print(f"\n误差分析完成！")
            return self.predictions_df
        else:
            print("无法生成预测结果，分析终止")
            return None

if __name__ == "__main__":
    analyzer = ErrorPatternAnalyzer('output_results/A01_A40_cycles__analysis.csv')
    results = analyzer.run_complete_analysis()
