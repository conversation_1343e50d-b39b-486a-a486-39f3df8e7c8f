#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正v7预测器，改进预测公式
"""

import os
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def fix_v7_predictor():
    """修正v7预测器"""
    print("🔧 修正v7预测器...")
    
    v7_predictor_path = Path("kongwen_power_control/beta_version/v7/production_deployment/src/v7_simple_predictor.py")
    
    if not v7_predictor_path.exists():
        print(f"❌ 预测器文件不存在: {v7_predictor_path}")
        return False
    
    # 读取原始文件
    with open(v7_predictor_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修正预测公式
    improved_prediction_method = '''    def _simplified_prediction(self, features):
        """改进的预测公式，基于特征工程结果"""
        
        # 基于训练结果的改进权重和公式
        weight_diff = features.get('weight_diff', 0)
        silicon_energy = features.get('silicon_energy', 0)
        is_first_cast = features.get('is_first_cast', 0)
        
        # 改进的预测公式 - 基于物理模型和经验数据
        base_prediction = weight_diff * 0.75 + silicon_energy * 0.65
        
        # 工艺类型调整
        if is_first_cast == 1:
            # 首投工艺通常需要更少的副功率
            process_factor = 0.8
        else:
            # 复投工艺需要标准副功率
            process_factor = 1.0
        
        # 应用工艺因子
        prediction = base_prediction * process_factor
        
        # 添加基础偏移量
        prediction += 50
        
        # 基于能量密度的调整
        energy_density = features.get('energy_density', 1.0)
        if energy_density > 2.0:
            prediction *= 1.1  # 高能量密度需要更多副功率
        elif energy_density < 0.5:
            prediction *= 0.9  # 低能量密度需要较少副功率
        
        # 确保预测值在合理范围内
        prediction = max(80, min(600, prediction))
        
        return prediction'''
    
    # 替换原有的预测方法
    import re
    pattern = r'def _simplified_prediction\(self, features\):.*?return prediction'
    
    if re.search(pattern, content, re.DOTALL):
        new_content = re.sub(pattern, improved_prediction_method.strip(), content, flags=re.DOTALL)
        
        # 备份原文件
        backup_path = v7_predictor_path.with_suffix('.py.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 原文件已备份: {backup_path}")
        
        # 写入改进版本
        with open(v7_predictor_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ 预测器已改进: {v7_predictor_path}")
        
        return True
    else:
        print("❌ 未找到预测方法，无法修正")
        return False

def test_improved_predictor():
    """测试改进的预测器"""
    print("\n🧪 测试改进的预测器...")
    
    try:
        v7_path = Path("kongwen_power_control/beta_version/v7")
        sys.path.insert(0, str(v7_path))
        
        # 清除之前的导入
        modules_to_remove = [m for m in sys.modules.keys() if 'v7_simple_predictor' in m or 'model' in m]
        for module in modules_to_remove:
            del sys.modules[module]
        
        from model import VicePowerControlModel
        v7_model = VicePowerControlModel()
        
        # 测试案例
        test_cases = [
            {'weight': 150, 'energy': 130, 'process': '首投', 'expected_range': [150, 250]},
            {'weight': 300, 'energy': 280, 'process': '复投', 'expected_range': [250, 350]},
            {'weight': 450, 'energy': 400, 'process': '复投', 'expected_range': [350, 500]}
        ]
        
        print(f"执行 {len(test_cases)} 个测试案例:")
        
        for i, case in enumerate(test_cases, 1):
            # 重置模型状态
            if hasattr(v7_model, 'reset_vice_power_state'):
                v7_model.reset_vice_power_state()
            
            # 准备参数
            params = {
                't': 0,
                'ratio': 1.0,
                'ccd': 1400,
                'ccd3': 1400,
                'fullmelting': True,
                'sum_jialiao_time': 3600,
                'last_jialiao_weight': case['weight'],
                'last_Interval_time': 600,
                'barrelage': case['weight'],
                'time_interval': 600,
                'cumulative_feed_weight': case['weight'] * (1 if case['process'] == '首投' else 2)
            }
            
            # 执行预测
            main_power, vice_power, vice_info = v7_model.predict(**params)
            predicted_total = vice_info.get('predicted_total', 0)
            
            # 检查结果
            expected_min, expected_max = case['expected_range']
            in_range = expected_min <= predicted_total <= expected_max
            
            print(f"  案例{i} ({case['process']}): {case['weight']}kg → {predicted_total:.1f}kWh "
                  f"{'✅' if in_range else '⚠️'} (期望: {expected_min}-{expected_max})")
        
        print("✅ 改进预测器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("修正v7预测器")
    print("="*60)
    
    # 1. 环境检查
    if not check_environment():
        return False
    
    # 2. 修正预测器
    if not fix_v7_predictor():
        return False
    
    # 3. 测试改进效果
    if not test_improved_predictor():
        return False
    
    print(f"\n🎉 v7预测器修正完成！")
    print(f"现在可以重新运行准确率验证脚本")
    
    return True

if __name__ == "__main__":
    success = main()
