#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单SVR执行脚本
"""

import os
import sys
import json
from datetime import datetime

def main():
    print("="*60)
    print("🧪 SVR模型执行检查")
    print("="*60)
    
    # 环境检查
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    print(f"当前Conda环境: {conda_env}")
    print(f"Python版本: {python_version}")
    
    # 检查scikit-learn
    try:
        import sklearn
        sklearn_version = sklearn.__version__
        print(f"scikit-learn版本: {sklearn_version}")
        
        if sklearn_version == "1.0.2":
            print("✅ scikit-learn版本正确: 1.0.2")
            sklearn_status = "CORRECT"
        else:
            print(f"⚠️ scikit-learn版本不是1.0.2，当前为: {sklearn_version}")
            sklearn_status = "DIFFERENT"
    except ImportError:
        print("❌ scikit-learn未安装")
        sklearn_status = "MISSING"
        sklearn_version = "NOT_INSTALLED"
    
    # 检查其他包
    packages = {}
    for pkg in ['pandas', 'numpy', 'joblib']:
        try:
            module = __import__(pkg)
            version = getattr(module, '__version__', 'unknown')
            packages[pkg] = version
            print(f"{pkg}版本: {version}")
        except ImportError:
            packages[pkg] = "NOT_INSTALLED"
            print(f"❌ {pkg}未安装")
    
    # 检查数据文件
    from pathlib import Path
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    data_exists = data_file.exists()
    print(f"\n📊 数据文件检查:")
    print(f"文件路径: {data_file}")
    print(f"文件存在: {'✅ 是' if data_exists else '❌ 否'}")
    
    if data_exists:
        try:
            import pandas as pd
            df = pd.read_csv(data_file)
            print(f"数据形状: {df.shape}")
            print(f"主要列: {list(df.columns)[:10]}")
        except Exception as e:
            print(f"数据读取失败: {e}")
    
    # 检查模型文件
    model_path = Path("副功率预测_85.4%准确率_完整项目/models")
    model_files = {
        'model': model_path / "best_model_svr.joblib",
        'scaler': model_path / "scaler.joblib",
        'selector': model_path / "feature_selector.joblib",
        'results': model_path / "results.json"
    }
    
    print(f"\n🔍 模型文件检查:")
    model_status = {}
    for name, file_path in model_files.items():
        exists = file_path.exists()
        model_status[name] = exists
        size = file_path.stat().st_size / (1024*1024) if exists else 0
        print(f"{name}: {'✅' if exists else '❌'} {file_path.name} ({size:.2f}MB)")
    
    # 生成报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        'timestamp': timestamp,
        'environment': {
            'conda_env': conda_env,
            'python_version': python_version,
            'sklearn_version': sklearn_version,
            'sklearn_status': sklearn_status,
            'packages': packages
        },
        'data_file': {
            'path': str(data_file),
            'exists': data_exists
        },
        'model_files': model_status,
        'ready_for_execution': (
            sklearn_status in ['CORRECT', 'DIFFERENT'] and
            data_exists and
            all(model_status.values()) and
            all(v != "NOT_INSTALLED" for v in packages.values())
        )
    }
    
    # 保存报告
    report_file = f"environment_check_{timestamp}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 环境检查报告已保存: {report_file}")
    
    if report['ready_for_execution']:
        print("✅ 环境检查通过，可以执行SVR测试")
        
        # 尝试简单的模型加载测试
        try:
            import joblib
            model_file = model_path / "best_model_svr.joblib"
            if model_file.exists():
                model = joblib.load(model_file)
                print(f"✅ SVR模型加载测试成功: {type(model)}")
            else:
                print("❌ SVR模型文件不存在")
        except Exception as e:
            print(f"❌ SVR模型加载测试失败: {e}")
    else:
        print("❌ 环境检查未通过，请解决上述问题")
    
    return report

if __name__ == "__main__":
    result = main()
    print(f"\n🎯 执行状态: {'准备就绪' if result['ready_for_execution'] else '需要修复'}")
