#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本实时副功率预测器
"""

import joblib
import pandas as pd
import numpy as np
from pathlib import Path
from sklearn.preprocessing import StandardScaler
from v7_feature_engineer import V7FeatureEngineer

class RealtimePredictor:
    """v7版本实时副功率预测器"""

    def __init__(self, model_dir="models"):
        """初始化预测器"""
        self.model_dir = Path(model_dir)

        try:
            # 加载模型组件
            self.model = joblib.load(self.model_dir / "ensemble_model.joblib")
            self.scaler = joblib.load(self.model_dir / "scaler.joblib")

            # 使用独立的特征工程器
            self.feature_engineer = V7FeatureEngineer()

            print("✅ v7实时副功率预测器初始化完成")
        except Exception as e:
            print(f"❌ 预测器初始化失败: {e}")
            raise
    
    def predict(self, weight_difference, silicon_thermal_energy_kwh, 
                feed_type, folder_name=None, start_time=None):
        """
        预测副功率
        
        参数:
            weight_difference: 重量差异 (kg)
            silicon_thermal_energy_kwh: 硅热能需求 (kWh)
            feed_type: 工艺类型 ('首投' 或 '复投')
            folder_name: 设备名称 (可选)
            start_time: 开始时间 (可选)
        
        返回:
            dict: 包含预测结果和置信度
        """
        
        # 创建输入数据
        input_data = pd.DataFrame({
            'weight_difference': [weight_difference],
            'silicon_thermal_energy_kwh': [silicon_thermal_energy_kwh],
            'feed_type': [feed_type],
            'folder_name': [folder_name or 'analoga01'],
            'start_time': [start_time or pd.Timestamp.now()],
            'vice_total_energy_kwh': [0]  # 占位符
        })
        
        # 特征工程
        X = self.feature_engineer.create_realtime_features(input_data)
        
        # 预测
        predicted_power = self.model.predict(X)[0]
        
        # 计算置信度（基于输入数据的合理性）
        confidence = self._calculate_confidence(weight_difference, 
                                               silicon_thermal_energy_kwh, 
                                               feed_type)
        
        return {
            'predicted_vice_power': round(predicted_power, 2),
            'confidence': confidence,
            'input_features': {
                'weight_difference': weight_difference,
                'silicon_thermal_energy_kwh': silicon_thermal_energy_kwh,
                'feed_type': feed_type
            }
        }
    
    def _calculate_confidence(self, weight_diff, silicon_energy, feed_type):
        """计算预测置信度"""
        confidence = "High"
        
        # 检查输入范围
        if weight_diff < 50 or weight_diff > 800:
            confidence = "Low"
        elif silicon_energy < 50 or silicon_energy > 800:
            confidence = "Low"
        elif feed_type == '首投' and weight_diff > 500:
            confidence = "Medium"  # 首投大重量样本较少
        
        return confidence

# 使用示例
if __name__ == "__main__":
    # 初始化预测器
    predictor = RealtimePredictor()
    
    # 示例1：复投工艺预测
    result1 = predictor.predict(
        weight_difference=320.5,
        silicon_thermal_energy_kwh=280.3,
        feed_type='复投',
        folder_name='analoga01',
        start_time='2025-01-01 10:00:00'
    )
    
    print("\n示例1 - 复投工艺:")
    print(f"预测副功率: {result1['predicted_power']} kWh")
    print(f"置信度: {result1['confidence']}")
    
    # 示例2：首投工艺预测
    result2 = predictor.predict(
        weight_difference=180.2,
        silicon_thermal_energy_kwh=150.8,
        feed_type='首投',
        folder_name='analoga02'
    )
    
    print("\n示例2 - 首投工艺:")
    print(f"预测副功率: {result2['predicted_power']} kWh")
    print(f"置信度: {result2['confidence']}")
    
    # 批量预测示例
    test_cases = [
        (250, 220, '复投'),
        (150, 130, '首投'),
        (400, 350, '复投'),
        (100, 90, '首投')
    ]
    
    print("\n批量预测示例:")
    for i, (weight, energy, process) in enumerate(test_cases, 1):
        result = predictor.predict(weight, energy, process)
        print(f"案例{i}: {process} - 预测{result['predicted_power']}kWh (置信度:{result['confidence']})")
