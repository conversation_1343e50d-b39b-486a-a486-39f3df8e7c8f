#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境配置对副加热器控制逻辑优化的影响分析
结合model.py和副功率预测项目的环境要求分析
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

class EnvironmentImpactAnalyzer:
    """环境配置影响分析器"""
    
    def __init__(self):
        self.current_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        self.required_env = 'lj_env_1'
        self.analysis_results = {}
        
    def analyze_model_py_dependencies(self):
        """分析model.py的依赖关系"""
        print("🔍 分析model.py的依赖关系...")
        
        dependencies = {
            'core_packages': {
                'pandas': {
                    'usage': '数据处理和时序数据管理',
                    'impact_level': 'HIGH',
                    'version_sensitivity': 'MEDIUM',
                    'description': '用于处理turnover_data, fime_data, ratio_data等时序数据'
                },
                'numpy': {
                    'usage': '数值计算和数组操作',
                    'impact_level': 'HIGH', 
                    'version_sensitivity': 'HIGH',
                    'description': '影响滑动窗口算法的数值精度和性能'
                },
                'yaml': {
                    'usage': '配置文件读取',
                    'impact_level': 'MEDIUM',
                    'version_sensitivity': 'LOW',
                    'description': '读取turnover_threshold, film_threshold等配置参数'
                },
                'pickle': {
                    'usage': '模型序列化',
                    'impact_level': 'MEDIUM',
                    'version_sensitivity': 'MEDIUM',
                    'description': '保存和加载训练好的预测模型'
                }
            },
            
            'vice_power_predictor': {
                'scikit-learn': {
                    'usage': '机器学习算法',
                    'impact_level': 'VERY_HIGH',
                    'version_sensitivity': 'VERY_HIGH',
                    'description': '副功率预测模型的核心算法，版本不一致会影响预测精度'
                },
                'joblib': {
                    'usage': '模型持久化',
                    'impact_level': 'HIGH',
                    'version_sensitivity': 'HIGH',
                    'description': '模型文件的序列化格式，版本不匹配可能导致加载失败'
                },
                'xgboost': {
                    'usage': '高级机器学习算法',
                    'impact_level': 'HIGH',
                    'version_sensitivity': 'VERY_HIGH',
                    'description': '可能用于副功率预测的集成学习算法'
                }
            }
        }
        
        return dependencies
    
    def analyze_algorithm_sensitivity(self):
        """分析算法对环境的敏感性"""
        print("📊 分析算法对环境的敏感性...")
        
        algorithm_sensitivity = {
            'sliding_window_algorithms': {
                'max_turnover_ratio': {
                    'numpy_dependency': 'HIGH',
                    'precision_impact': 'MEDIUM',
                    'description': '300秒滑动窗口最大值计算，numpy版本影响数值精度'
                },
                'min_turnover_ratio': {
                    'numpy_dependency': 'HIGH', 
                    'precision_impact': 'MEDIUM',
                    'description': '60秒和480秒滑动窗口最小值计算'
                }
            },
            
            'decision_logic': {
                'threshold_comparisons': {
                    'float_precision': 'MEDIUM',
                    'environment_impact': 'LOW',
                    'description': '阈值比较逻辑对环境变化不敏感'
                },
                'film_ratio_calculation': {
                    'numpy_dependency': 'HIGH',
                    'precision_impact': 'HIGH',
                    'description': 'film_calc = min((filtered_value / (100 - ratio_delay)) * 100, 100)'
                }
            },
            
            'vice_power_prediction': {
                'model_compatibility': {
                    'sklearn_version': 'CRITICAL',
                    'joblib_version': 'CRITICAL',
                    'description': '预训练模型与环境版本必须匹配'
                },
                'feature_engineering': {
                    'pandas_version': 'HIGH',
                    'numpy_version': 'HIGH',
                    'description': '特征提取过程对数据处理库版本敏感'
                }
            }
        }
        
        return algorithm_sensitivity
    
    def analyze_performance_impact(self):
        """分析性能影响"""
        print("⚡ 分析性能影响...")
        
        performance_impact = {
            'computational_efficiency': {
                'numpy_optimization': {
                    'impact': 'HIGH',
                    'description': '不同numpy版本的优化程度不同，影响滑动窗口计算速度'
                },
                'pandas_performance': {
                    'impact': 'MEDIUM',
                    'description': '时序数据处理性能随pandas版本变化'
                }
            },
            
            'memory_usage': {
                'data_structures': {
                    'impact': 'MEDIUM',
                    'description': '不同版本的数据结构内存效率不同'
                },
                'model_loading': {
                    'impact': 'LOW',
                    'description': '模型加载内存使用相对稳定'
                }
            },
            
            'real_time_constraints': {
                'prediction_latency': {
                    'impact': 'HIGH',
                    'description': '实时预测延迟受环境配置影响'
                },
                'decision_response_time': {
                    'impact': 'MEDIUM',
                    'description': '决策响应时间的稳定性'
                }
            }
        }
        
        return performance_impact
    
    def analyze_reproducibility_impact(self):
        """分析可复现性影响"""
        print("🔄 分析可复现性影响...")
        
        reproducibility_impact = {
            'model_consistency': {
                'training_environment': {
                    'importance': 'CRITICAL',
                    'current_status': 'GOOD' if self.current_env == self.required_env else 'RISK',
                    'description': '训练和推理环境必须一致以确保模型行为一致'
                },
                'random_seed_behavior': {
                    'importance': 'HIGH',
                    'description': '不同numpy版本的随机数生成器行为可能不同'
                }
            },
            
            'algorithm_determinism': {
                'floating_point_precision': {
                    'importance': 'MEDIUM',
                    'description': '浮点数精度在不同环境下可能有微小差异'
                },
                'library_implementation': {
                    'importance': 'HIGH',
                    'description': '同一算法在不同库版本中的实现可能有差异'
                }
            },
            
            'feature_engineering_consistency': {
                'data_processing_pipeline': {
                    'importance': 'HIGH',
                    'description': '特征工程管道在不同环境下的一致性'
                },
                'temporal_feature_extraction': {
                    'importance': 'MEDIUM',
                    'description': '时序特征提取的稳定性'
                }
            }
        }
        
        return reproducibility_impact
    
    def generate_environment_recommendations(self):
        """生成环境配置建议"""
        print("💡 生成环境配置建议...")
        
        recommendations = {
            'immediate_actions': [],
            'best_practices': [],
            'risk_mitigation': []
        }
        
        # 检查当前环境状态
        if self.current_env != self.required_env:
            recommendations['immediate_actions'].append({
                'priority': 'CRITICAL',
                'action': f'切换到{self.required_env}环境',
                'reason': '确保与训练环境一致',
                'command': f'conda activate {self.required_env}'
            })
        
        # 最佳实践建议
        recommendations['best_practices'].extend([
            {
                'category': '版本固定',
                'recommendation': '使用固定版本的依赖包',
                'implementation': '使用requirements.txt或environment.yml'
            },
            {
                'category': '环境隔离',
                'recommendation': '为每个项目创建独立的conda环境',
                'implementation': '避免包版本冲突'
            },
            {
                'category': '测试验证',
                'recommendation': '在部署前进行环境兼容性测试',
                'implementation': '运行完整的测试套件'
            }
        ])
        
        # 风险缓解策略
        recommendations['risk_mitigation'].extend([
            {
                'risk': '模型加载失败',
                'mitigation': '验证joblib和scikit-learn版本兼容性',
                'monitoring': '监控模型加载错误'
            },
            {
                'risk': '预测精度下降',
                'mitigation': '在新环境中重新验证模型性能',
                'monitoring': '设置性能基准监控'
            },
            {
                'risk': '数值计算差异',
                'mitigation': '使用固定的numpy版本和随机种子',
                'monitoring': '比较关键计算结果'
            }
        ])
        
        return recommendations

def main():
    """主函数"""
    analyzer = EnvironmentImpactAnalyzer()
    
    # 执行各项分析
    dependencies = analyzer.analyze_model_py_dependencies()
    algorithm_sensitivity = analyzer.analyze_algorithm_sensitivity()
    performance_impact = analyzer.analyze_performance_impact()
    reproducibility_impact = analyzer.analyze_reproducibility_impact()
    recommendations = analyzer.generate_environment_recommendations()
    
    # 输出关键发现
    print("\n" + "="*60)
    print("🎯 关键发现")
    print("="*60)
    
    env_status = "✅ 正确" if analyzer.current_env == analyzer.required_env else "⚠️ 不匹配"
    print(f"环境状态: {env_status}")
    
    if recommendations['immediate_actions']:
        print("\n🚨 立即行动项:")
        for action in recommendations['immediate_actions']:
            print(f"  - {action['action']} ({action['priority']})")
    
    print("\n💡 主要建议:")
    for practice in recommendations['best_practices']:
        print(f"  - {practice['recommendation']}")

if __name__ == "__main__":
    main()
