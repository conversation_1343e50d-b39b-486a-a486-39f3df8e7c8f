#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的副功率预测模型准确率测试脚本
分析所有相关模型，使用output_results数据进行100组随机测试
确保在lj_env_1环境下运行并统计准确率
"""

import os
import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print("="*60)
    print("🔍 环境检查")
    print("="*60)
    print(f"当前Conda环境: {conda_env}")
    print(f"Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    if conda_env != 'lj_env_1':
        print(f"⚠️ 警告：当前环境为 {conda_env}，建议使用 lj_env_1 环境")
        print("建议执行: conda activate lj_env_1")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def load_test_data():
    """加载测试数据"""
    print("\n" + "="*60)
    print("📊 加载测试数据")
    print("="*60)
    
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return None
    
    try:
        df = pd.read_csv(data_file)
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        
        # 检查必要列
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh', 'feed_type']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ 缺少必要列: {missing_cols}")
            return None
        
        # 数据清洗
        df_clean = df.dropna(subset=required_cols)
        
        # 过滤异常值
        df_filtered = df_clean[
            (df_clean['weight_difference'] > 0) &
            (df_clean['weight_difference'] < 1000) &
            (df_clean['silicon_thermal_energy_kwh'] > 0) &
            (df_clean['silicon_thermal_energy_kwh'] < 1000) &
            (df_clean['vice_total_energy_kwh'] > 0) &
            (df_clean['vice_total_energy_kwh'] < 2000)
        ]
        
        print(f"📊 清洗后数据: {df_filtered.shape}")
        
        # 随机采样100条
        if len(df_filtered) > 100:
            test_data = df_filtered.sample(n=100, random_state=42)
        else:
            test_data = df_filtered
        
        print(f"📊 测试样本: {len(test_data)} 条")
        
        # 数据统计
        print(f"\n📈 数据统计:")
        print(f"  重量差异: {test_data['weight_difference'].min():.1f} - {test_data['weight_difference'].max():.1f} kg")
        print(f"  硅热能: {test_data['silicon_thermal_energy_kwh'].min():.1f} - {test_data['silicon_thermal_energy_kwh'].max():.1f} kWh")
        print(f"  副功率: {test_data['vice_total_energy_kwh'].min():.1f} - {test_data['vice_total_energy_kwh'].max():.1f} kWh")
        print(f"  工艺类型: {test_data['feed_type'].value_counts().to_dict()}")
        
        return test_data
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def discover_models():
    """发现所有可用模型"""
    print("\n" + "="*60)
    print("🔍 发现可用模型")
    print("="*60)
    
    models = {}
    
    # 1. SVR模型 (85.4%准确率)
    svr_path = Path("副功率预测_85.4%准确率_完整项目/models")
    svr_files = {
        'model': svr_path / "best_model_svr.joblib",
        'scaler': svr_path / "scaler.joblib",
        'selector': svr_path / "feature_selector.joblib",
        'results': svr_path / "results.json"
    }
    
    if all(f.exists() for f in svr_files.values()):
        # 读取性能信息
        try:
            with open(svr_files['results'], 'r', encoding='utf-8') as f:
                results = json.load(f)
                reported_acc = results.get('best_accuracy', 85.4)
        except:
            reported_acc = 85.4
        
        models['SVR_85.4%'] = {
            'type': 'svr',
            'files': svr_files,
            'reported_accuracy': reported_acc,
            'description': f'SVR模型 (报告准确率: {reported_acc:.1f}%)'
        }
        print(f"  ✅ SVR模型: {reported_acc:.1f}%准确率")
    else:
        print(f"  ❌ SVR模型文件缺失")
    
    # 2. 生产集成模型
    prod_path = Path("production_ready_models")
    prod_model = prod_path / "ensemble_model.joblib"
    
    if prod_model.exists():
        models['Production_Ensemble'] = {
            'type': 'production',
            'files': {'model': prod_model},
            'reported_accuracy': 71.3,
            'description': '生产集成模型 (报告准确率: 71.3%)'
        }
        print(f"  ✅ 生产集成模型: 71.3%准确率")
    else:
        print(f"  ❌ 生产集成模型文件不存在")
    
    # 3. 实时集成模型
    realtime_path = Path("realtime_vice_power_models")
    realtime_model = realtime_path / "ensemble_model.joblib"
    
    if realtime_model.exists():
        models['Realtime_Ensemble'] = {
            'type': 'realtime',
            'files': {'model': realtime_model},
            'reported_accuracy': 71.3,
            'description': '实时集成模型 (报告准确率: 71.3%)'
        }
        print(f"  ✅ 实时集成模型: 71.3%准确率")
    else:
        print(f"  ❌ 实时集成模型文件不存在")
    
    print(f"\n📊 总共发现 {len(models)} 个模型")
    return models

def test_models(models, test_data):
    """测试所有模型"""
    print("\n" + "="*60)
    print("🧪 开始模型测试")
    print("="*60)
    
    results = {}
    
    # 检查是否可以导入joblib
    try:
        import joblib
        print("✅ joblib可用")
    except ImportError:
        print("❌ joblib不可用，无法加载模型")
        return {}
    
    for model_name, model_info in models.items():
        print(f"\n🔬 测试模型: {model_name}")
        
        try:
            if model_info['type'] == 'svr':
                result = test_svr_model(model_info, test_data)
            else:
                result = test_ensemble_model(model_info, test_data)
            
            results[model_name] = result
            
            if 'error' in result:
                print(f"  ❌ 测试失败: {result['error']}")
            else:
                print(f"  ✅ 测试完成")
                if 'accuracy_10kwh' in result:
                    print(f"    ±10kWh准确率: {result['accuracy_10kwh']:.2f}%")
                    print(f"    MAE: {result['mae']:.2f} kWh")
                    print(f"    测试样本: {result['sample_count']} 条")
        
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
            results[model_name] = {'error': str(e)}
    
    return results

def test_svr_model(model_info, test_data):
    """测试SVR模型"""
    try:
        import joblib
        
        # 加载模型组件
        model = joblib.load(model_info['files']['model'])
        scaler = joblib.load(model_info['files']['scaler'])
        selector = joblib.load(model_info['files']['selector'])
        
        predictions = []
        actual_values = []
        
        for _, row in test_data.iterrows():
            try:
                # 准备特征（简化版本）
                features = prepare_features_for_svr(row)
                
                # 特征选择和标准化
                features_selected = selector.transform([features])
                features_scaled = scaler.transform(features_selected)
                
                # 预测
                pred = model.predict(features_scaled)[0]
                predictions.append(pred)
                actual_values.append(row['vice_total_energy_kwh'])
                
            except Exception as e:
                continue  # 跳过失败的预测
        
        if len(predictions) == 0:
            return {'error': 'No successful predictions'}
        
        return calculate_metrics(predictions, actual_values)
        
    except Exception as e:
        return {'error': str(e)}

def test_ensemble_model(model_info, test_data):
    """测试集成模型"""
    try:
        import joblib
        
        # 加载模型
        model = joblib.load(model_info['files']['model'])
        
        predictions = []
        actual_values = []
        
        for _, row in test_data.iterrows():
            try:
                # 简化特征（基于可用数据）
                features = [
                    row['weight_difference'],
                    row['silicon_thermal_energy_kwh'],
                    1 if row['feed_type'] == '首投' else 0
                ]
                
                # 预测
                pred = model.predict([features])[0]
                predictions.append(pred)
                actual_values.append(row['vice_total_energy_kwh'])
                
            except Exception as e:
                continue  # 跳过失败的预测
        
        if len(predictions) == 0:
            return {'error': 'No successful predictions'}
        
        return calculate_metrics(predictions, actual_values)
        
    except Exception as e:
        return {'error': str(e)}

def prepare_features_for_svr(row):
    """为SVR模型准备特征"""
    # 基础特征
    base_features = [
        row.get('start_weight', 500),
        row.get('end_weight', 700),
        row.get('weight_difference', 200),
        row.get('end_temperature_celsius', 1450),
        row.get('first_crystal_seeding_main_power_kw', 60),
        row.get('feed_number_1_records', 0),
        row.get('silicon_thermal_energy_kwh', 150),
        row.get('energy_efficiency_percent', 75),
        row.get('record_count', 3000),
        row.get('duration_hours', 3)
    ]
    
    # 工程特征（简化版本）
    weight_diff = row.get('weight_difference', 200)
    silicon_energy = row.get('silicon_thermal_energy_kwh', 150)
    duration = max(row.get('duration_hours', 3), 0.1)
    
    engineered_features = [
        weight_diff ** 2,
        np.sqrt(abs(weight_diff)),
        np.log1p(abs(weight_diff)),
        silicon_energy ** 2,
        np.sqrt(abs(silicon_energy)),
        np.log1p(abs(silicon_energy)),
        duration ** 2,
        np.sqrt(abs(duration)),
        np.log1p(abs(duration)),
        weight_diff * silicon_energy,
        weight_diff * duration,
        weight_diff / duration,
        silicon_energy * duration,
        silicon_energy / duration,
        1 if row.get('feed_type') == '首投' else 0
    ]
    
    return base_features + engineered_features

def calculate_metrics(predictions, actual_values):
    """计算评估指标"""
    predictions = np.array(predictions)
    actual_values = np.array(actual_values)
    
    # 基本指标
    mae = np.mean(np.abs(predictions - actual_values))
    rmse = np.sqrt(np.mean((predictions - actual_values) ** 2))
    
    # 准确率指标
    acc_5 = np.mean(np.abs(predictions - actual_values) <= 5) * 100
    acc_10 = np.mean(np.abs(predictions - actual_values) <= 10) * 100
    acc_15 = np.mean(np.abs(predictions - actual_values) <= 15) * 100
    
    # R²分数
    ss_res = np.sum((actual_values - predictions) ** 2)
    ss_tot = np.sum((actual_values - np.mean(actual_values)) ** 2)
    r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
    
    return {
        'sample_count': len(predictions),
        'mae': float(mae),
        'rmse': float(rmse),
        'r2_score': float(r2),
        'accuracy_5kwh': float(acc_5),
        'accuracy_10kwh': float(acc_10),
        'accuracy_15kwh': float(acc_15),
        'prediction_stats': {
            'min': float(np.min(predictions)),
            'max': float(np.max(predictions)),
            'mean': float(np.mean(predictions)),
            'std': float(np.std(predictions))
        },
        'actual_stats': {
            'min': float(np.min(actual_values)),
            'max': float(np.max(actual_values)),
            'mean': float(np.mean(actual_values)),
            'std': float(np.std(actual_values))
        }
    }

def main():
    """主函数"""
    print("="*60)
    print("🧪 副功率预测模型完整准确率测试")
    print("="*60)
    print("分析所有相关模型，使用output_results数据随机测试100组")
    
    # 1. 环境检查
    env_ok = check_environment()
    
    # 2. 加载测试数据
    test_data = load_test_data()
    if test_data is None:
        print("\n❌ 测试数据加载失败，测试终止")
        return
    
    # 3. 发现模型
    models = discover_models()
    if not models:
        print("\n❌ 未发现可用模型，测试终止")
        return
    
    # 4. 测试模型
    test_results = test_models(models, test_data)
    
    # 5. 结果汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    print(f"环境状态: {'✅ lj_env_1' if env_ok else '⚠️ 非lj_env_1'}")
    print(f"测试数据: {len(test_data)} 条样本")
    print(f"测试模型: {len(models)} 个")
    
    print(f"\n🎯 准确率统计:")
    for model_name, result in test_results.items():
        if 'error' in result:
            print(f"  ❌ {model_name}: 测试失败 ({result['error']})")
        else:
            reported_acc = models[model_name]['reported_accuracy']
            actual_acc = result.get('accuracy_10kwh', 0)
            print(f"  📊 {model_name}:")
            print(f"    报告准确率: {reported_acc:.1f}%")
            print(f"    实测准确率: {actual_acc:.2f}% (±10kWh)")
            print(f"    MAE: {result['mae']:.2f} kWh")
            print(f"    RMSE: {result['rmse']:.2f} kWh")
            print(f"    R²: {result['r2_score']:.4f}")
            print(f"    测试样本: {result['sample_count']} 条")
    
    # 6. 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    final_report = {
        'test_timestamp': timestamp,
        'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
        'environment_ok': env_ok,
        'test_data_size': len(test_data),
        'models_tested': {name: info['description'] for name, info in models.items()},
        'test_results': test_results,
        'summary': {
            'total_models': len(models),
            'successful_tests': len([r for r in test_results.values() if 'error' not in r]),
            'failed_tests': len([r for r in test_results.values() if 'error' in r])
        }
    }
    
    report_file = f"complete_model_test_results_{timestamp}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(final_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细结果已保存到: {report_file}")
    print("✅ 模型测试完成")

if __name__ == "__main__":
    main()
