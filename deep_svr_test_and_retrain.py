#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度测试SVR模型准确率并在lj_env_1环境中重新训练
保存到新的文件夹中
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import joblib
from pathlib import Path
from datetime import datetime
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class EnvironmentValidator:
    """环境验证器"""
    
    @staticmethod
    def validate_lj_env_1():
        """验证是否在lj_env_1环境中"""
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        print("="*60)
        print("🔍 环境验证")
        print("="*60)
        print(f"当前Conda环境: {conda_env}")
        print(f"Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        
        if conda_env != 'lj_env_1':
            print(f"❌ 错误：当前环境为 {conda_env}")
            print("必须在 lj_env_1 环境中运行此脚本")
            print("请执行: conda activate lj_env_1")
            return False
        
        print("✅ 环境验证通过：lj_env_1")
        return True

class SVRModelTester:
    """SVR模型深度测试器"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.selector = None
        self.test_results = {}
        
    def load_existing_svr_model(self):
        """加载现有的SVR模型"""
        print("\n" + "="*60)
        print("📥 加载现有SVR模型")
        print("="*60)
        
        model_path = Path("副功率预测_85.4%准确率_完整项目/models")
        
        model_files = {
            'model': model_path / "best_model_svr.joblib",
            'scaler': model_path / "scaler.joblib",
            'selector': model_path / "feature_selector.joblib",
            'results': model_path / "results.json"
        }
        
        # 检查文件存在性
        missing_files = [name for name, file in model_files.items() if not file.exists()]
        if missing_files:
            print(f"❌ 缺少文件: {missing_files}")
            return False
        
        try:
            # 加载模型组件
            self.model = joblib.load(model_files['model'])
            self.scaler = joblib.load(model_files['scaler'])
            self.selector = joblib.load(model_files['selector'])
            
            # 读取性能信息
            with open(model_files['results'], 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            print(f"✅ SVR模型加载成功")
            print(f"📊 模型类型: {type(self.model)}")
            print(f"📊 报告准确率: {results.get('best_accuracy', 'unknown')}%")
            print(f"📊 特征选择器: {self.selector.k} 个特征")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def prepare_advanced_features(self, df):
        """准备高级特征工程"""
        print("\n🔧 高级特征工程...")
        
        # 基础特征
        base_features = [
            'start_weight', 'end_weight', 'weight_difference',
            'end_temperature_celsius', 'first_crystal_seeding_main_power_kw',
            'feed_number_1_records', 'silicon_thermal_energy_kwh',
            'energy_efficiency_percent', 'record_count', 'duration_hours'
        ]
        
        # 确保基础特征存在
        for col in base_features:
            if col not in df.columns:
                print(f"⚠️ 缺少特征: {col}，使用默认值")
                if col == 'start_weight':
                    df[col] = 500
                elif col == 'end_weight':
                    df[col] = df.get('start_weight', 500) + df.get('weight_difference', 200)
                elif col == 'end_temperature_celsius':
                    df[col] = 1450
                elif col == 'first_crystal_seeding_main_power_kw':
                    df[col] = 60
                elif col == 'feed_number_1_records':
                    df[col] = 0
                elif col == 'energy_efficiency_percent':
                    df[col] = 75
                elif col == 'record_count':
                    df[col] = 3000
                elif col == 'duration_hours':
                    df[col] = 3
        
        # 创建特征矩阵
        features_list = []
        
        for _, row in df.iterrows():
            # 基础特征
            base_vals = [row.get(col, 0) for col in base_features]
            
            # 工程特征
            weight_diff = row.get('weight_difference', 200)
            silicon_energy = row.get('silicon_thermal_energy_kwh', 150)
            duration = max(row.get('duration_hours', 3), 0.1)
            
            engineered_features = [
                weight_diff ** 2,  # weight_difference_squared
                np.sqrt(abs(weight_diff)),  # weight_difference_sqrt
                np.log1p(abs(weight_diff)),  # weight_difference_log
                silicon_energy ** 2,  # silicon_thermal_energy_kwh_squared
                np.sqrt(abs(silicon_energy)),  # silicon_thermal_energy_kwh_sqrt
                np.log1p(abs(silicon_energy)),  # silicon_thermal_energy_kwh_log
                duration ** 2,  # duration_hours_squared
                np.sqrt(abs(duration)),  # duration_hours_sqrt
                np.log1p(abs(duration)),  # duration_hours_log
                weight_diff * silicon_energy,  # interaction features
                weight_diff * duration,
                weight_diff / duration,
                silicon_energy * duration,
                silicon_energy / duration,
                1 if row.get('feed_type') == '首投' else 0,  # device_frequency
                # 额外的工程特征
                row.get('start_weight', 500) / max(duration, 0.1),  # weight rate
                row.get('end_temperature_celsius', 1450) / 1000,  # normalized temp
                row.get('first_crystal_seeding_main_power_kw', 60) * duration,  # power * time
                row.get('energy_efficiency_percent', 75) / 100,  # normalized efficiency
                row.get('record_count', 3000) / 1000  # normalized record count
            ]
            
            # 组合所有特征
            all_features = base_vals + engineered_features
            features_list.append(all_features)
        
        feature_matrix = np.array(features_list)
        print(f"✅ 特征工程完成: {feature_matrix.shape}")
        
        return feature_matrix
    
    def deep_test_existing_model(self, test_data):
        """深度测试现有模型"""
        print("\n" + "="*60)
        print("🧪 深度测试现有SVR模型")
        print("="*60)
        
        if self.model is None:
            print("❌ 模型未加载")
            return None
        
        # 准备特征
        X_test = self.prepare_advanced_features(test_data)
        y_test = test_data['vice_total_energy_kwh'].values
        
        predictions = []
        actual_values = []
        failed_predictions = 0
        
        print(f"📊 开始预测 {len(test_data)} 个样本...")
        
        for i in range(len(X_test)):
            try:
                # 特征选择和标准化
                features_selected = self.selector.transform([X_test[i]])
                features_scaled = self.scaler.transform(features_selected)
                
                # 预测
                pred = self.model.predict(features_scaled)[0]
                predictions.append(pred)
                actual_values.append(y_test[i])
                
            except Exception as e:
                failed_predictions += 1
                continue
        
        if len(predictions) == 0:
            print("❌ 所有预测都失败了")
            return None
        
        # 计算评估指标
        predictions = np.array(predictions)
        actual_values = np.array(actual_values)
        
        mae = mean_absolute_error(actual_values, predictions)
        rmse = np.sqrt(mean_squared_error(actual_values, predictions))
        r2 = r2_score(actual_values, predictions)
        
        # 准确率指标
        errors = np.abs(predictions - actual_values)
        acc_5 = (errors <= 5).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        results = {
            'successful_predictions': len(predictions),
            'failed_predictions': failed_predictions,
            'mae': float(mae),
            'rmse': float(rmse),
            'r2_score': float(r2),
            'accuracy_5kwh': float(acc_5),
            'accuracy_10kwh': float(acc_10),
            'accuracy_15kwh': float(acc_15),
            'prediction_stats': {
                'min': float(np.min(predictions)),
                'max': float(np.max(predictions)),
                'mean': float(np.mean(predictions)),
                'std': float(np.std(predictions))
            },
            'actual_stats': {
                'min': float(np.min(actual_values)),
                'max': float(np.max(actual_values)),
                'mean': float(np.mean(actual_values)),
                'std': float(np.std(actual_values))
            }
        }
        
        print(f"\n📊 深度测试结果:")
        print(f"  成功预测: {results['successful_predictions']} / {len(test_data)}")
        print(f"  失败预测: {results['failed_predictions']}")
        print(f"  ±5kWh准确率: {results['accuracy_5kwh']:.2f}%")
        print(f"  ±10kWh准确率: {results['accuracy_10kwh']:.2f}%")
        print(f"  ±15kWh准确率: {results['accuracy_15kwh']:.2f}%")
        print(f"  MAE: {results['mae']:.2f} kWh")
        print(f"  RMSE: {results['rmse']:.2f} kWh")
        print(f"  R²: {results['r2_score']:.4f}")
        
        self.test_results = results
        return results

class SVRModelTrainer:
    """SVR模型训练器"""
    
    def __init__(self):
        self.best_model = None
        self.best_scaler = None
        self.best_selector = None
        self.training_results = {}
        
    def load_training_data(self):
        """加载训练数据"""
        print("\n" + "="*60)
        print("📊 加载训练数据")
        print("="*60)
        
        data_file = Path("output_results/A01_A40_cycles__analysis.csv")
        
        if not data_file.exists():
            print(f"❌ 数据文件不存在: {data_file}")
            return None
        
        try:
            df = pd.read_csv(data_file)
            print(f"✅ 数据加载成功: {df.shape}")
            
            # 数据清洗
            required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
            df_clean = df.dropna(subset=required_cols)
            
            # 过滤异常值
            df_filtered = df_clean[
                (df_clean['weight_difference'] > 0) &
                (df_clean['weight_difference'] < 1000) &
                (df_clean['silicon_thermal_energy_kwh'] > 0) &
                (df_clean['silicon_thermal_energy_kwh'] < 1000) &
                (df_clean['vice_total_energy_kwh'] > 0) &
                (df_clean['vice_total_energy_kwh'] < 2000)
            ]
            
            print(f"📊 清洗后数据: {df_filtered.shape}")
            
            return df_filtered
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None
    
    def train_optimized_svr(self, df):
        """训练优化的SVR模型"""
        print("\n" + "="*60)
        print("🚀 训练优化的SVR模型")
        print("="*60)
        
        # 特征工程
        tester = SVRModelTester()
        X = tester.prepare_advanced_features(df)
        y = df['vice_total_energy_kwh'].values
        
        print(f"📊 特征矩阵: {X.shape}")
        print(f"📊 目标变量范围: {y.min():.1f} - {y.max():.1f} kWh")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=True
        )
        
        print(f"📊 训练集: {X_train.shape}")
        print(f"📊 测试集: {X_test.shape}")
        
        # 特征选择
        print(f"\n🔧 特征选择...")
        selector = SelectKBest(score_func=f_regression, k=20)
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        print(f"✅ 选择了 {X_train_selected.shape[1]} 个最佳特征")
        
        # 数据标准化
        print(f"\n🔧 数据标准化...")
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        # SVR参数网格搜索
        print(f"\n🔍 SVR参数优化...")
        param_grid = {
            'C': [10, 50, 100, 200],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1],
            'epsilon': [0.01, 0.1, 0.2, 0.5]
        }
        
        svr_base = SVR(kernel='rbf')
        grid_search = GridSearchCV(
            svr_base, param_grid, 
            cv=5, scoring='neg_mean_absolute_error',
            n_jobs=-1, verbose=1
        )
        
        grid_search.fit(X_train_scaled, y_train)
        
        print(f"✅ 最佳参数: {grid_search.best_params_}")
        print(f"✅ 最佳CV分数: {-grid_search.best_score_:.2f}")
        
        # 使用最佳模型预测
        best_model = grid_search.best_estimator_
        y_pred = best_model.predict(X_test_scaled)
        
        # 评估结果
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        errors = np.abs(y_pred - y_test)
        acc_5 = (errors <= 5).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        results = {
            'best_params': grid_search.best_params_,
            'cv_score': float(-grid_search.best_score_),
            'test_mae': float(mae),
            'test_rmse': float(rmse),
            'test_r2': float(r2),
            'test_accuracy_5kwh': float(acc_5),
            'test_accuracy_10kwh': float(acc_10),
            'test_accuracy_15kwh': float(acc_15),
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'selected_features': int(X_train_selected.shape[1])
        }
        
        print(f"\n📊 训练结果:")
        print(f"  ±5kWh准确率: {results['test_accuracy_5kwh']:.2f}%")
        print(f"  ±10kWh准确率: {results['test_accuracy_10kwh']:.2f}%")
        print(f"  ±15kWh准确率: {results['test_accuracy_15kwh']:.2f}%")
        print(f"  MAE: {results['test_mae']:.2f} kWh")
        print(f"  RMSE: {results['test_rmse']:.2f} kWh")
        print(f"  R²: {results['test_r2']:.4f}")
        
        # 保存最佳模型
        self.best_model = best_model
        self.best_scaler = scaler
        self.best_selector = selector
        self.training_results = results
        
        return results
    
    def save_retrained_model(self):
        """保存重新训练的模型"""
        print("\n" + "="*60)
        print("💾 保存重新训练的SVR模型")
        print("="*60)
        
        if self.best_model is None:
            print("❌ 没有训练好的模型可保存")
            return None
        
        # 创建新的模型目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path(f"retrained_svr_model_lj_env_1_{timestamp}")
        model_dir.mkdir(exist_ok=True)
        
        try:
            # 保存模型组件
            joblib.dump(self.best_model, model_dir / "best_model_svr.joblib")
            joblib.dump(self.best_scaler, model_dir / "scaler.joblib")
            joblib.dump(self.best_selector, model_dir / "feature_selector.joblib")
            
            # 保存训练结果
            training_info = {
                'timestamp': timestamp,
                'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'model_type': 'SVR',
                'training_results': self.training_results,
                'model_files': {
                    'model': 'best_model_svr.joblib',
                    'scaler': 'scaler.joblib',
                    'selector': 'feature_selector.joblib'
                }
            }
            
            with open(model_dir / "training_results.json", 'w', encoding='utf-8') as f:
                json.dump(training_info, f, indent=2, ensure_ascii=False)
            
            # 创建README文件
            readme_content = f"""# 重新训练的SVR模型

## 模型信息
- 训练时间: {timestamp}
- 训练环境: {os.environ.get('CONDA_DEFAULT_ENV', 'unknown')}
- Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}

## 性能指标
- ±10kWh准确率: {self.training_results['test_accuracy_10kwh']:.2f}%
- MAE: {self.training_results['test_mae']:.2f} kWh
- RMSE: {self.training_results['test_rmse']:.2f} kWh
- R²: {self.training_results['test_r2']:.4f}

## 模型参数
{self.training_results['best_params']}

## 文件说明
- best_model_svr.joblib: 训练好的SVR模型
- scaler.joblib: 数据标准化器
- feature_selector.joblib: 特征选择器
- training_results.json: 详细训练结果
- README.md: 本说明文件

## 使用方法
```python
import joblib
model = joblib.load('best_model_svr.joblib')
scaler = joblib.load('scaler.joblib')
selector = joblib.load('feature_selector.joblib')
```
"""
            
            with open(model_dir / "README.md", 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            print(f"✅ 模型保存成功")
            print(f"📁 保存位置: {model_dir}")
            print(f"📊 ±10kWh准确率: {self.training_results['test_accuracy_10kwh']:.2f}%")
            
            return model_dir
            
        except Exception as e:
            print(f"❌ 模型保存失败: {e}")
            return None

def main():
    """主函数"""
    print("="*60)
    print("🧪 SVR模型深度测试和重新训练")
    print("="*60)
    print("在lj_env_1环境中深度测试现有模型并重新训练")
    
    # 1. 环境验证
    if not EnvironmentValidator.validate_lj_env_1():
        print("\n❌ 环境验证失败，程序终止")
        return
    
    # 2. 加载测试数据
    trainer = SVRModelTrainer()
    df = trainer.load_training_data()
    if df is None:
        print("\n❌ 数据加载失败，程序终止")
        return
    
    # 随机选择100组数据进行测试
    if len(df) > 100:
        test_data = df.sample(n=100, random_state=42)
    else:
        test_data = df
    
    print(f"\n📊 使用 {len(test_data)} 条数据进行测试")
    
    # 3. 深度测试现有模型
    tester = SVRModelTester()
    if tester.load_existing_svr_model():
        print("\n🧪 开始深度测试现有SVR模型...")
        existing_results = tester.deep_test_existing_model(test_data)
    else:
        print("\n⚠️ 无法加载现有模型，跳过测试")
        existing_results = None
    
    # 4. 重新训练模型
    print("\n🚀 开始重新训练SVR模型...")
    training_results = trainer.train_optimized_svr(df)
    
    # 5. 保存新模型
    model_dir = trainer.save_retrained_model()
    
    # 6. 结果对比
    print("\n" + "="*60)
    print("📊 结果对比")
    print("="*60)
    
    if existing_results:
        print(f"现有模型测试结果:")
        print(f"  ±10kWh准确率: {existing_results['accuracy_10kwh']:.2f}%")
        print(f"  MAE: {existing_results['mae']:.2f} kWh")
        print(f"  成功预测: {existing_results['successful_predictions']} / {len(test_data)}")
    
    print(f"\n重新训练模型结果:")
    print(f"  ±10kWh准确率: {training_results['test_accuracy_10kwh']:.2f}%")
    print(f"  MAE: {training_results['test_mae']:.2f} kWh")
    print(f"  训练样本: {training_results['training_samples']}")
    print(f"  测试样本: {training_results['test_samples']}")
    
    if existing_results and model_dir:
        improvement = training_results['test_accuracy_10kwh'] - existing_results['accuracy_10kwh']
        print(f"\n📈 性能改进: {improvement:+.2f}%")
    
    if model_dir:
        print(f"\n✅ 新模型已保存到: {model_dir}")
        print("🎯 深度测试和重新训练完成！")
    
    return model_dir

if __name__ == "__main__":
    result = main()
