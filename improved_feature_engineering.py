#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的特征工程 - 基于数据挖掘结果设计
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class ImprovedFeatureEngineer:
    """改进的特征工程器"""
    
    def __init__(self):
        """初始化"""
        self.scaler = RobustScaler()  # 使用RobustScaler对异常值更鲁棒
        self.feature_selector = None
        self.selected_features = None
        
    def create_physics_based_features(self, df):
        """创建基于物理学的特征"""
        features_df = df.copy()
        
        # 1. 基础物理特征
        features_df['energy_density'] = features_df['silicon_thermal_energy_kwh'] / (features_df['weight_difference'] + 1e-6)
        features_df['energy_weight_ratio'] = features_df['silicon_thermal_energy_kwh'] / (features_df['weight_difference'] + 1e-6)
        features_df['thermal_efficiency'] = features_df['vice_total_energy_kwh'] / (features_df['silicon_thermal_energy_kwh'] + 1e-6)
        
        # 2. 温度相关特征
        features_df['temp_deviation'] = abs(features_df['end_temperature_celsius'] - 1448)
        features_df['temp_efficiency'] = features_df['vice_total_energy_kwh'] / (features_df['end_temperature_celsius'] + 1e-6)
        
        # 3. 功率相关特征
        features_df['power_density'] = features_df['first_crystal_seeding_main_power_kw'] / (features_df['weight_difference'] + 1e-6)
        features_df['power_time_product'] = features_df['first_crystal_seeding_main_power_kw'] * features_df['duration_hours']
        
        # 4. 时间相关特征
        features_df['energy_per_hour'] = features_df['vice_total_energy_kwh'] / (features_df['duration_hours'] + 1e-6)
        features_df['weight_per_hour'] = features_df['weight_difference'] / (features_df['duration_hours'] + 1e-6)
        
        return features_df
    
    def create_process_specific_features(self, df):
        """创建工艺特定特征"""
        features_df = df.copy()
        
        # 按工艺类型创建特征
        features_df['is_first_cast'] = (features_df['feed_type'] == '首投').astype(int)
        
        # 基于聚类分析的特征
        # 根据数据挖掘结果，创建聚类相关特征
        
        # 聚类0特征 (高效率，中等重量)
        cluster0_condition = (
            (features_df['weight_difference'] > 200) & 
            (features_df['weight_difference'] < 500) & 
            (features_df['thermal_efficiency'] > 1.2) & 
            (features_df['thermal_efficiency'] < 1.6)
        )
        features_df['is_cluster0_pattern'] = cluster0_condition.astype(int)
        
        # 聚类1特征 (低重量，高效率)
        cluster1_condition = (
            (features_df['weight_difference'] < 100) & 
            (features_df['thermal_efficiency'] > 2.5)
        )
        features_df['is_cluster1_pattern'] = cluster1_condition.astype(int)
        
        # 聚类3特征 (高重量，低效率)
        cluster3_condition = (
            (features_df['weight_difference'] > 500) & 
            (features_df['thermal_efficiency'] < 1.5)
        )
        features_df['is_cluster3_pattern'] = cluster3_condition.astype(int)
        
        return features_df
    
    def create_interaction_features(self, df):
        """创建交互特征"""
        features_df = df.copy()
        
        # 基于相关性分析的交互特征
        # weight_difference 和 silicon_thermal_energy_kwh 高度相关，创建比值特征
        features_df['weight_energy_balance'] = abs(features_df['weight_difference'] - features_df['silicon_thermal_energy_kwh'])
        features_df['weight_energy_harmonic_mean'] = 2 * features_df['weight_difference'] * features_df['silicon_thermal_energy_kwh'] / (features_df['weight_difference'] + features_df['silicon_thermal_energy_kwh'] + 1e-6)
        
        # 温度和重量的交互
        features_df['temp_weight_interaction'] = features_df['end_temperature_celsius'] * features_df['weight_difference'] / 1000
        
        # 功率和时间的交互
        features_df['power_duration_efficiency'] = features_df['first_crystal_seeding_main_power_kw'] * features_df['duration_hours'] / (features_df['weight_difference'] + 1e-6)
        
        return features_df
    
    def create_range_based_features(self, df):
        """基于范围分析创建特征"""
        features_df = df.copy()
        
        # 基于误差分析，为不同范围创建特征
        
        # 重量范围特征
        features_df['is_light_weight'] = (features_df['weight_difference'] < 100).astype(int)
        features_df['is_medium_weight'] = ((features_df['weight_difference'] >= 100) & (features_df['weight_difference'] < 300)).astype(int)
        features_df['is_heavy_weight'] = ((features_df['weight_difference'] >= 300) & (features_df['weight_difference'] < 500)).astype(int)
        features_df['is_super_heavy'] = (features_df['weight_difference'] >= 500).astype(int)
        
        # 副功率范围特征
        features_df['is_low_power'] = (features_df['vice_total_energy_kwh'] < 200).astype(int)
        features_df['is_medium_power'] = ((features_df['vice_total_energy_kwh'] >= 200) & (features_df['vice_total_energy_kwh'] < 400)).astype(int)
        features_df['is_high_power'] = ((features_df['vice_total_energy_kwh'] >= 400) & (features_df['vice_total_energy_kwh'] < 600)).astype(int)
        features_df['is_super_high_power'] = (features_df['vice_total_energy_kwh'] >= 600).astype(int)
        
        # 效率范围特征
        features_df['is_high_efficiency'] = (features_df['thermal_efficiency'] > 2.0).astype(int)
        features_df['is_normal_efficiency'] = ((features_df['thermal_efficiency'] >= 1.0) & (features_df['thermal_efficiency'] <= 2.0)).astype(int)
        features_df['is_low_efficiency'] = (features_df['thermal_efficiency'] < 1.0).astype(int)
        
        return features_df
    
    def create_bias_correction_features(self, df):
        """创建偏差校正特征"""
        features_df = df.copy()
        
        # 基于系统性偏差分析创建校正特征
        
        # 工艺类型偏差校正
        features_df['first_cast_bias_correction'] = features_df['is_first_cast'] * 10.88  # 首投偏差校正
        features_df['recast_bias_correction'] = (1 - features_df['is_first_cast']) * 33.74  # 复投偏差校正
        
        # 功率范围偏差校正
        features_df['low_power_bias_correction'] = features_df['is_low_power'] * 31.52
        features_df['medium_power_bias_correction'] = features_df['is_medium_power'] * 44.83
        features_df['high_power_bias_correction'] = features_df['is_high_power'] * 33.47
        
        return features_df
    
    def create_all_features(self, df):
        """创建所有改进特征"""
        print("创建改进的特征集...")
        
        # 1. 物理特征
        features_df = self.create_physics_based_features(df)
        
        # 2. 工艺特定特征
        features_df = self.create_process_specific_features(features_df)
        
        # 3. 交互特征
        features_df = self.create_interaction_features(features_df)
        
        # 4. 范围特征
        features_df = self.create_range_based_features(features_df)
        
        # 5. 偏差校正特征
        features_df = self.create_bias_correction_features(features_df)
        
        print(f"特征创建完成，总特征数: {features_df.shape[1]}")
        return features_df
    
    def select_best_features(self, X, y, k=20):
        """选择最佳特征"""
        print(f"从 {X.shape[1]} 个特征中选择最佳 {k} 个特征...")
        
        # 移除非数值列
        numeric_columns = X.select_dtypes(include=[np.number]).columns
        X_numeric = X[numeric_columns]
        
        # 填充缺失值
        X_numeric = X_numeric.fillna(0)
        
        # 使用多种特征选择方法
        
        # 1. 基于F统计量
        f_selector = SelectKBest(score_func=f_regression, k=k)
        f_selector.fit(X_numeric, y)
        f_selected = X_numeric.columns[f_selector.get_support()]
        
        # 2. 基于互信息
        mi_scores = mutual_info_regression(X_numeric, y, random_state=42)
        mi_selected = X_numeric.columns[np.argsort(mi_scores)[-k:]]
        
        # 3. 基于随机森林重要性
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X_numeric, y)
        rf_selected = X_numeric.columns[np.argsort(rf.feature_importances_)[-k:]]
        
        # 合并选择的特征
        selected_features = list(set(f_selected) | set(mi_selected) | set(rf_selected))
        
        print(f"特征选择完成，选择了 {len(selected_features)} 个特征")
        print("选择的特征:", selected_features[:10], "...")
        
        self.selected_features = selected_features
        return selected_features
    
    def prepare_training_data(self, df, target_col='vice_total_energy_kwh'):
        """准备训练数据"""
        print("准备训练数据...")
        
        # 创建所有特征
        features_df = self.create_all_features(df)
        
        # 选择目标变量
        y = features_df[target_col]
        
        # 移除目标变量和非特征列
        exclude_cols = [target_col, 'file_path', 'file_name', 'folder_name', 'start_time', 'end_time', 'feed_type']
        feature_cols = [col for col in features_df.columns if col not in exclude_cols]
        X = features_df[feature_cols]
        
        # 特征选择
        selected_features = self.select_best_features(X, y, k=25)
        X_selected = X[selected_features]
        
        # 标准化
        X_scaled = self.scaler.fit_transform(X_selected.fillna(0))
        X_scaled = pd.DataFrame(X_scaled, columns=selected_features, index=X_selected.index)
        
        print(f"训练数据准备完成: {X_scaled.shape}")
        return X_scaled, y, selected_features

def test_improved_features():
    """测试改进的特征工程"""
    print("测试改进的特征工程...")
    
    # 加载数据
    df = pd.read_csv('output_results/A01_A40_cycles__analysis.csv')
    
    # 初始化特征工程器
    engineer = ImprovedFeatureEngineer()
    
    # 准备训练数据
    X, y, selected_features = engineer.prepare_training_data(df)
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # 训练简单模型测试
    rf = RandomForestRegressor(n_estimators=100, random_state=42)
    rf.fit(X_train, y_train)
    
    # 预测
    y_pred = rf.predict(X_test)
    
    # 评估
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    # 计算准确率
    errors = np.abs(y_pred - y_test)
    accuracy_5 = (errors <= 5).mean() * 100
    accuracy_10 = (errors <= 10).mean() * 100
    accuracy_15 = (errors <= 15).mean() * 100
    
    print(f"\n改进特征工程测试结果:")
    print(f"MAE: {mae:.2f} kWh")
    print(f"R²: {r2:.4f}")
    print(f"±5 kWh 准确率: {accuracy_5:.1f}%")
    print(f"±10 kWh 准确率: {accuracy_10:.1f}%")
    print(f"±15 kWh 准确率: {accuracy_15:.1f}%")
    
    return engineer, X, y, selected_features

if __name__ == "__main__":
    engineer, X, y, features = test_improved_features()
