#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
lj_env_1环境自动设置脚本
用于副功率预测项目的环境配置
"""

import os
import sys
import subprocess
import json
from pathlib import Path

class LjEnv1Setup:
    """lj_env_1环境设置器"""
    
    def __init__(self):
        self.env_name = "lj_env_1"
        self.python_version = "3.8"
        self.required_packages = [
            "pandas==2.0.3",
            "numpy==1.24.3", 
            "scikit-learn==1.3.0",
            "joblib==1.3.2",
            "xgboost==1.7.6",
            "lightgbm==4.0.0"
        ]
    
    def check_conda_available(self):
        """检查conda是否可用"""
        try:
            result = subprocess.run(['conda', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Conda可用: {result.stdout.strip()}")
                return True
            else:
                print("❌ Conda不可用")
                return False
        except FileNotFoundError:
            print("❌ 未找到conda命令")
            return False
    
    def check_environment_exists(self):
        """检查lj_env_1环境是否存在"""
        try:
            result = subprocess.run(['conda', 'env', 'list'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                env_exists = self.env_name in result.stdout
                if env_exists:
                    print(f"✅ 环境 {self.env_name} 已存在")
                else:
                    print(f"⚠️  环境 {self.env_name} 不存在")
                return env_exists
            else:
                print("❌ 无法检查环境列表")
                return False
        except Exception as e:
            print(f"❌ 检查环境时出错: {e}")
            return False
    
    def create_environment(self):
        """创建lj_env_1环境"""
        print(f"🔧 创建环境 {self.env_name}...")
        
        try:
            # 创建环境
            cmd = ['conda', 'create', '-n', self.env_name, 
                   f'python={self.python_version}', '-y']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ 环境 {self.env_name} 创建成功")
                return True
            else:
                print(f"❌ 环境创建失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 创建环境时出错: {e}")
            return False
    
    def install_packages(self):
        """安装必要的包"""
        print("📦 安装依赖包...")
        
        # 构建pip安装命令
        pip_cmd = [
            'conda', 'run', '-n', self.env_name, 
            'pip', 'install'
        ] + self.required_packages
        
        try:
            result = subprocess.run(pip_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 依赖包安装成功")
                return True
            else:
                print(f"❌ 包安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 安装包时出错: {e}")
            return False
    
    def verify_installation(self):
        """验证安装"""
        print("🔍 验证安装...")
        
        # 检查Python版本
        try:
            cmd = ['conda', 'run', '-n', self.env_name, 'python', '--version']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Python版本: {result.stdout.strip()}")
            else:
                print(f"❌ 无法检查Python版本")
                return False
        except Exception as e:
            print(f"❌ 检查Python版本时出错: {e}")
            return False
        
        # 检查关键包
        packages_to_check = ['pandas', 'numpy', 'sklearn', 'joblib']
        
        for package in packages_to_check:
            try:
                if package == 'sklearn':
                    import_name = 'sklearn'
                else:
                    import_name = package
                
                cmd = ['conda', 'run', '-n', self.env_name, 'python', '-c', 
                       f'import {import_name}; print("{package}:", {import_name}.__version__)']
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"✅ {result.stdout.strip()}")
                else:
                    print(f"❌ 无法导入 {package}")
                    return False
                    
            except Exception as e:
                print(f"❌ 检查 {package} 时出错: {e}")
                return False
        
        return True
    
    def setup_environment(self):
        """完整的环境设置流程"""
        print("="*60)
        print("🚀 lj_env_1环境自动设置")
        print("="*60)
        
        # 1. 检查conda
        if not self.check_conda_available():
            print("\n❌ 设置失败：需要先安装Anaconda或Miniconda")
            return False
        
        # 2. 检查环境是否存在
        env_exists = self.check_environment_exists()
        
        # 3. 如果不存在，创建环境
        if not env_exists:
            if not self.create_environment():
                print("\n❌ 设置失败：无法创建环境")
                return False
        
        # 4. 安装包
        if not self.install_packages():
            print("\n❌ 设置失败：无法安装依赖包")
            return False
        
        # 5. 验证安装
        if not self.verify_installation():
            print("\n❌ 设置失败：验证安装失败")
            return False
        
        # 6. 成功完成
        print("\n" + "="*60)
        print("🎉 lj_env_1环境设置成功！")
        print("="*60)
        print("\n📋 下一步操作：")
        print("1. 激活环境: conda activate lj_env_1")
        print("2. 验证环境: python quick_env_check.py")
        print("3. 运行训练: python realtime_model_training.py")
        
        return True
    
    def generate_activation_script(self):
        """生成激活脚本"""
        script_content = f"""@echo off
REM lj_env_1环境激活脚本
echo 激活 {self.env_name} 环境...
conda activate {self.env_name}
echo 环境已激活！
echo.
echo 可用命令：
echo   python quick_env_check.py     - 检查环境
echo   python realtime_model_training.py - 运行训练
echo.
cmd /k
"""
        
        with open('activate_lj_env_1.bat', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print("📄 已生成激活脚本: activate_lj_env_1.bat")

def main():
    """主函数"""
    setup = LjEnv1Setup()
    
    # 执行设置
    success = setup.setup_environment()
    
    if success:
        # 生成激活脚本
        setup.generate_activation_script()
        
        print("\n🎯 环境设置完成！")
        print("现在可以使用lj_env_1环境进行副功率预测模型训练了。")
    else:
        print("\n💥 环境设置失败！")
        print("请检查错误信息并手动解决问题。")

if __name__ == "__main__":
    main()
