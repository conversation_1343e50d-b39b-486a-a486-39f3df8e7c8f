#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本特征工程器 - 独立实现
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

class V7FeatureEngineer:
    """v7版本特征工程器"""
    
    def __init__(self):
        """初始化"""
        self.scaler = StandardScaler()
        self.feature_names = None
        
    def create_realtime_features(self, df):
        """创建实时可获取的特征（26个特征）"""
        features_df = df.copy()
        
        # 1. 基础实时特征（3个）
        features_df['weight_diff'] = features_df['weight_difference']
        features_df['silicon_energy'] = features_df['silicon_thermal_energy_kwh']
        features_df['is_first_cast'] = (features_df['feed_type'] == '首投').astype(int)
        
        # 2. 物理学衍生特征（3个）
        features_df['energy_density'] = features_df['silicon_energy'] / (features_df['weight_diff'] + 1e-6)
        features_df['melting_energy_ratio'] = features_df['silicon_energy'] / (features_df['weight_diff'] * 0.5 + 1e-6)
        features_df['thermal_efficiency_est'] = np.clip(1.2 + 0.3 * features_df['is_first_cast'], 1.0, 2.5)
        
        # 3. 工艺特定特征（4个）
        features_df['first_cast_weight_factor'] = features_df['is_first_cast'] * features_df['weight_diff']
        features_df['first_cast_energy_factor'] = features_df['is_first_cast'] * features_df['silicon_energy']
        features_df['recast_weight_factor'] = (1 - features_df['is_first_cast']) * features_df['weight_diff']
        features_df['recast_energy_factor'] = (1 - features_df['is_first_cast']) * features_df['silicon_energy']
        
        # 4. 范围和分类特征（2个）
        features_df['weight_category'] = pd.cut(features_df['weight_diff'], 
                                               bins=[0, 100, 200, 350, 500, 1000], 
                                               labels=[1, 2, 3, 4, 5]).astype(float)
        features_df['energy_category'] = pd.cut(features_df['silicon_energy'], 
                                               bins=[0, 150, 300, 450, 600, 1000], 
                                               labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 5. 交互特征（3个）
        features_df['weight_energy_balance'] = abs(features_df['weight_diff'] - features_df['silicon_energy'])
        features_df['process_weight_energy'] = features_df['is_first_cast'] * features_df['weight_diff'] * features_df['silicon_energy'] / 10000
        features_df['weight_energy_interaction'] = features_df['weight_diff'] * features_df['silicon_energy'] / 1000
        
        # 6. 非线性变换特征（4个）
        features_df['log_weight'] = np.log(features_df['weight_diff'] + 1)
        features_df['log_energy'] = np.log(features_df['silicon_energy'] + 1)
        features_df['sqrt_weight'] = np.sqrt(features_df['weight_diff'])
        features_df['sqrt_energy'] = np.sqrt(features_df['silicon_energy'])
        
        # 7. 经验预估特征（2个）
        features_df['vice_power_estimate_v1'] = features_df['weight_diff'] * 0.8 + features_df['silicon_energy'] * 0.6
        features_df['vice_power_estimate_v2'] = features_df['weight_diff'] * features_df['thermal_efficiency_est'] * 0.7
        
        # 8. 设备和时间特征（5个）
        if 'folder_name' in features_df.columns:
            device_mapping = {name: idx for idx, name in enumerate(features_df['folder_name'].unique())}
            features_df['device_id'] = features_df['folder_name'].map(device_mapping)
        else:
            features_df['device_id'] = 0
            
        if 'start_time' in features_df.columns:
            features_df['start_time'] = pd.to_datetime(features_df['start_time'])
            features_df['start_hour'] = features_df['start_time'].dt.hour
            features_df['start_day_of_week'] = features_df['start_time'].dt.dayofweek
            features_df['is_work_hours'] = ((features_df['start_hour'] >= 8) & (features_df['start_hour'] <= 18)).astype(int)
            features_df['is_weekend'] = (features_df['start_day_of_week'] >= 5).astype(int)
        else:
            features_df['start_hour'] = 12
            features_df['start_day_of_week'] = 1
            features_df['is_work_hours'] = 1
            features_df['is_weekend'] = 0
        
        # 选择最终特征（26个）
        feature_cols = [
            'weight_diff', 'silicon_energy', 'is_first_cast',
            'energy_density', 'melting_energy_ratio', 'thermal_efficiency_est',
            'first_cast_weight_factor', 'first_cast_energy_factor',
            'recast_weight_factor', 'recast_energy_factor',
            'weight_category', 'energy_category',
            'weight_energy_balance', 'process_weight_energy', 'weight_energy_interaction',
            'log_weight', 'log_energy', 'sqrt_weight', 'sqrt_energy',
            'vice_power_estimate_v1', 'vice_power_estimate_v2',
            'device_id', 'start_hour', 'start_day_of_week', 'is_work_hours', 'is_weekend'
        ]
        
        # 确保所有特征都存在
        for col in feature_cols:
            if col not in features_df.columns:
                features_df[col] = 0
        
        self.feature_names = feature_cols
        
        return features_df[feature_cols].fillna(0)
