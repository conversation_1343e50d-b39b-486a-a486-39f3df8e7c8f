#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的两特征SVR模型
只使用重量差异和硅热能
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime

def main():
    print("="*60)
    print("🧪 简化SVR模型 - 只使用重量差异和硅热能")
    print("="*60)
    
    # 检查数据文件
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    try:
        # 加载数据
        df = pd.read_csv(data_file)
        print(f"✅ 数据加载成功: {df.shape}")
        
        # 检查必要列
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"❌ 缺少列: {missing_cols}")
            return
        
        # 数据清洗
        df_clean = df.dropna(subset=required_cols)
        df_filtered = df_clean[
            (df_clean['weight_difference'] > 0) &
            (df_clean['weight_difference'] < 1000) &
            (df_clean['silicon_thermal_energy_kwh'] > 0) &
            (df_clean['silicon_thermal_energy_kwh'] < 1000) &
            (df_clean['vice_total_energy_kwh'] > 0) &
            (df_clean['vice_total_energy_kwh'] < 2000)
        ]
        
        print(f"📊 清洗后数据: {df_filtered.shape}")
        
        # 数据统计
        print(f"\n📈 数据统计:")
        print(f"  重量差异: {df_filtered['weight_difference'].min():.1f} - {df_filtered['weight_difference'].max():.1f} kg")
        print(f"  硅热能: {df_filtered['silicon_thermal_energy_kwh'].min():.1f} - {df_filtered['silicon_thermal_energy_kwh'].max():.1f} kWh")
        print(f"  副功率: {df_filtered['vice_total_energy_kwh'].min():.1f} - {df_filtered['vice_total_energy_kwh'].max():.1f} kWh")
        
        # 尝试导入sklearn
        try:
            from sklearn.svm import SVR
            from sklearn.preprocessing import StandardScaler
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import mean_absolute_error, r2_score
            import joblib
            
            print(f"\n✅ sklearn可用")
            
            # 准备特征
            def prepare_features(weight_diff, silicon_energy):
                """准备特征向量"""
                # 基础特征
                base = [weight_diff, silicon_energy]
                
                # 工程特征
                engineered = [
                    weight_diff ** 2,
                    silicon_energy ** 2,
                    np.sqrt(abs(weight_diff)),
                    np.sqrt(abs(silicon_energy)),
                    np.log1p(abs(weight_diff)),
                    np.log1p(abs(silicon_energy)),
                    weight_diff * silicon_energy,
                    weight_diff / max(silicon_energy, 0.1),
                    silicon_energy / max(weight_diff, 0.1),
                    (weight_diff + silicon_energy) / 2
                ]
                
                return base + engineered
            
            # 准备训练数据
            X = []
            for _, row in df_filtered.iterrows():
                features = prepare_features(
                    row['weight_difference'], 
                    row['silicon_thermal_energy_kwh']
                )
                X.append(features)
            
            X = np.array(X)
            y = df_filtered['vice_total_energy_kwh'].values
            
            print(f"📊 特征矩阵: {X.shape}")
            
            # 数据分割
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # 标准化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # 训练模型
            print(f"\n🚀 训练SVR模型...")
            svr = SVR(kernel='rbf', C=100, gamma='scale', epsilon=0.1)
            svr.fit(X_train_scaled, y_train)
            
            # 预测和评估
            y_pred = svr.predict(X_test_scaled)
            
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            errors = np.abs(y_pred - y_test)
            acc_5 = (errors <= 5).mean() * 100
            acc_10 = (errors <= 10).mean() * 100
            acc_15 = (errors <= 15).mean() * 100
            
            print(f"\n📊 模型性能:")
            print(f"  ±5kWh准确率: {acc_5:.2f}%")
            print(f"  ±10kWh准确率: {acc_10:.2f}%")
            print(f"  ±15kWh准确率: {acc_15:.2f}%")
            print(f"  MAE: {mae:.2f} kWh")
            print(f"  R²: {r2:.4f}")
            
            # 保存模型
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_dir = Path(f"simple_two_feature_model_{timestamp}")
            model_dir.mkdir(exist_ok=True)
            
            joblib.dump(svr, model_dir / "svr_model.joblib")
            joblib.dump(scaler, model_dir / "scaler.joblib")
            
            # 保存模型信息
            model_info = {
                'timestamp': timestamp,
                'model_type': 'SVR with 2 input features',
                'input_features': ['weight_difference', 'silicon_thermal_energy_kwh'],
                'total_features': X.shape[1],
                'performance': {
                    'accuracy_5kwh': float(acc_5),
                    'accuracy_10kwh': float(acc_10),
                    'accuracy_15kwh': float(acc_15),
                    'mae': float(mae),
                    'r2_score': float(r2)
                },
                'training_data': {
                    'total_samples': len(df_filtered),
                    'training_samples': len(X_train),
                    'test_samples': len(X_test)
                }
            }
            
            with open(model_dir / "model_info.json", 'w', encoding='utf-8') as f:
                json.dump(model_info, f, indent=2, ensure_ascii=False)
            
            # 创建使用示例
            usage_example = f"""# 简化SVR模型使用示例

## 模型信息
- 输入特征: 重量差异(kg), 硅热能(kWh)
- ±10kWh准确率: {acc_10:.2f}%
- MAE: {mae:.2f} kWh

## 使用方法

```python
import joblib
import numpy as np

# 加载模型
model = joblib.load('{model_dir}/svr_model.joblib')
scaler = joblib.load('{model_dir}/scaler.joblib')

def predict_vice_power(weight_difference, silicon_thermal_energy):
    \"\"\"
    预测副功率
    
    参数:
    weight_difference: 重量差异 (kg)
    silicon_thermal_energy: 硅热能 (kWh)
    
    返回:
    predicted_vice_power: 预测的副功率 (kWh)
    \"\"\"
    # 准备特征
    base = [weight_difference, silicon_thermal_energy]
    engineered = [
        weight_difference ** 2,
        silicon_thermal_energy ** 2,
        np.sqrt(abs(weight_difference)),
        np.sqrt(abs(silicon_thermal_energy)),
        np.log1p(abs(weight_difference)),
        np.log1p(abs(silicon_thermal_energy)),
        weight_difference * silicon_thermal_energy,
        weight_difference / max(silicon_thermal_energy, 0.1),
        silicon_thermal_energy / max(weight_difference, 0.1),
        (weight_difference + silicon_thermal_energy) / 2
    ]
    
    features = np.array([base + engineered])
    
    # 标准化和预测
    features_scaled = scaler.transform(features)
    prediction = model.predict(features_scaled)[0]
    
    return prediction

# 使用示例
weight_diff = 200.0  # kg
silicon_energy = 150.0  # kWh
predicted_power = predict_vice_power(weight_diff, silicon_energy)
print(f"预测副功率: {{predicted_power:.2f}} kWh")
```

## 输入范围建议
- 重量差异: {df_filtered['weight_difference'].min():.1f} - {df_filtered['weight_difference'].max():.1f} kg
- 硅热能: {df_filtered['silicon_thermal_energy_kwh'].min():.1f} - {df_filtered['silicon_thermal_energy_kwh'].max():.1f} kWh
"""
            
            with open(model_dir / "usage_example.md", 'w', encoding='utf-8') as f:
                f.write(usage_example)
            
            # 预测示例
            print(f"\n🎯 预测示例:")
            test_cases = [
                (200.0, 150.0),
                (180.0, 140.0),
                (220.0, 160.0),
                (100.0, 80.0),
                (250.0, 180.0)
            ]
            
            for weight_diff, silicon_energy in test_cases:
                features = prepare_features(weight_diff, silicon_energy)
                features_scaled = scaler.transform([features])
                pred = svr.predict(features_scaled)[0]
                print(f"  重量差异: {weight_diff}kg, 硅热能: {silicon_energy}kWh → 预测副功率: {pred:.2f}kWh")
            
            print(f"\n💾 模型已保存到: {model_dir}")
            print(f"✅ 简化模型训练完成!")
            
            return model_info
            
        except ImportError as e:
            print(f"❌ sklearn导入失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🎉 成功创建只需要重量差异和硅热能的简化模型!")
    else:
        print(f"\n❌ 模型创建失败")
