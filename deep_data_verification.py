#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入验证17,134个样本的真实性，检查重复数据，重新测试模型
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def find_and_analyze_all_data():
    """查找并分析所有数据源"""
    print("🔍 查找并分析所有数据源...")
    
    # 查找所有可能的数据文件
    all_csv_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.csv') and os.path.getsize(Path(root) / file) > 1024:  # 大于1KB
                all_csv_files.append(Path(root) / file)
    
    print(f"找到 {len(all_csv_files)} 个CSV文件:")
    
    # 分析每个文件
    data_sources = []
    for file_path in all_csv_files:
        try:
            # 读取前几行检查结构
            df_sample = pd.read_csv(file_path, nrows=5)
            file_size = os.path.getsize(file_path)
            
            # 检查是否包含关键列
            key_columns = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
            alt_columns = ['Weight_Difference', 'Silicon_Thermal_Energy_kWh', 'Vice_Total_Energy_kWh']
            
            has_key_cols = all(col in df_sample.columns for col in key_columns)
            has_alt_cols = all(col in df_sample.columns for col in alt_columns)
            
            if has_key_cols or has_alt_cols:
                # 读取完整文件
                df_full = pd.read_csv(file_path)
                
                data_info = {
                    'file_path': str(file_path),
                    'file_size_kb': file_size / 1024,
                    'row_count': len(df_full),
                    'column_count': len(df_full.columns),
                    'has_standard_cols': has_key_cols,
                    'has_alt_cols': has_alt_cols,
                    'dataframe': df_full
                }
                data_sources.append(data_info)
                
                print(f"  📄 {file_path}")
                print(f"      大小: {file_size/1024:.1f}KB, 行数: {len(df_full)}, 列数: {len(df_full.columns)}")
        
        except Exception as e:
            continue
    
    return data_sources

def analyze_data_duplicates(data_sources):
    """分析数据重复情况"""
    print(f"\n🔍 分析数据重复情况...")
    
    if not data_sources:
        print("❌ 没有找到有效的数据源")
        return None
    
    # 找到最大的数据集
    largest_dataset = max(data_sources, key=lambda x: x['row_count'])
    print(f"最大数据集: {largest_dataset['file_path']} ({largest_dataset['row_count']} 行)")
    
    df = largest_dataset['dataframe'].copy()
    
    # 标准化列名
    column_mapping = {
        'Weight_Difference': 'weight_difference',
        'Silicon_Thermal_Energy_kWh': 'silicon_thermal_energy_kwh',
        'Vice_Total_Energy_kWh': 'vice_total_energy_kwh',
        'Feed_Type': 'feed_type',
        'Folder_Name': 'folder_name',
        'Start_Time': 'start_time'
    }
    
    for old_name, new_name in column_mapping.items():
        if old_name in df.columns:
            df[new_name] = df[old_name]
    
    print(f"\n数据质量分析:")
    print(f"  原始行数: {len(df)}")
    
    # 检查完全重复的行
    duplicate_rows = df.duplicated()
    print(f"  完全重复行: {duplicate_rows.sum()} ({duplicate_rows.sum()/len(df)*100:.1f}%)")
    
    # 检查关键字段重复
    key_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
    if all(col in df.columns for col in key_cols):
        key_duplicates = df[key_cols].duplicated()
        print(f"  关键字段重复: {key_duplicates.sum()} ({key_duplicates.sum()/len(df)*100:.1f}%)")
        
        # 移除重复数据
        df_clean = df[key_cols + (['feed_type', 'folder_name', 'start_time'] if all(c in df.columns for c in ['feed_type', 'folder_name', 'start_time']) else [])].drop_duplicates(subset=key_cols)
        print(f"  去重后行数: {len(df_clean)}")
        print(f"  实际有效样本: {len(df_clean)}")
        
        return df_clean, df
    else:
        print(f"❌ 缺少关键列: {key_cols}")
        return None, df

def load_original_model():
    """加载原始训练的模型"""
    print(f"\n🤖 加载原始训练的模型...")
    
    try:
        # 尝试加载训练好的模型
        model_dir = Path("realtime_vice_power_models")
        if model_dir.exists():
            model_files = list(model_dir.glob("*.joblib"))
            if model_files:
                import joblib
                
                # 查找集成模型
                ensemble_model_file = model_dir / "ensemble_model.joblib"
                if ensemble_model_file.exists():
                    model = joblib.load(ensemble_model_file)
                    print(f"✅ 加载集成模型: {ensemble_model_file}")
                    return model, "ensemble"
                else:
                    # 加载第一个可用模型
                    model = joblib.load(model_files[0])
                    print(f"✅ 加载模型: {model_files[0]}")
                    return model, "single"
        
        # 如果没有找到模型文件，尝试使用v7预测器
        print("⚠️ 未找到训练好的模型文件，尝试使用v7预测器...")
        
        v7_path = Path("kongwen_power_control/beta_version/v7")
        sys.path.insert(0, str(v7_path))
        
        # 清除之前的导入
        modules_to_remove = [m for m in sys.modules.keys() if 'model' in m or 'predictor' in m]
        for module in modules_to_remove:
            if module != '__main__':
                del sys.modules[module]
        
        from model import VicePowerControlModel
        v7_model = VicePowerControlModel()
        print(f"✅ 加载v7模型")
        return v7_model, "v7"
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None

def test_model_with_clean_data(model, model_type, df_clean, sample_size=500):
    """使用清洁数据测试模型"""
    print(f"\n🧪 使用清洁数据测试模型...")
    
    if model is None or df_clean is None:
        print("❌ 模型或数据不可用")
        return None
    
    # 随机抽取样本
    if len(df_clean) > sample_size:
        test_samples = df_clean.sample(n=sample_size, random_state=42)
        print(f"从 {len(df_clean)} 个有效样本中随机抽取 {sample_size} 个进行测试")
    else:
        test_samples = df_clean.copy()
        print(f"使用全部 {len(df_clean)} 个样本进行测试")
    
    predictions = []
    errors = []
    successful_predictions = 0
    
    print(f"开始预测...")
    
    for idx, row in test_samples.iterrows():
        try:
            actual_value = row['vice_total_energy_kwh']
            
            if model_type == "v7":
                # 重置模型状态
                if hasattr(model, 'reset_vice_power_state'):
                    model.reset_vice_power_state()
                
                # 准备v7模型参数
                params = {
                    't': 0,
                    'ratio': 1.0,
                    'ccd': 1400,
                    'ccd3': 1400,
                    'fullmelting': True,
                    'sum_jialiao_time': 3600,
                    'last_jialiao_weight': row['weight_difference'],
                    'last_Interval_time': 600,
                    'barrelage': row['weight_difference'],
                    'time_interval': 600,
                    'cumulative_feed_weight': row['weight_difference'] * (1 if row.get('feed_type') == '首投' else 2)
                }
                
                main_power, vice_power, vice_info = model.predict(**params)
                predicted_value = vice_info.get('predicted_total', 0)
                
            elif model_type in ["ensemble", "single"]:
                # 使用训练好的机器学习模型
                # 这里需要特征工程，但我们先用简单的特征
                features = np.array([[row['weight_difference'], row['silicon_thermal_energy_kwh']]])
                predicted_value = model.predict(features)[0]
            
            else:
                print(f"❌ 未知模型类型: {model_type}")
                continue
            
            error = abs(predicted_value - actual_value)
            
            predictions.append({
                'sample_id': idx,
                'weight_difference': row['weight_difference'],
                'silicon_thermal_energy_kwh': row['silicon_thermal_energy_kwh'],
                'actual_vice_power': actual_value,
                'predicted_vice_power': predicted_value,
                'absolute_error': error,
                'relative_error': (error / actual_value * 100) if actual_value > 0 else 0,
                'within_5kwh': error <= 5,
                'within_10kwh': error <= 10,
                'within_15kwh': error <= 15,
                'within_20kwh': error <= 20,
                'feed_type': row.get('feed_type', 'unknown')
            })
            
            successful_predictions += 1
            
            if successful_predictions % 50 == 0:
                print(f"  已完成 {successful_predictions} 个预测...")
                
        except Exception as e:
            print(f"  ⚠️ 样本 {idx} 预测失败: {e}")
            continue
    
    if not predictions:
        print("❌ 没有成功的预测")
        return None
    
    results_df = pd.DataFrame(predictions)
    
    # 计算性能指标
    performance = {
        'total_samples': len(results_df),
        'successful_predictions': successful_predictions,
        'success_rate': successful_predictions / len(test_samples) * 100,
        'mae': results_df['absolute_error'].mean(),
        'rmse': np.sqrt((results_df['absolute_error'] ** 2).mean()),
        'min_error': results_df['absolute_error'].min(),
        'max_error': results_df['absolute_error'].max(),
        'acc_5kwh': results_df['within_5kwh'].mean() * 100,
        'acc_10kwh': results_df['within_10kwh'].mean() * 100,
        'acc_15kwh': results_df['within_15kwh'].mean() * 100,
        'acc_20kwh': results_df['within_20kwh'].mean() * 100,
        'mean_relative_error': results_df['relative_error'].mean()
    }
    
    print(f"\n📊 测试结果:")
    print(f"  测试样本: {len(test_samples)}")
    print(f"  成功预测: {successful_predictions}")
    print(f"  成功率: {performance['success_rate']:.1f}%")
    print(f"  平均绝对误差: {performance['mae']:.2f} kWh")
    print(f"  ±5kWh准确率: {performance['acc_5kwh']:.1f}%")
    print(f"  ±10kWh准确率: {performance['acc_10kwh']:.1f}%")
    print(f"  ±15kWh准确率: {performance['acc_15kwh']:.1f}%")
    print(f"  ±20kWh准确率: {performance['acc_20kwh']:.1f}%")
    
    return results_df, performance

def save_verification_results(results_df, performance, df_clean):
    """保存验证结果"""
    print(f"\n💾 保存验证结果...")
    
    timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细结果
    results_file = f"deep_verification_results_{timestamp}.csv"
    results_df.to_csv(results_file, index=False, encoding='utf-8-sig')
    print(f"✅ 详细结果已保存: {results_file}")
    
    # 保存性能报告
    performance_report = f"""# 深度验证报告 - {timestamp}

## 数据验证结果

### 数据质量分析
- **有效样本数**: {len(df_clean)}
- **测试样本数**: {len(results_df)}
- **预测成功率**: {performance['success_rate']:.1f}%

### 性能指标
- **平均绝对误差**: {performance['mae']:.2f} kWh
- **均方根误差**: {performance['rmse']:.2f} kWh
- **最小误差**: {performance['min_error']:.1f} kWh
- **最大误差**: {performance['max_error']:.1f} kWh

### 准确率分析
- **±5kWh准确率**: {performance['acc_5kwh']:.1f}%
- **±10kWh准确率**: {performance['acc_10kwh']:.1f}%
- **±15kWh准确率**: {performance['acc_15kwh']:.1f}%
- **±20kWh准确率**: {performance['acc_20kwh']:.1f}%

### 与71.3%准确率的对比
- **训练时±10kWh准确率**: 71.3%
- **本次验证±10kWh准确率**: {performance['acc_10kwh']:.1f}%
- **性能差异**: {performance['acc_10kwh'] - 71.3:+.1f}%

## 结论
{'✅ 性能接近训练结果' if abs(performance['acc_10kwh'] - 71.3) < 10 else '❌ 性能显著下降'}
"""
    
    report_file = f"deep_verification_report_{timestamp}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(performance_report)
    
    print(f"✅ 验证报告已保存: {report_file}")
    
    return results_file, report_file

def main():
    """主函数"""
    print("="*60)
    print("深度验证17,134个样本的真实性")
    print("="*60)
    
    # 1. 环境检查
    if not check_environment():
        return False
    
    # 2. 查找并分析所有数据源
    data_sources = find_and_analyze_all_data()
    
    # 3. 分析数据重复情况
    df_clean, df_original = analyze_data_duplicates(data_sources)
    
    if df_clean is None:
        print("❌ 数据分析失败")
        return False
    
    # 4. 加载原始模型
    model, model_type = load_original_model()
    
    if model is None:
        print("❌ 模型加载失败")
        return False
    
    # 5. 使用清洁数据测试模型
    results_df, performance = test_model_with_clean_data(model, model_type, df_clean)
    
    if results_df is None:
        print("❌ 模型测试失败")
        return False
    
    # 6. 保存验证结果
    results_file, report_file = save_verification_results(results_df, performance, df_clean)
    
    print(f"\n🎯 深度验证完成！")
    print(f"关键发现:")
    print(f"  - 有效样本数: {len(df_clean)}")
    print(f"  - ±10kWh准确率: {performance['acc_10kwh']:.1f}%")
    print(f"  - 与71.3%的差异: {performance['acc_10kwh'] - 71.3:+.1f}%")
    
    if abs(performance['acc_10kwh'] - 71.3) < 10:
        print(f"  ✅ 性能接近训练结果，71.3%基本可信")
    else:
        print(f"  ❌ 性能显著不同，需要进一步分析")
    
    return True

if __name__ == "__main__":
    success = main()
