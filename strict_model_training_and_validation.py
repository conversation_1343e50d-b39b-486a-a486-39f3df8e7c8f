#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格的模型训练和验证 - 防止数据泄露
在lj_env_1环境中训练并保存模型，严格验证准确率
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import joblib
from pathlib import Path
from datetime import datetime
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GridSearchCV, StratifiedKFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class StrictModelTrainer:
    """严格的模型训练器 - 防止数据泄露"""
    
    def __init__(self):
        self.verify_environment()
        self.model = None
        self.scaler = None
        self.feature_names = None
        self.training_log = []
        
    def verify_environment(self):
        """验证lj_env_1环境"""
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        print("="*60)
        print("🔍 环境验证")
        print("="*60)
        print(f"当前Conda环境: {conda_env}")
        
        if conda_env != 'lj_env_1':
            print(f"❌ 错误：当前环境为 {conda_env}")
            print("必须在 lj_env_1 环境中运行")
            raise EnvironmentError("必须在lj_env_1环境中运行此脚本")
        
        print("✅ 环境验证通过：lj_env_1")
        
        # 检查依赖包
        try:
            import sklearn
            print(f"scikit-learn版本: {sklearn.__version__}")
            self.log_step("环境验证", "通过", {"conda_env": conda_env, "sklearn_version": sklearn.__version__})
        except ImportError:
            raise ImportError("scikit-learn未安装")
    
    def log_step(self, step, status, details=None):
        """记录训练步骤"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'step': step,
            'status': status,
            'details': details or {}
        }
        self.training_log.append(log_entry)
    
    def load_and_split_data(self, test_size=0.2, random_state=42):
        """
        加载数据并严格分割 - 防止数据泄露
        """
        print("\n" + "="*60)
        print("📊 数据加载和分割")
        print("="*60)
        
        # 加载数据
        data_file = Path("output_results/A01_A40_cycles__analysis.csv")
        if not data_file.exists():
            raise FileNotFoundError(f"数据文件不存在: {data_file}")
        
        df = pd.read_csv(data_file)
        print(f"✅ 原始数据加载: {df.shape}")
        
        # 检查必要列
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"缺少必要列: {missing_cols}")
        
        # 数据清洗
        df_clean = df.dropna(subset=required_cols)
        df_filtered = df_clean[
            (df_clean['weight_difference'] > 0) &
            (df_clean['weight_difference'] < 1000) &
            (df_clean['silicon_thermal_energy_kwh'] > 0) &
            (df_clean['silicon_thermal_energy_kwh'] < 1000) &
            (df_clean['vice_total_energy_kwh'] > 0) &
            (df_clean['vice_total_energy_kwh'] < 2000)
        ]
        
        print(f"📊 数据清洗:")
        print(f"  原始: {df.shape[0]} 条")
        print(f"  删除缺失值: {df_clean.shape[0]} 条")
        print(f"  过滤异常值: {df_filtered.shape[0]} 条")
        
        # 严格的数据分割 - 在特征工程之前分割
        print(f"\n🔒 严格数据分割 (防止数据泄露):")
        print(f"  测试集比例: {test_size}")
        print(f"  随机种子: {random_state}")
        
        # 基于副功率进行分层分割，确保训练集和测试集分布相似
        # 创建分层标签
        y_raw = df_filtered['vice_total_energy_kwh'].values
        y_bins = pd.qcut(y_raw, q=5, labels=False, duplicates='drop')  # 分成5个区间
        
        train_indices, test_indices = train_test_split(
            range(len(df_filtered)),
            test_size=test_size,
            random_state=random_state,
            stratify=y_bins
        )
        
        df_train = df_filtered.iloc[train_indices].copy()
        df_test = df_filtered.iloc[test_indices].copy()
        
        print(f"  训练集: {len(df_train)} 条")
        print(f"  测试集: {len(df_test)} 条")
        
        # 验证分割质量
        train_mean = df_train['vice_total_energy_kwh'].mean()
        test_mean = df_test['vice_total_energy_kwh'].mean()
        print(f"  训练集副功率均值: {train_mean:.2f} kWh")
        print(f"  测试集副功率均值: {test_mean:.2f} kWh")
        print(f"  分布差异: {abs(train_mean - test_mean):.2f} kWh")
        
        self.log_step("数据分割", "完成", {
            "total_samples": len(df_filtered),
            "train_samples": len(df_train),
            "test_samples": len(df_test),
            "train_mean": float(train_mean),
            "test_mean": float(test_mean)
        })
        
        return df_train, df_test
    
    def engineer_features(self, df):
        """特征工程"""
        features_list = []
        
        for _, row in df.iterrows():
            weight_diff = row['weight_difference']
            silicon_energy = row['silicon_thermal_energy_kwh']
            
            # 基础特征
            base_features = [weight_diff, silicon_energy]
            
            # 工程特征
            engineered_features = [
                weight_diff ** 2,                           # 重量差异平方
                silicon_energy ** 2,                        # 硅热能平方
                np.sqrt(abs(weight_diff)),                  # 重量差异开方
                np.sqrt(abs(silicon_energy)),               # 硅热能开方
                np.log1p(abs(weight_diff)),                 # 重量差异对数
                np.log1p(abs(silicon_energy)),              # 硅热能对数
                weight_diff * silicon_energy,               # 交互项
                weight_diff / max(silicon_energy, 0.1),     # 比率1
                silicon_energy / max(weight_diff, 0.1),     # 比率2
                (weight_diff + silicon_energy) / 2,         # 平均值
                abs(weight_diff - silicon_energy),          # 差值绝对值
                max(weight_diff, silicon_energy)            # 最大值
            ]
            
            all_features = base_features + engineered_features
            features_list.append(all_features)
        
        feature_matrix = np.array(features_list)
        
        if self.feature_names is None:
            self.feature_names = [
                'weight_difference', 'silicon_thermal_energy',
                'weight_diff_squared', 'silicon_energy_squared',
                'weight_diff_sqrt', 'silicon_energy_sqrt',
                'weight_diff_log', 'silicon_energy_log',
                'interaction', 'weight_silicon_ratio',
                'silicon_weight_ratio', 'average',
                'difference_abs', 'maximum'
            ]
        
        return feature_matrix
    
    def train_model(self, df_train):
        """训练模型 - 只使用训练数据"""
        print(f"\n🚀 模型训练 (仅使用训练数据):")
        
        # 特征工程 - 只在训练数据上进行
        X_train = self.engineer_features(df_train)
        y_train = df_train['vice_total_energy_kwh'].values
        
        print(f"  训练特征矩阵: {X_train.shape}")
        print(f"  特征数量: {len(self.feature_names)}")
        
        # 数据标准化 - 只在训练数据上fit
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        
        print(f"  数据标准化完成")
        
        # 超参数优化 - 使用交叉验证
        print(f"  超参数优化...")
        param_grid = {
            'C': [50, 100, 200, 500],
            'gamma': ['scale', 'auto', 0.01, 0.1],
            'epsilon': [0.1, 0.2, 0.5]
        }
        
        svr_base = SVR(kernel='rbf')
        
        # 使用StratifiedKFold确保CV的稳定性
        cv_folds = 5
        grid_search = GridSearchCV(
            svr_base, param_grid,
            cv=cv_folds,
            scoring='neg_mean_absolute_error',
            n_jobs=-1,
            verbose=0
        )
        
        grid_search.fit(X_train_scaled, y_train)
        self.model = grid_search.best_estimator_
        
        print(f"  ✅ 最佳参数: {grid_search.best_params_}")
        print(f"  ✅ CV分数: {-grid_search.best_score_:.2f} MAE")
        
        # 训练集性能评估
        y_train_pred = self.model.predict(X_train_scaled)
        train_mae = mean_absolute_error(y_train, y_train_pred)
        train_errors = np.abs(y_train_pred - y_train)
        train_acc_10 = (train_errors <= 10).mean() * 100
        
        print(f"  训练集MAE: {train_mae:.2f} kWh")
        print(f"  训练集±10kWh准确率: {train_acc_10:.2f}%")
        
        self.log_step("模型训练", "完成", {
            "best_params": grid_search.best_params_,
            "cv_score": float(-grid_search.best_score_),
            "train_mae": float(train_mae),
            "train_accuracy_10kwh": float(train_acc_10)
        })
        
        return X_train, y_train
    
    def test_model(self, df_test):
        """测试模型 - 严格使用测试数据"""
        print(f"\n🧪 模型测试 (严格使用测试数据):")
        
        if self.model is None or self.scaler is None:
            raise ValueError("模型尚未训练")
        
        # 特征工程 - 在测试数据上应用相同的变换
        X_test = self.engineer_features(df_test)
        y_test = df_test['vice_total_energy_kwh'].values
        
        print(f"  测试特征矩阵: {X_test.shape}")
        
        # 使用训练时的scaler进行标准化
        X_test_scaled = self.scaler.transform(X_test)
        
        # 预测
        y_test_pred = self.model.predict(X_test_scaled)
        
        # 评估指标
        test_mae = mean_absolute_error(y_test, y_test_pred)
        test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
        test_r2 = r2_score(y_test, y_test_pred)
        
        test_errors = np.abs(y_test_pred - y_test)
        test_acc_5 = (test_errors <= 5).mean() * 100
        test_acc_10 = (test_errors <= 10).mean() * 100
        test_acc_15 = (test_errors <= 15).mean() * 100
        
        # 详细结果
        test_results = {
            'mae': float(test_mae),
            'rmse': float(test_rmse),
            'r2': float(test_r2),
            'accuracy_5kwh': float(test_acc_5),
            'accuracy_10kwh': float(test_acc_10),
            'accuracy_15kwh': float(test_acc_15),
            'test_samples': len(y_test),
            'prediction_range': {
                'min': float(np.min(y_test_pred)),
                'max': float(np.max(y_test_pred)),
                'mean': float(np.mean(y_test_pred))
            },
            'actual_range': {
                'min': float(np.min(y_test)),
                'max': float(np.max(y_test)),
                'mean': float(np.mean(y_test))
            }
        }
        
        print(f"  📊 测试结果:")
        print(f"    MAE: {test_mae:.2f} kWh")
        print(f"    RMSE: {test_rmse:.2f} kWh")
        print(f"    R²: {test_r2:.4f}")
        print(f"    ±5kWh准确率: {test_acc_5:.2f}%")
        print(f"    ±10kWh准确率: {test_acc_10:.2f}%")
        print(f"    ±15kWh准确率: {test_acc_15:.2f}%")
        
        self.log_step("模型测试", "完成", test_results)
        
        return test_results, y_test, y_test_pred
    
    def save_model(self, test_results):
        """保存模型和相关文件"""
        print(f"\n💾 保存模型:")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path(f"lj_env_1_strict_model_{timestamp}")
        model_dir.mkdir(exist_ok=True)
        
        # 保存模型组件
        joblib.dump(self.model, model_dir / "svr_model.joblib")
        joblib.dump(self.scaler, model_dir / "scaler.joblib")
        
        # 保存特征名称
        with open(model_dir / "feature_names.json", 'w') as f:
            json.dump(self.feature_names, f, indent=2)
        
        # 保存完整的训练日志
        training_info = {
            'model_metadata': {
                'timestamp': timestamp,
                'training_environment': 'lj_env_1',
                'conda_env': os.environ.get('CONDA_DEFAULT_ENV'),
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'sklearn_version': __import__('sklearn').__version__,
                'data_leakage_prevention': 'STRICT_SPLIT_BEFORE_FEATURE_ENGINEERING'
            },
            'test_results': test_results,
            'feature_names': self.feature_names,
            'training_log': self.training_log
        }
        
        with open(model_dir / "training_info.json", 'w', encoding='utf-8') as f:
            json.dump(training_info, f, indent=2, ensure_ascii=False)
        
        # 创建预测函数文件
        prediction_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
lj_env_1环境严格训练的副功率预测模型
训练时间: {timestamp}
测试准确率: {test_results['accuracy_10kwh']:.2f}%
"""

import numpy as np
import joblib
from pathlib import Path

class VicePowerPredictor:
    def __init__(self, model_dir="{model_dir}"):
        self.model_dir = Path(model_dir)
        self.model = joblib.load(self.model_dir / "svr_model.joblib")
        self.scaler = joblib.load(self.model_dir / "scaler.joblib")
        
    def predict(self, weight_difference, silicon_thermal_energy):
        """
        预测副功率
        
        参数:
        weight_difference: 重量差异 (kg)
        silicon_thermal_energy: 硅热能 (kWh)
        
        返回:
        predicted_vice_power: 预测的副功率 (kWh)
        """
        # 特征工程
        features = [
            weight_difference, silicon_thermal_energy,
            weight_difference**2, silicon_thermal_energy**2,
            np.sqrt(abs(weight_difference)), np.sqrt(abs(silicon_thermal_energy)),
            np.log1p(abs(weight_difference)), np.log1p(abs(silicon_thermal_energy)),
            weight_difference * silicon_thermal_energy,
            weight_difference / max(silicon_thermal_energy, 0.1),
            silicon_thermal_energy / max(weight_difference, 0.1),
            (weight_difference + silicon_thermal_energy) / 2,
            abs(weight_difference - silicon_thermal_energy),
            max(weight_difference, silicon_thermal_energy)
        ]
        
        # 标准化和预测
        features_scaled = self.scaler.transform([features])
        prediction = self.model.predict(features_scaled)[0]
        
        return prediction

# 使用示例
if __name__ == "__main__":
    predictor = VicePowerPredictor()
    
    # 测试预测
    weight_diff = 200.0  # kg
    silicon_energy = 150.0  # kWh
    result = predictor.predict(weight_diff, silicon_energy)
    print(f"预测副功率: {{result:.2f}} kWh")
'''
        
        with open(model_dir / "predictor.py", 'w', encoding='utf-8') as f:
            f.write(prediction_code)
        
        print(f"  ✅ 模型已保存到: {model_dir}")
        print(f"  📁 包含文件:")
        print(f"    - svr_model.joblib (SVR模型)")
        print(f"    - scaler.joblib (数据标准化器)")
        print(f"    - feature_names.json (特征名称)")
        print(f"    - training_info.json (训练信息)")
        print(f"    - predictor.py (预测器类)")
        
        return model_dir

def main():
    """主函数"""
    print("="*60)
    print("🔒 严格模型训练和验证 (防止数据泄露)")
    print("="*60)
    print("确保在lj_env_1环境中训练，严格验证准确率")
    
    try:
        # 创建训练器
        trainer = StrictModelTrainer()
        
        # 1. 严格数据分割
        df_train, df_test = trainer.load_and_split_data(test_size=0.2, random_state=42)
        
        # 2. 训练模型 (只使用训练数据)
        X_train, y_train = trainer.train_model(df_train)
        
        # 3. 测试模型 (严格使用测试数据)
        test_results, y_test, y_test_pred = trainer.test_model(df_test)
        
        # 4. 保存模型
        model_dir = trainer.save_model(test_results)
        
        # 5. 数据泄露检查
        print(f"\n🔍 数据泄露检查:")
        print(f"  ✅ 数据分割在特征工程之前")
        print(f"  ✅ 标准化器只在训练数据上fit")
        print(f"  ✅ 超参数优化只使用训练数据")
        print(f"  ✅ 测试数据完全独立")
        
        # 6. 最终总结
        print(f"\n" + "="*60)
        print("📊 最终结果")
        print("="*60)
        print(f"✅ 环境: lj_env_1")
        print(f"✅ 训练样本: {len(df_train)}")
        print(f"✅ 测试样本: {len(df_test)}")
        print(f"✅ 测试MAE: {test_results['mae']:.2f} kWh")
        print(f"✅ 测试±10kWh准确率: {test_results['accuracy_10kwh']:.2f}%")
        print(f"✅ 模型保存位置: {model_dir}")
        print(f"✅ 数据泄露检查: 通过")
        
        return trainer, test_results, model_dir
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    trainer, results, model_dir = main()
    if trainer and results:
        print(f"\n🎉 严格训练完成，模型可信度高!")
    else:
        print(f"\n❌ 训练失败")
