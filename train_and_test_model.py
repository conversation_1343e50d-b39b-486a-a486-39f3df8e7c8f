#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用output_results数据训练SVR模型并测试效果
只使用重量差异和硅热能两个特征
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import joblib
from pathlib import Path
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class VicePowerModelTrainer:
    """副功率预测模型训练器"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.training_data = None
        self.results = {}
        
    def load_data(self):
        """加载所有可用的数据"""
        print("="*60)
        print("📊 加载训练数据")
        print("="*60)
        
        data_dir = Path("output_results")
        if not data_dir.exists():
            raise FileNotFoundError("output_results目录不存在")
        
        # 主要数据文件
        main_file = data_dir / "A01_A40_cycles__analysis.csv"
        
        if main_file.exists():
            print(f"✅ 加载主数据文件: {main_file}")
            df_main = pd.read_csv(main_file)
            print(f"📊 主数据形状: {df_main.shape}")
        else:
            raise FileNotFoundError("主数据文件不存在")
        
        # 加载其他分析文件
        additional_files = [
            "A01_A40_cycles__复投_analysis.csv",
            "A01_A40_cycles__首投_analysis.csv"
        ]
        
        df_list = [df_main]
        
        for file_name in additional_files:
            file_path = data_dir / file_name
            if file_path.exists():
                print(f"✅ 加载附加数据: {file_name}")
                df_add = pd.read_csv(file_path)
                print(f"📊 {file_name} 形状: {df_add.shape}")
                df_list.append(df_add)
        
        # 合并所有数据
        df_combined = pd.concat(df_list, ignore_index=True)
        
        # 去重
        df_combined = df_combined.drop_duplicates()
        
        print(f"📊 合并后总数据: {df_combined.shape}")
        
        # 检查必要列
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        missing_cols = [col for col in required_cols if col not in df_combined.columns]
        if missing_cols:
            raise ValueError(f"缺少必要列: {missing_cols}")
        
        print(f"✅ 数据列检查通过")
        print(f"📋 可用列: {list(df_combined.columns)}")
        
        return df_combined
    
    def clean_data(self, df):
        """数据清洗"""
        print(f"\n🔧 数据清洗...")
        
        print(f"原始数据: {df.shape}")
        
        # 删除缺失值
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        df_clean = df.dropna(subset=required_cols)
        print(f"删除缺失值后: {df_clean.shape}")
        
        # 过滤异常值
        df_filtered = df_clean[
            (df_clean['weight_difference'] > 0) &
            (df_clean['weight_difference'] < 1000) &
            (df_clean['silicon_thermal_energy_kwh'] > 0) &
            (df_clean['silicon_thermal_energy_kwh'] < 1000) &
            (df_clean['vice_total_energy_kwh'] > 0) &
            (df_clean['vice_total_energy_kwh'] < 2000)
        ]
        print(f"过滤异常值后: {df_filtered.shape}")
        
        # 数据统计
        print(f"\n📈 数据统计:")
        for col in required_cols:
            print(f"  {col}: {df_filtered[col].min():.2f} - {df_filtered[col].max():.2f}")
        
        return df_filtered
    
    def prepare_features(self, df):
        """准备特征"""
        print(f"\n🔧 特征工程...")
        
        features_list = []
        
        for _, row in df.iterrows():
            weight_diff = row['weight_difference']
            silicon_energy = row['silicon_thermal_energy_kwh']
            
            # 基础特征
            base_features = [weight_diff, silicon_energy]
            
            # 工程特征
            engineered_features = [
                weight_diff ** 2,                           # 重量差异平方
                silicon_energy ** 2,                        # 硅热能平方
                np.sqrt(abs(weight_diff)),                  # 重量差异开方
                np.sqrt(abs(silicon_energy)),               # 硅热能开方
                np.log1p(abs(weight_diff)),                 # 重量差异对数
                np.log1p(abs(silicon_energy)),              # 硅热能对数
                weight_diff * silicon_energy,               # 交互项
                weight_diff / max(silicon_energy, 0.1),     # 比率1
                silicon_energy / max(weight_diff, 0.1),     # 比率2
                (weight_diff + silicon_energy) / 2,         # 平均值
                abs(weight_diff - silicon_energy),          # 差值
                max(weight_diff, silicon_energy),           # 最大值
            ]
            
            all_features = base_features + engineered_features
            features_list.append(all_features)
        
        feature_matrix = np.array(features_list)
        
        feature_names = [
            'weight_difference', 'silicon_thermal_energy',
            'weight_diff_squared', 'silicon_energy_squared',
            'weight_diff_sqrt', 'silicon_energy_sqrt',
            'weight_diff_log', 'silicon_energy_log',
            'interaction', 'weight_silicon_ratio',
            'silicon_weight_ratio', 'average',
            'difference', 'maximum'
        ]
        
        print(f"✅ 特征工程完成: {feature_matrix.shape}")
        print(f"📋 特征名称: {feature_names}")
        
        return feature_matrix, feature_names
    
    def train_model(self, X, y, optimize=True):
        """训练模型"""
        print(f"\n🚀 训练SVR模型...")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=True
        )
        
        print(f"📊 训练集: {X_train.shape}")
        print(f"📊 测试集: {X_test.shape}")
        
        # 数据标准化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        if optimize:
            # 超参数优化
            print(f"🔧 超参数优化...")
            param_grid = {
                'C': [10, 50, 100, 200, 500],
                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1],
                'epsilon': [0.01, 0.1, 0.2, 0.5, 1.0]
            }
            
            svr_base = SVR(kernel='rbf')
            grid_search = GridSearchCV(
                svr_base, param_grid, 
                cv=5, scoring='neg_mean_absolute_error',
                n_jobs=-1, verbose=1
            )
            
            grid_search.fit(X_train_scaled, y_train)
            self.model = grid_search.best_estimator_
            
            print(f"✅ 最佳参数: {grid_search.best_params_}")
            print(f"✅ 最佳CV分数: {-grid_search.best_score_:.2f}")
            
            best_params = grid_search.best_params_
            cv_score = -grid_search.best_score_
        else:
            # 使用默认参数
            print(f"🔧 使用默认参数...")
            self.model = SVR(kernel='rbf', C=100, gamma='scale', epsilon=0.1)
            self.model.fit(X_train_scaled, y_train)
            
            best_params = {'C': 100, 'gamma': 'scale', 'epsilon': 0.1}
            cv_score = None
        
        # 预测和评估
        y_train_pred = self.model.predict(X_train_scaled)
        y_test_pred = self.model.predict(X_test_scaled)
        
        # 训练集评估
        train_mae = mean_absolute_error(y_train, y_train_pred)
        train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))
        train_r2 = r2_score(y_train, y_train_pred)
        
        train_errors = np.abs(y_train_pred - y_train)
        train_acc_5 = (train_errors <= 5).mean() * 100
        train_acc_10 = (train_errors <= 10).mean() * 100
        train_acc_15 = (train_errors <= 15).mean() * 100
        
        # 测试集评估
        test_mae = mean_absolute_error(y_test, y_test_pred)
        test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))
        test_r2 = r2_score(y_test, y_test_pred)
        
        test_errors = np.abs(y_test_pred - y_test)
        test_acc_5 = (test_errors <= 5).mean() * 100
        test_acc_10 = (test_errors <= 10).mean() * 100
        test_acc_15 = (test_errors <= 15).mean() * 100
        
        # 保存结果
        self.results = {
            'best_params': best_params,
            'cv_score': cv_score,
            'train_metrics': {
                'mae': float(train_mae),
                'rmse': float(train_rmse),
                'r2': float(train_r2),
                'accuracy_5kwh': float(train_acc_5),
                'accuracy_10kwh': float(train_acc_10),
                'accuracy_15kwh': float(train_acc_15)
            },
            'test_metrics': {
                'mae': float(test_mae),
                'rmse': float(test_rmse),
                'r2': float(test_r2),
                'accuracy_5kwh': float(test_acc_5),
                'accuracy_10kwh': float(test_acc_10),
                'accuracy_15kwh': float(test_acc_15)
            },
            'data_info': {
                'total_samples': len(X),
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'feature_count': X.shape[1]
            }
        }
        
        # 打印结果
        print(f"\n📊 训练结果:")
        print(f"  训练集 - MAE: {train_mae:.2f}, ±10kWh准确率: {train_acc_10:.2f}%")
        print(f"  测试集 - MAE: {test_mae:.2f}, ±10kWh准确率: {test_acc_10:.2f}%")
        print(f"  测试集 - RMSE: {test_rmse:.2f}, R²: {test_r2:.4f}")
        
        return X_train, X_test, y_train, y_test, y_train_pred, y_test_pred
    
    def save_model(self):
        """保存模型"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path(f"trained_vice_power_model_{timestamp}")
        model_dir.mkdir(exist_ok=True)
        
        # 保存模型组件
        joblib.dump(self.model, model_dir / "svr_model.joblib")
        joblib.dump(self.scaler, model_dir / "scaler.joblib")
        
        # 保存结果
        model_info = {
            'timestamp': timestamp,
            'model_type': 'SVR with 2 input features (weight_difference + silicon_thermal_energy)',
            'training_results': self.results,
            'environment': {
                'conda_env': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'sklearn_version': __import__('sklearn').__version__
            }
        }
        
        with open(model_dir / "model_info.json", 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        # 创建使用说明
        readme_content = f"""# 副功率预测模型

## 模型信息
- 训练时间: {timestamp}
- 输入特征: 重量差异(kg), 硅热能(kWh)
- 模型类型: Support Vector Regression

## 性能指标
- 测试集±10kWh准确率: {self.results['test_metrics']['accuracy_10kwh']:.2f}%
- 测试集MAE: {self.results['test_metrics']['mae']:.2f} kWh
- 测试集R²: {self.results['test_metrics']['r2']:.4f}

## 使用方法
```python
import joblib
import numpy as np

# 加载模型
model = joblib.load('svr_model.joblib')
scaler = joblib.load('scaler.joblib')

def predict_vice_power(weight_difference, silicon_thermal_energy):
    # 特征工程
    features = [
        weight_difference, silicon_thermal_energy,
        weight_difference**2, silicon_thermal_energy**2,
        np.sqrt(abs(weight_difference)), np.sqrt(abs(silicon_thermal_energy)),
        np.log1p(abs(weight_difference)), np.log1p(abs(silicon_thermal_energy)),
        weight_difference * silicon_thermal_energy,
        weight_difference / max(silicon_thermal_energy, 0.1),
        silicon_thermal_energy / max(weight_difference, 0.1),
        (weight_difference + silicon_thermal_energy) / 2,
        abs(weight_difference - silicon_thermal_energy),
        max(weight_difference, silicon_thermal_energy)
    ]
    
    # 标准化和预测
    features_scaled = scaler.transform([features])
    prediction = model.predict(features_scaled)[0]
    return prediction

# 使用示例
weight_diff = 200.0  # kg
silicon_energy = 150.0  # kWh
result = predict_vice_power(weight_diff, silicon_energy)
print(f"预测副功率: {{result:.2f}} kWh")
```
"""
        
        with open(model_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"\n💾 模型已保存到: {model_dir}")
        return model_dir

def main():
    """主函数"""
    print("="*60)
    print("🧪 副功率预测模型训练和测试")
    print("="*60)
    print("使用output_results数据，只需重量差异和硅热能")
    
    try:
        # 创建训练器
        trainer = VicePowerModelTrainer()
        
        # 1. 加载数据
        df = trainer.load_data()
        
        # 2. 数据清洗
        df_clean = trainer.clean_data(df)
        
        # 3. 特征工程
        X, feature_names = trainer.prepare_features(df_clean)
        y = df_clean['vice_total_energy_kwh'].values
        
        # 4. 训练模型
        X_train, X_test, y_train, y_test, y_train_pred, y_test_pred = trainer.train_model(X, y, optimize=True)
        
        # 5. 保存模型
        model_dir = trainer.save_model()
        
        # 6. 预测示例
        print(f"\n🎯 预测示例:")
        test_cases = [
            (200.0, 150.0),
            (100.0, 80.0),
            (300.0, 250.0),
            (50.0, 40.0),
            (400.0, 350.0)
        ]
        
        print(f"{'重量差异(kg)':<12} {'硅热能(kWh)':<12} {'预测副功率(kWh)':<15}")
        print("-" * 45)
        
        for weight_diff, silicon_energy in test_cases:
            # 准备特征
            features = [
                weight_diff, silicon_energy,
                weight_diff**2, silicon_energy**2,
                np.sqrt(abs(weight_diff)), np.sqrt(abs(silicon_energy)),
                np.log1p(abs(weight_diff)), np.log1p(abs(silicon_energy)),
                weight_diff * silicon_energy,
                weight_diff / max(silicon_energy, 0.1),
                silicon_energy / max(weight_diff, 0.1),
                (weight_diff + silicon_energy) / 2,
                abs(weight_diff - silicon_energy),
                max(weight_diff, silicon_energy)
            ]
            
            # 预测
            features_scaled = trainer.scaler.transform([features])
            prediction = trainer.model.predict(features_scaled)[0]
            
            print(f"{weight_diff:<12.1f} {silicon_energy:<12.1f} {prediction:<15.2f}")
        
        print(f"\n✅ 训练和测试完成!")
        print(f"📊 最终性能: ±10kWh准确率 {trainer.results['test_metrics']['accuracy_10kwh']:.2f}%")
        print(f"📁 模型保存位置: {model_dir}")
        
        return trainer
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    trainer = main()
