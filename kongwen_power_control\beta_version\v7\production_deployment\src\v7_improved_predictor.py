#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本改进预测器 - 基于真实数据分析
"""

import pandas as pd
import numpy as np
from pathlib import Path
from v7_feature_engineer import V7FeatureEngineer

class V7ImprovedPredictor:
    """v7版本改进预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.feature_engineer = V7FeatureEngineer()
        print("✅ v7改进预测器初始化完成")
    
    def predict(self, weight_difference, silicon_thermal_energy_kwh, feed_type, 
                folder_name=None, start_time=None):
        """
        改进的预测方法 - 基于真实数据分析
        """
        
        try:
            # 创建输入数据
            input_data = pd.DataFrame({
                'weight_difference': [weight_difference],
                'silicon_thermal_energy_kwh': [silicon_thermal_energy_kwh],
                'feed_type': [feed_type],
                'folder_name': [folder_name or 'analoga01'],
                'start_time': [start_time or pd.Timestamp.now()],
                'vice_total_energy_kwh': [0]  # 占位符
            })
            
            # 特征工程
            X = self.feature_engineer.create_realtime_features(input_data)
            
            # 使用基于真实数据分析的预测公式
            predicted_power = self._data_driven_prediction(X.iloc[0], weight_difference, 
                                                         silicon_thermal_energy_kwh, feed_type)
            
            # 计算置信度
            confidence = self._calculate_confidence(weight_difference, 
                                                   silicon_thermal_energy_kwh, 
                                                   feed_type)
            
            return {
                'predicted_vice_power': round(predicted_power, 2),
                'confidence': confidence,
                'input_features': {
                    'weight_difference': weight_difference,
                    'silicon_thermal_energy_kwh': silicon_thermal_energy_kwh,
                    'feed_type': feed_type
                },
                'model_type': 'improved_v7'
            }
            
        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            # 返回基础经验公式结果
            fallback_power = self._fallback_prediction(weight_difference, silicon_thermal_energy_kwh, feed_type)
            return {
                'predicted_vice_power': round(fallback_power, 2),
                'confidence': 'Low',
                'error': str(e),
                'model_type': 'fallback'
            }
    
    def _data_driven_prediction(self, features, weight_diff, silicon_energy, feed_type):
        """基于真实数据分析的预测公式"""
        
        # 基于真实数据的线性关系分析
        # 从验证结果看，实际副功率与重量和能量有较强的线性关系
        
        # 基础预测：主要基于重量和能量的线性组合
        base_prediction = weight_diff * 0.85 + silicon_energy * 0.75
        
        # 工艺类型调整
        if feed_type == '首投':
            # 首投通常需要较少的副功率
            process_factor = 0.75
        else:
            # 复投需要更多副功率
            process_factor = 1.0
        
        # 应用工艺因子
        prediction = base_prediction * process_factor
        
        # 基于重量范围的调整
        if weight_diff < 100:
            prediction *= 0.9  # 小重量调整
        elif weight_diff > 500:
            prediction *= 1.1  # 大重量调整
        
        # 基于能量密度的调整
        energy_density = silicon_energy / (weight_diff + 1e-6)
        if energy_density > 1.5:
            prediction *= 1.05  # 高能量密度
        elif energy_density < 0.5:
            prediction *= 0.95  # 低能量密度
        
        # 确保预测值在合理范围内（不要过度限制）
        prediction = max(50, min(800, prediction))
        
        return prediction
    
    def _fallback_prediction(self, weight_diff, silicon_energy, feed_type):
        """降级预测方法"""
        # 简单的线性组合
        base = weight_diff * 0.8 + silicon_energy * 0.7
        if feed_type == '首投':
            base *= 0.8
        return max(50, min(800, base))
    
    def _calculate_confidence(self, weight_diff, silicon_energy, feed_type):
        """计算预测置信度"""
        confidence = "High"
        
        # 检查输入范围
        if weight_diff < 50 or weight_diff > 800:
            confidence = "Low"
        elif silicon_energy < 50 or silicon_energy > 800:
            confidence = "Low"
        elif feed_type == '首投' and weight_diff > 400:
            confidence = "Medium"  # 首投大重量样本较少
        elif abs(weight_diff - silicon_energy) > 300:
            confidence = "Medium"  # 重量和能量差异过大
        
        return confidence

# 为了兼容性，创建别名
RealtimePredictor = V7ImprovedPredictor
