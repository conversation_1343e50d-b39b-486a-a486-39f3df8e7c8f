# 两次测试样本详细分析

## 📊 文件说明

### 1. training_test_samples_71_3_percent.csv
- **描述**: 训练时使用的测试集样本（获得71.3%准确率）
- **来源**: A01_A40_cycles__analysis.csv 文件的后20%样本
- **特点**: 
  - 来自特定时间段（2025-05-01 到 2025-07-06）
  - 仅包含10个特定设备（analoga01-10）
  - 数值分布偏向较小值
  - 首投样本比例较低

### 2. real_validation_samples_22_5_percent.csv  
- **描述**: 真实验证使用的样本（获得22.5%准确率）
- **来源**: 从17,134条真实记录中随机抽取200个样本
- **特点**:
  - 覆盖更广泛的时间范围
  - 包含多种设备类型
  - 数值分布更接近真实生产情况
  - 工艺类型分布更均衡

### 3. test_samples_comparison_summary.csv
- **描述**: 两个数据集的统计对比摘要
- **用途**: 快速了解两个数据集的主要差异

## 🔍 手动查看建议

### 重点关注的差异
1. **数值范围**: 对比weight_difference和vice_total_energy_kwh的分布
2. **设备类型**: 查看folder_name字段的差异
3. **工艺分布**: 对比feed_type的比例
4. **时间特征**: 查看start_time的时间范围差异

### 查看方法
1. 用Excel或其他表格软件打开CSV文件
2. 对数值列进行排序，观察分布差异
3. 使用数据透视表分析分类变量的分布
4. 对比两个文件中相似样本的预测结果差异

## 💡 预期发现
- 训练测试集的数值普遍较小
- 真实验证集的数值范围更广
- 设备类型存在显著差异
- 这些差异解释了性能差距的原因
