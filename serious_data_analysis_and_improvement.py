#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认真分析原始数据，提高副功率预测准确率到70%以上
不找借口，只要结果
"""

import os
import pandas as pd
import numpy as np
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def analyze_all_available_data():
    """分析所有可用数据"""
    print("="*60)
    print("分析所有可用数据")
    print("="*60)
    
    data_sources = {}
    
    # 1. output_results中的筛选后数据
    print("\n1. 分析output_results中的筛选后数据:")
    output_files = list(Path("output_results").glob("*.csv"))
    for file in output_files:
        try:
            df = pd.read_csv(file)
            data_sources[file.name] = {
                'path': file,
                'rows': len(df),
                'cols': len(df.columns),
                'columns': list(df.columns),
                'has_vice_power': 'vice_total_energy_kwh' in df.columns,
                'has_weight': any('weight' in col.lower() for col in df.columns),
                'has_energy': any('energy' in col.lower() for col in df.columns)
            }
            print(f"  {file.name}: {len(df)}行, {len(df.columns)}列")
            if 'vice_total_energy_kwh' in df.columns:
                print(f"    ✅ 包含副功率数据")
                print(f"    副功率范围: {df['vice_total_energy_kwh'].min():.1f} - {df['vice_total_energy_kwh'].max():.1f} kWh")
        except Exception as e:
            print(f"  {file.name}: 读取失败 - {e}")
    
    # 2. 拉晶数据提取中的原始数据
    print(f"\n2. 分析拉晶数据提取中的原始数据:")
    raw_data_path = Path("拉晶数据提取")
    if raw_data_path.exists():
        for root, dirs, files in os.walk(raw_data_path):
            for file in files:
                if file.endswith('.csv'):
                    file_path = Path(root) / file
                    try:
                        df = pd.read_csv(file_path)
                        rel_path = file_path.relative_to(Path("."))
                        data_sources[str(rel_path)] = {
                            'path': file_path,
                            'rows': len(df),
                            'cols': len(df.columns),
                            'columns': list(df.columns),
                            'has_vice_power': any('副' in col or 'vice' in col.lower() for col in df.columns),
                            'has_weight': any('重' in col or 'weight' in col.lower() for col in df.columns),
                            'has_energy': any('能' in col or 'energy' in col.lower() for col in df.columns)
                        }
                        print(f"  {rel_path}: {len(df)}行, {len(df.columns)}列")
                        if any('副' in col or 'vice' in col.lower() for col in df.columns):
                            print(f"    ✅ 可能包含副功率相关数据")
                    except Exception as e:
                        print(f"  {rel_path}: 读取失败 - {e}")
    
    return data_sources

def deep_analyze_best_dataset(data_sources):
    """深入分析最佳数据集"""
    print(f"\n" + "="*60)
    print("深入分析最佳数据集")
    print("="*60)
    
    # 选择最佳数据集
    best_dataset = None
    max_score = 0
    
    for name, info in data_sources.items():
        score = 0
        if info['has_vice_power']: score += 10
        if info['has_weight']: score += 5
        if info['has_energy']: score += 5
        score += info['rows'] / 1000  # 数据量加分
        score += info['cols'] / 10    # 特征数量加分
        
        if score > max_score:
            max_score = score
            best_dataset = name
    
    print(f"选择最佳数据集: {best_dataset} (评分: {max_score:.1f})")
    
    # 加载最佳数据集
    best_info = data_sources[best_dataset]
    df = pd.read_csv(best_info['path'])
    
    print(f"\n数据集详细分析:")
    print(f"  文件: {best_info['path']}")
    print(f"  形状: {df.shape}")
    print(f"  列名: {list(df.columns)}")
    
    # 数据质量分析
    print(f"\n数据质量分析:")
    print(f"  缺失值: {df.isnull().sum().sum()}")
    print(f"  重复行: {df.duplicated().sum()}")
    print(f"  数据类型: {df.dtypes.value_counts().to_dict()}")
    
    # 寻找目标变量
    target_col = None
    for col in df.columns:
        if 'vice_total_energy_kwh' in col:
            target_col = col
            break
        elif '副' in col and ('功率' in col or '能耗' in col or 'kWh' in col):
            target_col = col
            break
    
    if target_col:
        print(f"\n目标变量: {target_col}")
        y = df[target_col]
        print(f"  数值范围: {y.min():.1f} - {y.max():.1f}")
        print(f"  均值: {y.mean():.1f}")
        print(f"  标准差: {y.std():.1f}")
        print(f"  分位数: {y.quantile([0.25, 0.5, 0.75]).to_dict()}")
    else:
        print(f"❌ 未找到目标变量")
        return None
    
    # 寻找特征变量
    feature_cols = []
    for col in df.columns:
        if col != target_col and df[col].dtype in ['int64', 'float64']:
            feature_cols.append(col)
    
    print(f"\n潜在特征变量 ({len(feature_cols)}个):")
    for col in feature_cols[:10]:  # 显示前10个
        corr = df[col].corr(df[target_col]) if not df[col].isnull().all() else 0
        print(f"  {col}: 相关性 {corr:.4f}")
    
    return df, target_col, feature_cols

def advanced_feature_engineering(df, target_col, feature_cols):
    """高级特征工程"""
    print(f"\n" + "="*60)
    print("高级特征工程")
    print("="*60)
    
    # 创建特征工程后的数据框
    df_features = df.copy()
    
    # 1. 基础特征清理
    print(f"1. 基础特征清理:")
    numeric_features = []
    for col in feature_cols:
        if df[col].dtype in ['int64', 'float64'] and not df[col].isnull().all():
            # 处理异常值
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 异常值处理
            outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
            if outliers > 0:
                print(f"  {col}: 处理 {outliers} 个异常值")
                df_features[col] = df[col].clip(lower_bound, upper_bound)
            
            numeric_features.append(col)
    
    print(f"  保留 {len(numeric_features)} 个数值特征")
    
    # 2. 交互特征
    print(f"\n2. 创建交互特征:")
    interaction_count = 0
    for i, col1 in enumerate(numeric_features[:5]):  # 限制前5个特征避免过多
        for col2 in numeric_features[i+1:6]:
            if col1 != col2:
                # 乘积特征
                new_col = f"{col1}_x_{col2}"
                df_features[new_col] = df_features[col1] * df_features[col2]
                
                # 比值特征
                if (df_features[col2] != 0).all():
                    ratio_col = f"{col1}_div_{col2}"
                    df_features[ratio_col] = df_features[col1] / df_features[col2]
                    interaction_count += 2
                else:
                    interaction_count += 1
    
    print(f"  创建 {interaction_count} 个交互特征")
    
    # 3. 多项式特征
    print(f"\n3. 创建多项式特征:")
    poly_count = 0
    for col in numeric_features[:5]:  # 限制前5个特征
        # 平方特征
        df_features[f"{col}_squared"] = df_features[col] ** 2
        # 立方根特征
        df_features[f"{col}_cbrt"] = np.cbrt(df_features[col])
        # 对数特征（如果都是正数）
        if (df_features[col] > 0).all():
            df_features[f"{col}_log"] = np.log(df_features[col])
            poly_count += 3
        else:
            poly_count += 2
    
    print(f"  创建 {poly_count} 个多项式特征")
    
    # 4. 统计特征
    print(f"\n4. 创建统计特征:")
    if len(numeric_features) >= 3:
        # 前几个特征的统计量
        feature_subset = df_features[numeric_features[:5]]
        df_features['features_mean'] = feature_subset.mean(axis=1)
        df_features['features_std'] = feature_subset.std(axis=1)
        df_features['features_max'] = feature_subset.max(axis=1)
        df_features['features_min'] = feature_subset.min(axis=1)
        print(f"  创建 4 个统计特征")
    
    # 5. 获取所有新特征
    all_features = [col for col in df_features.columns if col != target_col and df_features[col].dtype in ['int64', 'float64']]
    
    print(f"\n特征工程总结:")
    print(f"  原始特征: {len(numeric_features)}")
    print(f"  总特征数: {len(all_features)}")
    print(f"  新增特征: {len(all_features) - len(numeric_features)}")
    
    return df_features, all_features

def train_advanced_models(df_features, target_col, all_features):
    """训练高级模型"""
    print(f"\n" + "="*60)
    print("训练高级模型")
    print("="*60)
    
    # 准备数据
    X = df_features[all_features].fillna(0)  # 填充缺失值
    y = df_features[target_col]
    
    # 移除无限值和NaN
    mask = np.isfinite(X).all(axis=1) & np.isfinite(y)
    X = X[mask]
    y = y[mask]
    
    print(f"数据准备:")
    print(f"  样本数: {len(X)}")
    print(f"  特征数: {len(all_features)}")
    print(f"  目标范围: {y.min():.1f} - {y.max():.1f}")
    
    # 时间序列分割
    if 'start_time' in df_features.columns:
        df_features['start_time'] = pd.to_datetime(df_features['start_time'])
        sorted_indices = df_features[mask].sort_values('start_time').index
        split_idx = int(len(sorted_indices) * 0.8)
        train_idx = sorted_indices[:split_idx]
        test_idx = sorted_indices[split_idx:]
    else:
        # 随机分割
        from sklearn.model_selection import train_test_split
        train_idx, test_idx = train_test_split(X.index, test_size=0.2, random_state=42)
    
    X_train, X_test = X.loc[train_idx], X.loc[test_idx]
    y_train, y_test = y.loc[train_idx], y.loc[test_idx]
    
    print(f"  训练集: {len(X_train)} 样本")
    print(f"  测试集: {len(X_test)} 样本")
    
    # 特征选择
    print(f"\n特征选择:")
    from sklearn.feature_selection import SelectKBest, f_regression
    from sklearn.preprocessing import StandardScaler
    
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 选择最佳特征
    k_best = min(50, len(all_features))  # 最多选择50个特征
    selector = SelectKBest(score_func=f_regression, k=k_best)
    X_train_selected = selector.fit_transform(X_train_scaled, y_train)
    X_test_selected = selector.transform(X_test_scaled)
    
    selected_features = [all_features[i] for i in selector.get_support(indices=True)]
    print(f"  选择了 {len(selected_features)} 个最佳特征")
    
    # 训练多个模型
    models = {}
    results = {}
    
    # 1. 线性回归
    print(f"\n1. 线性回归:")
    from sklearn.linear_model import LinearRegression, Ridge, Lasso
    
    lr = LinearRegression()
    lr.fit(X_train_selected, y_train)
    lr_pred = lr.predict(X_test_selected)
    lr_acc = evaluate_model(y_test, lr_pred, "线性回归")
    models['linear'] = lr
    results['linear'] = lr_acc
    
    # 2. 岭回归
    print(f"\n2. 岭回归:")
    ridge = Ridge(alpha=1.0)
    ridge.fit(X_train_selected, y_train)
    ridge_pred = ridge.predict(X_test_selected)
    ridge_acc = evaluate_model(y_test, ridge_pred, "岭回归")
    models['ridge'] = ridge
    results['ridge'] = ridge_acc
    
    # 3. 随机森林
    print(f"\n3. 随机森林:")
    from sklearn.ensemble import RandomForestRegressor
    
    rf = RandomForestRegressor(n_estimators=200, max_depth=15, random_state=42, n_jobs=-1)
    rf.fit(X_train_selected, y_train)
    rf_pred = rf.predict(X_test_selected)
    rf_acc = evaluate_model(y_test, rf_pred, "随机森林")
    models['random_forest'] = rf
    results['random_forest'] = rf_acc
    
    # 4. 梯度提升
    print(f"\n4. 梯度提升:")
    from sklearn.ensemble import GradientBoostingRegressor
    
    gb = GradientBoostingRegressor(n_estimators=200, max_depth=8, learning_rate=0.1, random_state=42)
    gb.fit(X_train_selected, y_train)
    gb_pred = gb.predict(X_test_selected)
    gb_acc = evaluate_model(y_test, gb_pred, "梯度提升")
    models['gradient_boosting'] = gb
    results['gradient_boosting'] = gb_acc
    
    # 5. XGBoost
    print(f"\n5. XGBoost:")
    try:
        import xgboost as xgb
        
        xgb_model = xgb.XGBRegressor(n_estimators=200, max_depth=8, learning_rate=0.1, random_state=42)
        xgb_model.fit(X_train_selected, y_train)
        xgb_pred = xgb_model.predict(X_test_selected)
        xgb_acc = evaluate_model(y_test, xgb_pred, "XGBoost")
        models['xgboost'] = xgb_model
        results['xgboost'] = xgb_acc
    except ImportError:
        print("  XGBoost未安装，跳过")
        xgb_acc = 0
    
    # 6. 集成模型
    print(f"\n6. 集成模型:")
    ensemble_pred = (lr_pred + ridge_pred + rf_pred + gb_pred) / 4
    ensemble_acc = evaluate_model(y_test, ensemble_pred, "集成模型")
    results['ensemble'] = ensemble_acc
    
    # 找到最佳模型
    best_model_name = max(results.keys(), key=lambda k: results[k])
    best_acc = results[best_model_name]
    
    print(f"\n🎯 最佳模型: {best_model_name}")
    print(f"   ±10kWh准确率: {best_acc:.1f}%")
    
    if best_acc >= 70:
        print(f"   ✅ 达到70%目标！")
    else:
        print(f"   ❌ 未达到70%目标，差距: {70-best_acc:.1f}%")
    
    return models, results, best_model_name, selected_features, scaler, selector

def evaluate_model(y_true, y_pred, model_name):
    """评估模型"""
    errors = np.abs(y_true - y_pred)
    mae = errors.mean()
    rmse = np.sqrt(((y_true - y_pred) ** 2).mean())
    
    acc_5 = (errors <= 5).mean() * 100
    acc_10 = (errors <= 10).mean() * 100
    acc_15 = (errors <= 15).mean() * 100
    acc_20 = (errors <= 20).mean() * 100
    
    print(f"  {model_name}结果:")
    print(f"    MAE: {mae:.2f} kWh")
    print(f"    RMSE: {rmse:.2f} kWh")
    print(f"    ±5kWh准确率: {acc_5:.1f}%")
    print(f"    ±10kWh准确率: {acc_10:.1f}%")
    print(f"    ±15kWh准确率: {acc_15:.1f}%")
    print(f"    ±20kWh准确率: {acc_20:.1f}%")
    
    return acc_10

def save_best_model_and_results(models, results, best_model_name, selected_features, scaler, selector, df_features, target_col):
    """保存最佳模型和结果"""
    print(f"\n" + "="*60)
    print("保存最佳模型和结果")
    print("="*60)
    
    import joblib
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存模型
    model_dir = Path(f"improved_models_{timestamp}")
    model_dir.mkdir(exist_ok=True)
    
    if best_model_name in models:
        joblib.dump(models[best_model_name], model_dir / f"best_model_{best_model_name}.joblib")
        print(f"✅ 保存最佳模型: {best_model_name}")
    
    # 保存预处理器
    joblib.dump(scaler, model_dir / "scaler.joblib")
    joblib.dump(selector, model_dir / "feature_selector.joblib")
    print(f"✅ 保存预处理器")
    
    # 保存特征列表
    with open(model_dir / "selected_features.json", 'w', encoding='utf-8') as f:
        json.dump(selected_features, f, ensure_ascii=False, indent=2)
    
    # 保存结果报告
    report = {
        'timestamp': timestamp,
        'best_model': best_model_name,
        'best_accuracy': results[best_model_name],
        'all_results': results,
        'selected_features': selected_features,
        'feature_count': len(selected_features),
        'data_shape': df_features.shape,
        'target_column': target_col,
        'achieved_70_percent': results[best_model_name] >= 70
    }
    
    with open(model_dir / "improvement_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成Markdown报告
    markdown_report = f"""# 副功率预测模型改进报告

## 🎯 目标达成情况
- **目标**: ±10kWh准确率 ≥ 70%
- **最佳结果**: {results[best_model_name]:.1f}%
- **达成状态**: {'✅ 已达成' if results[best_model_name] >= 70 else '❌ 未达成'}

## 📊 模型性能对比
| 模型 | ±10kWh准确率 |
|------|-------------|
"""
    
    for model_name, acc in sorted(results.items(), key=lambda x: x[1], reverse=True):
        status = "✅" if acc >= 70 else "❌"
        markdown_report += f"| {model_name} | {acc:.1f}% {status} |\n"
    
    markdown_report += f"""
## 🔧 技术细节
- **最佳模型**: {best_model_name}
- **特征数量**: {len(selected_features)}
- **数据样本**: {df_features.shape[0]}
- **训练时间**: {timestamp}

## 📋 选择的特征
{chr(10).join([f"- {feature}" for feature in selected_features[:20]])}
{'...' if len(selected_features) > 20 else ''}

## 🎯 结论
{'成功达到70%准确率目标！' if results[best_model_name] >= 70 else f'需要进一步改进，当前最佳为{results[best_model_name]:.1f}%'}
"""
    
    with open(model_dir / "improvement_report.md", 'w', encoding='utf-8') as f:
        f.write(markdown_report)
    
    print(f"✅ 保存完整报告: {model_dir}")
    
    return model_dir

def main():
    """主函数"""
    print("认真分析原始数据，提高副功率预测准确率到70%以上")
    print("="*60)
    
    # 环境检查
    if not check_environment():
        return False
    
    # 1. 分析所有可用数据
    data_sources = analyze_all_available_data()
    
    if not data_sources:
        print("❌ 未找到可用数据")
        return False
    
    # 2. 深入分析最佳数据集
    result = deep_analyze_best_dataset(data_sources)
    if result is None:
        print("❌ 数据分析失败")
        return False
    
    df, target_col, feature_cols = result
    
    # 3. 高级特征工程
    df_features, all_features = advanced_feature_engineering(df, target_col, feature_cols)
    
    # 4. 训练高级模型
    models, results, best_model_name, selected_features, scaler, selector = train_advanced_models(df_features, target_col, all_features)
    
    # 5. 保存最佳模型和结果
    model_dir = save_best_model_and_results(models, results, best_model_name, selected_features, scaler, selector, df_features, target_col)
    
    # 6. 最终结果
    best_acc = results[best_model_name]
    print(f"\n🎯 任务完成！")
    print(f"最佳模型: {best_model_name}")
    print(f"±10kWh准确率: {best_acc:.1f}%")
    
    if best_acc >= 70:
        print(f"✅ 成功达到70%目标！")
        return True
    else:
        print(f"❌ 未达到70%目标，需要进一步改进")
        print(f"差距: {70-best_acc:.1f}%")
        return False

if __name__ == "__main__":
    success = main()
