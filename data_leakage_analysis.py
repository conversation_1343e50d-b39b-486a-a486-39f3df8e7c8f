#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据泄露检查和实时特征重设计
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class DataLeakageAnalyzer:
    """数据泄露分析器"""
    
    def __init__(self):
        """初始化"""
        self.leakage_features = []
        self.realtime_features = []
        self.future_features = []
        
    def analyze_feature_leakage(self, df):
        """分析特征数据泄露问题"""
        print("=" * 60)
        print("数据泄露分析报告")
        print("=" * 60)
        
        # 分析每个特征的时间可获取性
        all_features = df.columns.tolist()
        
        # 1. 明确的未来信息特征（数据泄露）
        future_features = [
            'end_time',           # 结束时间 - 预测时未知
            'end_weight',         # 结束重量 - 预测时未知
            'end_temperature_celsius',  # 最终温度 - 预测时未知
            'duration_hours',     # 总持续时间 - 预测时未知
            'vice_total_energy_kwh',  # 目标变量本身
            'record_count',       # 总记录数 - 预测时未知
            'preheating_found'    # 预热发现 - 基于全过程分析
        ]
        
        # 2. 可能的数据泄露特征（基于全过程统计）
        potential_leakage = [
            'first_crystal_seeding_main_power_kw',  # 可能基于全过程统计
            'feed_number_1_records',  # 基于全过程统计
            'energy_efficiency_percent',  # 基于最终结果计算
        ]
        
        # 3. 实时可获取特征
        realtime_features = [
            'start_weight',       # 开始重量 - 预测时已知
            'weight_difference',  # 重量差异 - 基于计划投料量
            'silicon_thermal_energy_kwh',  # 硅热能 - 基于物理计算
            'feed_type',          # 工艺类型 - 预测时已知
            'cc_feed_number',     # 加料桶数 - 预测时已知
            'start_time',         # 开始时间 - 预测时已知
            'folder_name',        # 设备名称 - 预测时已知
        ]
        
        print("\n1. 数据泄露特征识别:")
        print("   明确的未来信息特征（严重数据泄露）:")
        for feature in future_features:
            if feature in all_features:
                print(f"     ❌ {feature} - 预测时刻无法获取")
        
        print("\n   潜在数据泄露特征:")
        for feature in potential_leakage:
            if feature in all_features:
                print(f"     ⚠️ {feature} - 可能基于全过程统计")
        
        print("\n2. 实时可获取特征:")
        for feature in realtime_features:
            if feature in all_features:
                print(f"     ✅ {feature} - 预测时刻可获取")
        
        self.future_features = [f for f in future_features if f in all_features]
        self.realtime_features = [f for f in realtime_features if f in all_features]
        
        return self.future_features, self.realtime_features
    
    def analyze_original_raw_data(self):
        """分析原始拉晶数据结构"""
        print("\n" + "=" * 60)
        print("原始拉晶数据结构分析")
        print("=" * 60)
        
        # 读取一个原始数据文件
        raw_file = "拉晶数据提取/analoga01/extracted_content_1.csv"
        try:
            raw_df = pd.read_csv(raw_file)
            print(f"\n原始数据文件: {raw_file}")
            print(f"数据形状: {raw_df.shape}")
            print(f"时间跨度: {raw_df['cc_time'].iloc[0]} 到 {raw_df['cc_time'].iloc[-1]}")
            
            # 分析实时可获取的字段
            realtime_fields = [
                'cc_time',                    # 时间戳
                'cc_main_heating_set',        # 主加热设定值
                'cc_main_heating_display',    # 主加热显示值
                'cc_vice_heating_set',        # 副加热设定值
                'cc_vice_heating_display',    # 副加热显示值
                'cc_crystal_weight',          # 当前晶体重量
                'cc_residue_weight',          # 剩余重量
                'cc_ccd_liquid_temperature',  # CCD液体温度
                'cc_infrared_liquid_temperature',  # 红外液体温度
                'cc_feed_number',             # 加料桶数
                'cc_process_decoding',        # 工艺状态
                'cc_main_heating_voltage',    # 主加热电压
                'cc_main_heating_current',    # 主加热电流
                'cc_vice_heating_voltage',    # 副加热电压
                'cc_vice_heating_current',    # 副加热电流
                'cc_power',                   # 功率
            ]
            
            print(f"\n实时可监测字段 (共{len(realtime_fields)}个):")
            available_fields = []
            for field in realtime_fields:
                if field in raw_df.columns:
                    available_fields.append(field)
                    sample_value = raw_df[field].iloc[0]
                    print(f"  ✅ {field}: {sample_value}")
                else:
                    print(f"  ❌ {field}: 字段不存在")
            
            print(f"\n可用于实时预测的字段: {len(available_fields)}个")
            return available_fields
            
        except Exception as e:
            print(f"❌ 无法读取原始数据文件: {e}")
            return []

class RealtimeFeatureEngineer:
    """实时特征工程器"""
    
    def __init__(self):
        """初始化"""
        self.scaler = StandardScaler()
        
    def create_realtime_features(self, df):
        """创建基于实时可获取数据的特征"""
        print("\n" + "=" * 60)
        print("实时特征工程设计")
        print("=" * 60)
        
        features_df = df.copy()
        
        # 1. 基础实时特征（预测时刻已知）
        print("\n1. 基础实时特征:")
        
        # 工艺类型编码
        features_df['is_first_cast'] = (features_df['feed_type'] == '首投').astype(int)
        print("   ✅ is_first_cast - 工艺类型编码")
        
        # 重量相关特征
        features_df['planned_weight_diff'] = features_df['weight_difference']  # 计划重量差异
        print("   ✅ planned_weight_diff - 计划重量差异")
        
        # 硅热能特征（基于物理计算）
        features_df['silicon_thermal_energy'] = features_df['silicon_thermal_energy_kwh']
        print("   ✅ silicon_thermal_energy - 硅热能需求")
        
        # 2. 物理学衍生特征（基于已知参数）
        print("\n2. 物理学衍生特征:")
        
        # 能量密度（基于计划参数）
        features_df['energy_density_planned'] = features_df['silicon_thermal_energy_kwh'] / (features_df['weight_difference'] + 1e-6)
        print("   ✅ energy_density_planned - 计划能量密度")
        
        # 理论效率（基于物理模型）
        features_df['theoretical_efficiency'] = features_df['silicon_thermal_energy_kwh'] / (features_df['weight_difference'] * 0.8 + 1e-6)
        print("   ✅ theoretical_efficiency - 理论效率")
        
        # 3. 设备相关特征
        print("\n3. 设备相关特征:")
        
        # 设备编码（基于folder_name）
        if 'folder_name' in features_df.columns:
            device_mapping = {name: idx for idx, name in enumerate(features_df['folder_name'].unique())}
            features_df['device_id'] = features_df['folder_name'].map(device_mapping)
            print(f"   ✅ device_id - 设备编码 ({len(device_mapping)}个设备)")
        
        # 4. 时间相关特征（基于开始时间）
        print("\n4. 时间相关特征:")
        
        if 'start_time' in features_df.columns:
            features_df['start_time'] = pd.to_datetime(features_df['start_time'])
            features_df['start_hour'] = features_df['start_time'].dt.hour
            features_df['start_day_of_week'] = features_df['start_time'].dt.dayofweek
            features_df['start_month'] = features_df['start_time'].dt.month
            print("   ✅ start_hour, start_day_of_week, start_month - 时间特征")
        
        # 5. 交互特征（基于实时可获取数据）
        print("\n5. 交互特征:")
        
        # 重量-能量交互
        features_df['weight_energy_interaction'] = features_df['weight_difference'] * features_df['silicon_thermal_energy_kwh'] / 1000
        print("   ✅ weight_energy_interaction - 重量能量交互")
        
        # 工艺-重量交互
        features_df['process_weight_interaction'] = features_df['is_first_cast'] * features_df['weight_difference']
        print("   ✅ process_weight_interaction - 工艺重量交互")
        
        # 6. 范围特征（基于经验阈值）
        print("\n6. 范围特征:")
        
        # 重量范围
        features_df['is_light_weight'] = (features_df['weight_difference'] < 150).astype(int)
        features_df['is_medium_weight'] = ((features_df['weight_difference'] >= 150) & (features_df['weight_difference'] < 400)).astype(int)
        features_df['is_heavy_weight'] = (features_df['weight_difference'] >= 400).astype(int)
        print("   ✅ weight_range_features - 重量范围特征")
        
        # 能量范围
        features_df['is_low_energy'] = (features_df['silicon_thermal_energy_kwh'] < 200).astype(int)
        features_df['is_high_energy'] = (features_df['silicon_thermal_energy_kwh'] >= 400).astype(int)
        print("   ✅ energy_range_features - 能量范围特征")
        
        # 选择最终特征
        realtime_feature_cols = [
            'is_first_cast', 'planned_weight_diff', 'silicon_thermal_energy',
            'energy_density_planned', 'theoretical_efficiency',
            'start_hour', 'start_day_of_week', 'start_month',
            'weight_energy_interaction', 'process_weight_interaction',
            'is_light_weight', 'is_medium_weight', 'is_heavy_weight',
            'is_low_energy', 'is_high_energy'
        ]
        
        # 添加设备特征（如果可用）
        if 'device_id' in features_df.columns:
            realtime_feature_cols.append('device_id')
        
        # 过滤存在的特征
        available_features = [col for col in realtime_feature_cols if col in features_df.columns]
        
        print(f"\n最终实时特征集: {len(available_features)}个特征")
        print("特征列表:", available_features)
        
        return features_df[available_features], available_features

class RealtimeVicePowerModel:
    """基于实时特征的副功率预测模型"""
    
    def __init__(self):
        """初始化"""
        self.model = RandomForestRegressor(
            n_estimators=200,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=3,
            random_state=42,
            n_jobs=-1
        )
        self.scaler = StandardScaler()
        self.feature_names = None
        self.is_trained = False
        
    def train(self, X, y):
        """训练模型"""
        print("\n" + "=" * 60)
        print("实时约束下的模型训练")
        print("=" * 60)
        
        # 数据预处理
        X_scaled = self.scaler.fit_transform(X.fillna(0))
        self.feature_names = X.columns.tolist()
        
        # 使用时间序列分割（避免数据泄露）
        tscv = TimeSeriesSplit(n_splits=5)
        cv_scores = []
        
        print(f"\n使用时间序列交叉验证 (5折):")
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X_scaled)):
            X_train_fold, X_val_fold = X_scaled[train_idx], X_scaled[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
            
            # 训练模型
            fold_model = RandomForestRegressor(
                n_estimators=200, max_depth=10, min_samples_split=5,
                min_samples_leaf=3, random_state=42, n_jobs=-1
            )
            fold_model.fit(X_train_fold, y_train_fold)
            
            # 验证
            y_pred_fold = fold_model.predict(X_val_fold)
            mae_fold = mean_absolute_error(y_val_fold, y_pred_fold)
            r2_fold = r2_score(y_val_fold, y_pred_fold)
            
            # 计算准确率
            errors_fold = np.abs(y_pred_fold - y_val_fold)
            acc_10_fold = (errors_fold <= 10).mean() * 100
            
            cv_scores.append({
                'fold': fold + 1,
                'mae': mae_fold,
                'r2': r2_fold,
                'acc_10': acc_10_fold
            })
            
            print(f"  Fold {fold+1}: MAE={mae_fold:.2f}, R²={r2_fold:.4f}, ±10kWh准确率={acc_10_fold:.1f}%")
        
        # 计算平均性能
        avg_mae = np.mean([s['mae'] for s in cv_scores])
        avg_r2 = np.mean([s['r2'] for s in cv_scores])
        avg_acc_10 = np.mean([s['acc_10'] for s in cv_scores])
        
        print(f"\n交叉验证平均性能:")
        print(f"  平均MAE: {avg_mae:.2f} kWh")
        print(f"  平均R²: {avg_r2:.4f}")
        print(f"  平均±10kWh准确率: {avg_acc_10:.1f}% (目标: 80%)")
        
        # 在全部数据上训练最终模型
        self.model.fit(X_scaled, y)
        self.is_trained = True
        
        # 特征重要性分析
        feature_importance = pd.DataFrame({
            'feature': self.feature_names,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n特征重要性排序:")
        for idx, row in feature_importance.head(10).iterrows():
            print(f"  {row['feature']}: {row['importance']:.4f}")
        
        return {
            'cv_scores': cv_scores,
            'avg_mae': avg_mae,
            'avg_r2': avg_r2,
            'avg_acc_10': avg_acc_10,
            'feature_importance': feature_importance
        }
    
    def evaluate_final_performance(self, X, y):
        """评估最终性能（使用时间序列分割）"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        print(f"\n最终性能评估（时间序列分割）:")
        
        # 使用最后20%作为测试集
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # 预测
        X_test_scaled = self.scaler.transform(X_test.fillna(0))
        y_pred = self.model.predict(X_test_scaled)
        
        # 计算指标
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        errors = np.abs(y_pred - y_test)
        acc_5 = (errors <= 5).mean() * 100
        acc_7 = (errors <= 7).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        print(f"  测试集大小: {len(y_test)} 样本")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  R²: {r2:.4f}")
        print(f"  ±5 kWh 准确率: {acc_5:.1f}%")
        print(f"  ±7 kWh 准确率: {acc_7:.1f}%")
        print(f"  ±10 kWh 准确率: {acc_10:.1f}% (目标: 80%)")
        print(f"  ±15 kWh 准确率: {acc_15:.1f}%")
        
        # 按工艺类型分析
        if 'is_first_cast' in X_test.columns:
            print(f"\n按工艺类型分析:")
            for process_type, process_name in [(1, '首投'), (0, '复投')]:
                mask = X_test['is_first_cast'] == process_type
                if mask.sum() > 0:
                    process_errors = errors[mask]
                    process_acc_10 = (process_errors <= 10).mean() * 100
                    process_mae = np.mean(process_errors)
                    print(f"  {process_name}: ±10kWh准确率={process_acc_10:.1f}%, MAE={process_mae:.2f}kWh")
        
        return {
            'mae': mae,
            'r2': r2,
            'acc_5': acc_5,
            'acc_7': acc_7,
            'acc_10': acc_10,
            'acc_15': acc_15
        }

def main():
    """主函数"""
    print("开始数据泄露检查和实时特征重设计...")
    
    # 1. 加载数据
    df = pd.read_csv('output_results/A01_A40_cycles__analysis.csv')
    
    # 2. 数据泄露分析
    analyzer = DataLeakageAnalyzer()
    future_features, realtime_features = analyzer.analyze_feature_leakage(df)
    
    # 3. 原始数据结构分析
    available_fields = analyzer.analyze_original_raw_data()
    
    # 4. 实时特征工程
    engineer = RealtimeFeatureEngineer()
    X_realtime, feature_names = engineer.create_realtime_features(df)
    y = df['vice_total_energy_kwh']
    
    # 5. 训练实时约束下的模型
    model = RealtimeVicePowerModel()
    training_results = model.train(X_realtime, y)
    
    # 6. 最终性能评估
    final_results = model.evaluate_final_performance(X_realtime, y)
    
    print(f"\n" + "=" * 60)
    print("数据泄露检查和修正完成")
    print("=" * 60)
    print(f"实时约束下±10kWh准确率: {final_results['acc_10']:.1f}% (目标: 80%)")
    
    if final_results['acc_10'] >= 80:
        print("✅ 已达到目标准确率")
    else:
        print(f"⚠️ 未达到目标，差距: {80 - final_results['acc_10']:.1f}%")
    
    return analyzer, engineer, model, training_results, final_results

if __name__ == "__main__":
    analyzer, engineer, model, training_results, final_results = main()
