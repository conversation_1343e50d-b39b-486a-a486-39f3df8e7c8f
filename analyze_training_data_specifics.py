#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析训练数据的具体特征和"特定条件"
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_training_data():
    """分析训练数据的具体特征"""
    print("🔍 分析训练数据的具体特征...")
    
    # 加载训练数据
    training_file = 'output_results/A01_A40_cycles__analysis.csv'
    df = pd.read_csv(training_file)
    
    print(f"训练数据基本信息:")
    print(f"  文件: {training_file}")
    print(f"  样本数: {len(df)}")
    print(f"  列数: {len(df.columns)}")
    print(f"  时间范围: {df['start_time'].min()} 到 {df['start_time'].max()}")
    
    # 分析数据来源
    print(f"\n数据来源分析:")
    folder_counts = df['folder_name'].value_counts()
    print(f"  设备数量: {len(folder_counts)}")
    print(f"  主要设备: {folder_counts.head()}")
    
    # 分析时间特征
    df['start_time'] = pd.to_datetime(df['start_time'])
    df['date'] = df['start_time'].dt.date
    df['hour'] = df['start_time'].dt.hour
    df['day_of_week'] = df['start_time'].dt.dayofweek
    
    print(f"\n时间特征分析:")
    print(f"  日期范围: {df['date'].min()} 到 {df['date'].max()}")
    print(f"  时间跨度: {(df['date'].max() - df['date'].min()).days} 天")
    
    date_counts = df['date'].value_counts().sort_index()
    print(f"  每日样本数: 最少{date_counts.min()}, 最多{date_counts.max()}, 平均{date_counts.mean():.1f}")
    
    # 分析工艺类型
    print(f"\n工艺类型分析:")
    feed_type_counts = df['feed_type'].value_counts()
    print(f"  首投: {feed_type_counts.get('首投', 0)} ({feed_type_counts.get('首投', 0)/len(df)*100:.1f}%)")
    print(f"  复投: {feed_type_counts.get('复投', 0)} ({feed_type_counts.get('复投', 0)/len(df)*100:.1f}%)")
    
    # 分析数值特征
    print(f"\n数值特征分析:")
    numeric_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
    
    for col in numeric_cols:
        if col in df.columns:
            print(f"  {col}:")
            print(f"    范围: {df[col].min():.1f} - {df[col].max():.1f}")
            print(f"    均值: {df[col].mean():.1f}")
            print(f"    标准差: {df[col].std():.1f}")
            print(f"    中位数: {df[col].median():.1f}")
    
    return df

def compare_with_random_sample():
    """与随机抽取样本对比"""
    print(f"\n📊 与随机抽取样本对比...")
    
    # 加载训练数据
    training_df = pd.read_csv('output_results/A01_A40_cycles__analysis.csv')
    
    # 加载全部数据
    all_data_file = 'output_results/all_folders_summary.csv'
    all_df = pd.read_csv(all_data_file)
    
    print(f"数据集对比:")
    print(f"  训练数据: {len(training_df)} 样本")
    print(f"  全部数据: {len(all_df)} 样本")
    print(f"  训练数据占比: {len(training_df)/len(all_df)*100:.1f}%")
    
    # 对比统计特征
    print(f"\n统计特征对比:")
    comparison_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
    
    print(f"{'特征':<25} {'训练数据均值':<15} {'全部数据均值':<15} {'差异':<10} {'训练/全部':<10}")
    print("-" * 80)
    
    for col in comparison_cols:
        if col in training_df.columns and col in all_df.columns:
            train_mean = training_df[col].mean()
            all_mean = all_df[col].mean()
            diff = train_mean - all_mean
            ratio = train_mean / all_mean
            
            print(f"{col:<25} {train_mean:<15.1f} {all_mean:<15.1f} {diff:<10.1f} {ratio:<10.2f}")
    
    # 分析时间分布差异
    training_df['start_time'] = pd.to_datetime(training_df['start_time'])
    all_df['start_time'] = pd.to_datetime(all_df['start_time'])
    
    print(f"\n时间分布对比:")
    print(f"  训练数据时间范围: {training_df['start_time'].min()} 到 {training_df['start_time'].max()}")
    print(f"  全部数据时间范围: {all_df['start_time'].min()} 到 {all_df['start_time'].max()}")
    
    # 分析设备分布差异
    print(f"\n设备分布对比:")
    train_devices = set(training_df['folder_name'].unique())
    all_devices = set(all_df['folder_name'].unique())
    
    print(f"  训练数据设备: {len(train_devices)} 个")
    print(f"  全部数据设备: {len(all_devices)} 个")
    print(f"  训练数据设备: {sorted(train_devices)}")
    
    # 分析工艺类型分布差异
    print(f"\n工艺类型分布对比:")
    train_feed = training_df['feed_type'].value_counts(normalize=True) * 100
    all_feed = all_df['feed_type'].value_counts(normalize=True) * 100
    
    print(f"  训练数据 - 首投: {train_feed.get('首投', 0):.1f}%, 复投: {train_feed.get('复投', 0):.1f}%")
    print(f"  全部数据 - 首投: {all_feed.get('首投', 0):.1f}%, 复投: {all_feed.get('复投', 0):.1f}%")
    
    return training_df, all_df

def identify_specific_conditions(training_df, all_df):
    """识别训练数据的特定条件"""
    print(f"\n🎯 识别训练数据的特定条件...")
    
    # 1. 时间条件
    training_df['start_time'] = pd.to_datetime(training_df['start_time'])
    all_df['start_time'] = pd.to_datetime(all_df['start_time'])
    
    train_start = training_df['start_time'].min()
    train_end = training_df['start_time'].max()
    
    print(f"特定条件1 - 时间范围限制:")
    print(f"  训练数据仅来自: {train_start.date()} 到 {train_end.date()}")
    print(f"  时间跨度: {(train_end - train_start).days} 天")
    
    # 2. 设备条件
    train_devices = set(training_df['folder_name'].unique())
    all_devices = set(all_df['folder_name'].unique())
    excluded_devices = all_devices - train_devices
    
    print(f"\n特定条件2 - 设备选择:")
    print(f"  训练数据仅包含设备: {sorted(train_devices)}")
    print(f"  排除的设备: {sorted(excluded_devices)}")
    
    # 3. 数据质量条件
    print(f"\n特定条件3 - 数据质量筛选:")
    
    # 检查是否有质量筛选
    train_duration = training_df['duration_hours'].describe()
    all_duration = all_df['duration_hours'].describe()
    
    print(f"  训练数据持续时间: {train_duration['min']:.1f}-{train_duration['max']:.1f}小时")
    print(f"  全部数据持续时间: {all_duration['min']:.1f}-{all_duration['max']:.1f}小时")
    
    # 4. 数值范围条件
    print(f"\n特定条件4 - 数值范围特征:")
    
    for col in ['weight_difference', 'vice_total_energy_kwh']:
        if col in training_df.columns:
            train_q25, train_q75 = training_df[col].quantile([0.25, 0.75])
            all_q25, all_q75 = all_df[col].quantile([0.25, 0.75])
            
            print(f"  {col}:")
            print(f"    训练数据四分位数: {train_q25:.1f} - {train_q75:.1f}")
            print(f"    全部数据四分位数: {all_q25:.1f} - {all_q75:.1f}")
    
    # 5. 文件名模式
    print(f"\n特定条件5 - 文件来源:")
    print(f"  训练数据来源文件: A01_A40_cycles__analysis.csv")
    print(f"  这表明训练数据仅来自A01-A40周期的特定分析")
    
    return {
        'time_range': (train_start, train_end),
        'devices': train_devices,
        'excluded_devices': excluded_devices,
        'file_source': 'A01_A40_cycles'
    }

def create_comparison_visualization(training_df, all_df):
    """创建对比可视化"""
    print(f"\n📊 创建对比可视化...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 重量分布对比
    axes[0, 0].hist(training_df['weight_difference'], bins=30, alpha=0.6, 
                   color='blue', label=f'训练数据 (n={len(training_df)})')
    axes[0, 0].hist(all_df['weight_difference'], bins=30, alpha=0.6, 
                   color='red', label=f'全部数据 (n={len(all_df)})')
    axes[0, 0].set_title('重量差异分布对比')
    axes[0, 0].set_xlabel('重量差异 (kg)')
    axes[0, 0].legend()
    
    # 副功率分布对比
    axes[0, 1].hist(training_df['vice_total_energy_kwh'], bins=30, alpha=0.6, 
                   color='blue', label='训练数据')
    axes[0, 1].hist(all_df['vice_total_energy_kwh'], bins=30, alpha=0.6, 
                   color='red', label='全部数据')
    axes[0, 1].set_title('副功率分布对比')
    axes[0, 1].set_xlabel('副功率 (kWh)')
    axes[0, 1].legend()
    
    # 时间分布对比
    training_df['hour'] = training_df['start_time'].dt.hour
    all_df['hour'] = all_df['start_time'].dt.hour
    
    train_hour_counts = training_df['hour'].value_counts().sort_index()
    all_hour_counts = all_df['hour'].value_counts().sort_index()
    
    axes[0, 2].plot(train_hour_counts.index, train_hour_counts.values, 'b-', label='训练数据')
    axes[0, 2].plot(all_hour_counts.index, all_hour_counts.values, 'r-', label='全部数据')
    axes[0, 2].set_title('小时分布对比')
    axes[0, 2].set_xlabel('小时')
    axes[0, 2].legend()
    
    # 设备分布对比
    train_device_counts = training_df['folder_name'].value_counts()
    all_device_counts = all_df['folder_name'].value_counts()
    
    # 只显示前10个设备
    axes[1, 0].bar(range(len(train_device_counts[:10])), train_device_counts[:10].values, 
                  alpha=0.6, color='blue', label='训练数据')
    axes[1, 0].set_title('设备分布对比（前10个）')
    axes[1, 0].set_xlabel('设备编号')
    axes[1, 0].legend()
    
    # 工艺类型对比
    train_feed = training_df['feed_type'].value_counts()
    all_feed = all_df['feed_type'].value_counts()
    
    x = ['首投', '复投']
    train_values = [train_feed.get('首投', 0), train_feed.get('复投', 0)]
    all_values = [all_feed.get('首投', 0), all_feed.get('复投', 0)]
    
    x_pos = np.arange(len(x))
    axes[1, 1].bar(x_pos - 0.2, train_values, 0.4, label='训练数据', color='blue', alpha=0.6)
    axes[1, 1].bar(x_pos + 0.2, all_values, 0.4, label='全部数据', color='red', alpha=0.6)
    axes[1, 1].set_title('工艺类型分布对比')
    axes[1, 1].set_xticks(x_pos)
    axes[1, 1].set_xticklabels(x)
    axes[1, 1].legend()
    
    # 散点图对比
    sample_train = training_df.sample(n=min(500, len(training_df)), random_state=42)
    sample_all = all_df.sample(n=min(500, len(all_df)), random_state=42)
    
    axes[1, 2].scatter(sample_train['weight_difference'], sample_train['vice_total_energy_kwh'], 
                      alpha=0.6, color='blue', label='训练数据', s=20)
    axes[1, 2].scatter(sample_all['weight_difference'], sample_all['vice_total_energy_kwh'], 
                      alpha=0.6, color='red', label='全部数据', s=20)
    axes[1, 2].set_title('重量 vs 副功率散点图')
    axes[1, 2].set_xlabel('重量差异 (kg)')
    axes[1, 2].set_ylabel('副功率 (kWh)')
    axes[1, 2].legend()
    
    plt.tight_layout()
    plt.savefig('training_vs_all_data_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 对比图已保存: training_vs_all_data_comparison.png")

def main():
    """主函数"""
    print("="*60)
    print("分析训练数据的特定条件")
    print("="*60)
    
    # 1. 分析训练数据特征
    training_df = analyze_training_data()
    
    # 2. 与随机抽取样本对比
    training_df, all_df = compare_with_random_sample()
    
    # 3. 识别特定条件
    conditions = identify_specific_conditions(training_df, all_df)
    
    # 4. 创建可视化对比
    create_comparison_visualization(training_df, all_df)
    
    # 5. 总结分析
    print(f"\n🎯 71.3%准确率的'特定条件'总结:")
    print(f"1. ⏰ 时间限制: 仅使用{conditions['time_range'][0].date()}到{conditions['time_range'][1].date()}的数据")
    print(f"2. 🏭 设备限制: 仅使用{len(conditions['devices'])}个特定设备的数据")
    print(f"3. 📁 文件限制: 仅使用A01-A40周期分析文件")
    print(f"4. 📊 数据质量: 可能经过了特定的质量筛选")
    print(f"5. 🔢 数值范围: 训练数据的数值分布与全部数据存在差异")
    
    print(f"\n💡 关键发现:")
    print(f"- 训练数据只占全部数据的{len(training_df)/len(all_df)*100:.1f}%")
    print(f"- 训练数据来自特定的时间段和设备")
    print(f"- 训练数据的统计特征与全部数据存在系统性差异")
    print(f"- 这解释了为什么71.3%的准确率无法在随机抽取的真实数据上复现")
    
    return True

if __name__ == "__main__":
    success = main()
