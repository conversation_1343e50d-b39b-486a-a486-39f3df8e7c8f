#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析训练数据vs真实数据的差异，找出71.3%准确率的真相
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def find_training_data():
    """查找训练数据"""
    print("🔍 查找训练数据...")
    
    # 查找可能的训练数据文件
    possible_files = []
    
    # 1. 查找output_results目录中的原始数据
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.csv') and ('output' in file.lower() or 'result' in file.lower()):
                possible_files.append(Path(root) / file)
    
    # 2. 查找训练相关的数据文件
    training_patterns = ['*train*.csv', '*data*.csv', '*dataset*.csv', '*features*.csv']
    for pattern in training_patterns:
        possible_files.extend(list(Path(".").glob(f"**/{pattern}")))
    
    print(f"找到可能的数据文件:")
    for i, file in enumerate(possible_files[:10]):  # 只显示前10个
        print(f"  {i+1}. {file}")
    
    return possible_files

def load_and_analyze_data_files(data_files):
    """加载和分析数据文件"""
    print("\n📊 分析数据文件...")
    
    datasets = {}
    
    for file_path in data_files[:5]:  # 分析前5个文件
        try:
            print(f"\n分析文件: {file_path}")
            df = pd.read_csv(file_path)
            
            print(f"  - 记录数: {len(df)}")
            print(f"  - 列数: {len(df.columns)}")
            print(f"  - 列名: {list(df.columns)[:10]}...")  # 只显示前10个列名
            
            # 检查是否包含关键列
            key_columns = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
            has_key_cols = all(col in df.columns for col in key_columns)
            
            if has_key_cols:
                print(f"  ✅ 包含关键列")
                
                # 分析数据分布
                print(f"  - 重量范围: {df['weight_difference'].min():.1f} - {df['weight_difference'].max():.1f}")
                print(f"  - 能量范围: {df['silicon_thermal_energy_kwh'].min():.1f} - {df['silicon_thermal_energy_kwh'].max():.1f}")
                print(f"  - 副功率范围: {df['vice_total_energy_kwh'].min():.1f} - {df['vice_total_energy_kwh'].max():.1f}")
                
                datasets[str(file_path)] = df
            else:
                print(f"  ❌ 缺少关键列")
                
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
    
    return datasets

def compare_data_distributions(datasets):
    """对比数据分布"""
    print("\n📈 对比数据分布...")
    
    if len(datasets) < 2:
        print("❌ 数据集不足，无法对比")
        return
    
    # 创建对比图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    
    for i, (name, df) in enumerate(list(datasets.items())[:5]):
        color = colors[i % len(colors)]
        label = f"数据集{i+1} (n={len(df)})"
        
        # 重量分布
        axes[0, 0].hist(df['weight_difference'], bins=30, alpha=0.5, 
                       color=color, label=label)
        
        # 能量分布
        axes[0, 1].hist(df['silicon_thermal_energy_kwh'], bins=30, alpha=0.5, 
                       color=color, label=label)
        
        # 副功率分布
        axes[0, 2].hist(df['vice_total_energy_kwh'], bins=30, alpha=0.5, 
                       color=color, label=label)
        
        # 重量vs副功率散点图
        sample_df = df.sample(n=min(500, len(df)), random_state=42)
        axes[1, 0].scatter(sample_df['weight_difference'], sample_df['vice_total_energy_kwh'], 
                          alpha=0.5, color=color, label=label, s=10)
        
        # 能量vs副功率散点图
        axes[1, 1].scatter(sample_df['silicon_thermal_energy_kwh'], sample_df['vice_total_energy_kwh'], 
                          alpha=0.5, color=color, label=label, s=10)
        
        # 重量vs能量散点图
        axes[1, 2].scatter(sample_df['weight_difference'], sample_df['silicon_thermal_energy_kwh'], 
                          alpha=0.5, color=color, label=label, s=10)
    
    # 设置标题和标签
    axes[0, 0].set_title('重量分布对比')
    axes[0, 0].set_xlabel('重量 (kg)')
    axes[0, 0].legend()
    
    axes[0, 1].set_title('能量分布对比')
    axes[0, 1].set_xlabel('硅热能 (kWh)')
    axes[0, 1].legend()
    
    axes[0, 2].set_title('副功率分布对比')
    axes[0, 2].set_xlabel('副功率 (kWh)')
    axes[0, 2].legend()
    
    axes[1, 0].set_title('重量 vs 副功率')
    axes[1, 0].set_xlabel('重量 (kg)')
    axes[1, 0].set_ylabel('副功率 (kWh)')
    axes[1, 0].legend()
    
    axes[1, 1].set_title('能量 vs 副功率')
    axes[1, 1].set_xlabel('硅热能 (kWh)')
    axes[1, 1].set_ylabel('副功率 (kWh)')
    axes[1, 1].legend()
    
    axes[1, 2].set_title('重量 vs 能量')
    axes[1, 2].set_xlabel('重量 (kg)')
    axes[1, 2].set_ylabel('硅热能 (kWh)')
    axes[1, 2].legend()
    
    plt.tight_layout()
    plt.savefig('data_distribution_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 数据分布对比图已保存: data_distribution_comparison.png")

def analyze_training_performance_claims():
    """分析训练性能声明"""
    print("\n🔍 分析71.3%准确率的来源...")
    
    # 查找可能包含性能报告的文件
    report_files = []
    
    # 查找markdown报告
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.md') and ('report' in file.lower() or 'summary' in file.lower()):
                report_files.append(Path(root) / file)
    
    # 查找JSON性能文件
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.json') and ('performance' in file.lower() or 'metrics' in file.lower()):
                report_files.append(Path(root) / file)
    
    print(f"找到可能的报告文件:")
    for file in report_files:
        print(f"  📄 {file}")
    
    # 分析报告内容
    for file_path in report_files:
        try:
            print(f"\n分析报告: {file_path}")
            
            if file_path.suffix == '.md':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找准确率相关信息
                if '71.3' in content or '71%' in content:
                    print("  🎯 找到71.3%准确率相关信息")
                    
                    # 提取相关段落
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if '71.3' in line or '71%' in line:
                            print(f"    第{i+1}行: {line.strip()}")
                            # 显示上下文
                            for j in range(max(0, i-2), min(len(lines), i+3)):
                                if j != i:
                                    print(f"    第{j+1}行: {lines[j].strip()}")
                            break
            
            elif file_path.suffix == '.json':
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 查找准确率信息
                def find_accuracy_in_dict(d, path=""):
                    for key, value in d.items():
                        current_path = f"{path}.{key}" if path else key
                        if isinstance(value, dict):
                            find_accuracy_in_dict(value, current_path)
                        elif isinstance(value, (int, float)):
                            if 70 <= value <= 75:
                                print(f"    {current_path}: {value}")
                        elif isinstance(value, str) and ('71.3' in value or '71%' in value):
                            print(f"    {current_path}: {value}")
                
                find_accuracy_in_dict(data)
                
        except Exception as e:
            print(f"  ❌ 分析失败: {e}")

def investigate_training_methodology():
    """调查训练方法"""
    print("\n🔬 调查训练方法...")
    
    # 查找训练脚本
    training_scripts = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.py') and any(keyword in file.lower() for keyword in 
                                          ['train', 'model', 'create', 'build']):
                training_scripts.append(Path(root) / file)
    
    print(f"找到可能的训练脚本:")
    for script in training_scripts[:10]:  # 只显示前10个
        print(f"  🐍 {script}")
    
    # 分析关键训练脚本
    key_scripts = [s for s in training_scripts if 'create' in s.name.lower() or 'train' in s.name.lower()]
    
    for script_path in key_scripts[:3]:  # 分析前3个关键脚本
        try:
            print(f"\n分析脚本: {script_path}")
            
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找关键信息
            if 'cross_val' in content or 'train_test_split' in content:
                print("  ✅ 发现交叉验证或训练测试分割")
            
            if 'accuracy' in content or '准确率' in content:
                print("  ✅ 发现准确率计算")
            
            if 'time_series' in content or '时间序列' in content:
                print("  ⚠️ 发现时间序列相关代码")
            
            if 'data_leakage' in content or '数据泄露' in content:
                print("  ⚠️ 发现数据泄露相关讨论")
            
            # 查找71.3相关的代码
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if '71.3' in line:
                    print(f"    第{i+1}行: {line.strip()}")
                    
        except Exception as e:
            print(f"  ❌ 分析失败: {e}")

def create_hypothesis_analysis():
    """创建假设分析"""
    print("\n💡 创建假设分析...")
    
    hypotheses = [
        {
            'hypothesis': '训练时使用了包含未来信息的特征',
            'evidence': '原始模型可能使用了47个特征，其中一些包含数据泄露',
            'likelihood': 'High'
        },
        {
            'hypothesis': '训练数据和真实数据分布不同',
            'evidence': '训练可能使用了特定时间段或特定条件的数据',
            'likelihood': 'High'
        },
        {
            'hypothesis': '交叉验证方法不当',
            'evidence': '可能使用了随机分割而非时间序列分割',
            'likelihood': 'Medium'
        },
        {
            'hypothesis': '过拟合问题',
            'evidence': '模型在训练数据上表现好，但泛化能力差',
            'likelihood': 'High'
        },
        {
            'hypothesis': '评估指标计算错误',
            'evidence': '可能在计算准确率时使用了错误的方法',
            'likelihood': 'Medium'
        }
    ]
    
    print("可能的原因分析:")
    for i, hyp in enumerate(hypotheses, 1):
        print(f"\n假设{i}: {hyp['hypothesis']}")
        print(f"  证据: {hyp['evidence']}")
        print(f"  可能性: {hyp['likelihood']}")

def main():
    """主函数"""
    print("="*60)
    print("分析训练数据vs真实数据差异")
    print("调查71.3%准确率的真相")
    print("="*60)
    
    # 1. 环境检查
    if not check_environment():
        return False
    
    # 2. 查找训练数据
    data_files = find_training_data()
    
    # 3. 加载和分析数据文件
    datasets = load_and_analyze_data_files(data_files)
    
    # 4. 对比数据分布
    if len(datasets) >= 2:
        compare_data_distributions(datasets)
    
    # 5. 分析训练性能声明
    analyze_training_performance_claims()
    
    # 6. 调查训练方法
    investigate_training_methodology()
    
    # 7. 创建假设分析
    create_hypothesis_analysis()
    
    print(f"\n🎯 结论:")
    print("71.3%的准确率可能来自以下原因:")
    print("1. ❌ 数据泄露: 训练时使用了包含未来信息的特征")
    print("2. ❌ 数据分布差异: 训练数据与真实应用数据分布不同")
    print("3. ❌ 验证方法不当: 使用了不适合时间序列的交叉验证")
    print("4. ❌ 过拟合: 模型记住了训练数据但泛化能力差")
    print("5. ❌ 评估错误: 可能在评估过程中存在计算错误")
    
    print(f"\n💡 真实情况:")
    print("- 真实数据验证显示±10kWh准确率仅为22.5%")
    print("- 这更接近模型的真实性能")
    print("- 71.3%的准确率很可能是训练过程中的错误结果")
    
    return True

if __name__ == "__main__":
    success = main()
