#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从头认真重新分析 - 不找借口，找真相
"""

import os
import pandas as pd
import numpy as np
import sys
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def deep_investigate_71_3_source():
    """深入调查71.3%的真实来源"""
    print("="*60)
    print("深入调查71.3%的真实来源")
    print("="*60)
    
    # 1. 检查性能报告的详细内容
    print("\n1. 检查性能报告详细内容:")
    perf_file = Path("realtime_vice_power_models/performance_report.json")
    if perf_file.exists():
        with open(perf_file, 'r', encoding='utf-8') as f:
            perf_data = json.load(f)
        
        print(f"性能报告完整内容:")
        print(json.dumps(perf_data, indent=2, ensure_ascii=False))
        
        # 检查是否有多个性能指标
        if 'final_performance' in perf_data:
            final_perf = perf_data['final_performance']
            print(f"\n最终性能数据:")
            for key, value in final_perf.items():
                print(f"  {key}: {value}")
    
    # 2. 检查训练脚本中71.3的具体计算
    print(f"\n2. 检查训练脚本中的具体计算:")
    training_script = Path("realtime_model_training.py")
    if training_script.exists():
        with open(training_script, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 查找所有包含71.3或类似数值的行
        lines = code.split('\n')
        for i, line in enumerate(lines):
            if '71' in line and ('.' in line or '%' in line):
                print(f"  第{i+1}行: {line.strip()}")
                # 显示上下文
                for j in range(max(0, i-3), min(len(lines), i+4)):
                    if j != i:
                        print(f"    {j+1}: {lines[j].strip()}")
                print()
    
    # 3. 检查是否有其他性能记录文件
    print(f"\n3. 查找所有性能相关文件:")
    for root, dirs, files in os.walk("."):
        for file in files:
            if any(keyword in file.lower() for keyword in ['performance', 'result', 'accuracy', 'report']):
                file_path = Path(root) / file
                print(f"  找到文件: {file_path}")
                
                # 如果是文本文件，检查是否包含71.3
                if file.endswith(('.txt', '.csv', '.json', '.md', '.log')):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        if '71.3' in content or '71.32' in content:
                            print(f"    ✅ 包含71.3相关内容")
                    except:
                        pass

def analyze_training_data_thoroughly():
    """彻底分析训练数据"""
    print(f"\n" + "="*60)
    print("彻底分析训练数据")
    print("="*60)
    
    # 加载训练数据
    training_data = Path("output_results/A01_A40_cycles__analysis.csv")
    if not training_data.exists():
        print(f"❌ 训练数据不存在")
        return None
    
    df = pd.read_csv(training_data)
    print(f"✅ 加载训练数据: {len(df)} 行, {len(df.columns)} 列")
    
    # 详细分析数据质量
    print(f"\n数据质量分析:")
    print(f"  列名: {list(df.columns)}")
    print(f"  数据类型: {df.dtypes.to_dict()}")
    print(f"  缺失值: {df.isnull().sum().to_dict()}")
    print(f"  重复行: {df.duplicated().sum()}")
    
    # 分析目标变量
    target_col = 'vice_total_energy_kwh'
    if target_col in df.columns:
        y = df[target_col]
        print(f"\n目标变量分析 ({target_col}):")
        print(f"  均值: {y.mean():.2f}")
        print(f"  标准差: {y.std():.2f}")
        print(f"  最小值: {y.min():.2f}")
        print(f"  最大值: {y.max():.2f}")
        print(f"  分位数: {y.quantile([0.25, 0.5, 0.75]).to_dict()}")
        
        # 检查异常值
        Q1 = y.quantile(0.25)
        Q3 = y.quantile(0.75)
        IQR = Q3 - Q1
        outliers = y[(y < Q1 - 1.5*IQR) | (y > Q3 + 1.5*IQR)]
        print(f"  异常值数量: {len(outliers)} ({len(outliers)/len(y)*100:.1f}%)")
    
    # 分析特征变量
    feature_cols = ['weight_difference', 'silicon_thermal_energy_kwh']
    for col in feature_cols:
        if col in df.columns:
            x = df[col]
            print(f"\n特征变量分析 ({col}):")
            print(f"  均值: {x.mean():.2f}")
            print(f"  标准差: {x.std():.2f}")
            print(f"  最小值: {x.min():.2f}")
            print(f"  最大值: {x.max():.2f}")
            
            # 与目标变量的相关性
            if target_col in df.columns:
                corr = df[col].corr(df[target_col])
                print(f"  与目标变量相关性: {corr:.4f}")
    
    # 时间序列分析
    if 'start_time' in df.columns:
        df['start_time'] = pd.to_datetime(df['start_time'])
        print(f"\n时间序列分析:")
        print(f"  时间范围: {df['start_time'].min()} 到 {df['start_time'].max()}")
        print(f"  时间跨度: {(df['start_time'].max() - df['start_time'].min()).days} 天")
        
        # 按时间排序
        df_sorted = df.sort_values('start_time')
        
        # 检查80/20分割点
        split_idx = int(len(df_sorted) * 0.8)
        train_end = df_sorted.iloc[split_idx-1]['start_time']
        test_start = df_sorted.iloc[split_idx]['start_time']
        
        print(f"  80/20分割点:")
        print(f"    训练结束: {train_end}")
        print(f"    测试开始: {test_start}")
        print(f"    训练样本: {split_idx}")
        print(f"    测试样本: {len(df_sorted) - split_idx}")
    
    return df

def rebuild_and_test_models():
    """重新构建和测试模型"""
    print(f"\n" + "="*60)
    print("重新构建和测试模型")
    print("="*60)
    
    # 加载数据
    df = analyze_training_data_thoroughly()
    if df is None:
        return None
    
    # 准备数据
    if 'start_time' in df.columns:
        df['start_time'] = pd.to_datetime(df['start_time'])
        df_sorted = df.sort_values('start_time')
    else:
        df_sorted = df
    
    # 80/20分割
    split_idx = int(len(df_sorted) * 0.8)
    train_df = df_sorted.iloc[:split_idx].copy()
    test_df = df_sorted.iloc[split_idx:].copy()
    
    print(f"\n数据分割:")
    print(f"  训练集: {len(train_df)} 样本")
    print(f"  测试集: {len(test_df)} 样本")
    
    # 准备特征和目标
    feature_cols = ['weight_difference', 'silicon_thermal_energy_kwh']
    target_col = 'vice_total_energy_kwh'
    
    X_train = train_df[feature_cols].values
    y_train = train_df[target_col].values
    X_test = test_df[feature_cols].values
    y_test = test_df[target_col].values
    
    print(f"\n特征和目标:")
    print(f"  特征: {feature_cols}")
    print(f"  目标: {target_col}")
    print(f"  训练特征形状: {X_train.shape}")
    print(f"  测试特征形状: {X_test.shape}")
    
    # 1. 简单线性回归
    print(f"\n1. 简单线性回归:")
    from sklearn.linear_model import LinearRegression
    from sklearn.metrics import mean_absolute_error, r2_score
    
    lr_model = LinearRegression()
    lr_model.fit(X_train, y_train)
    lr_pred = lr_model.predict(X_test)
    
    lr_errors = np.abs(lr_pred - y_test)
    lr_acc_5 = (lr_errors <= 5).mean() * 100
    lr_acc_10 = (lr_errors <= 10).mean() * 100
    lr_acc_15 = (lr_errors <= 15).mean() * 100
    lr_mae = lr_errors.mean()
    lr_r2 = r2_score(y_test, lr_pred)
    
    print(f"  系数: {lr_model.coef_}")
    print(f"  截距: {lr_model.intercept_}")
    print(f"  ±5kWh准确率: {lr_acc_5:.1f}%")
    print(f"  ±10kWh准确率: {lr_acc_10:.1f}%")
    print(f"  ±15kWh准确率: {lr_acc_15:.1f}%")
    print(f"  MAE: {lr_mae:.2f} kWh")
    print(f"  R²: {lr_r2:.4f}")
    
    # 2. 随机森林
    print(f"\n2. 随机森林:")
    from sklearn.ensemble import RandomForestRegressor
    
    rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
    rf_model.fit(X_train, y_train)
    rf_pred = rf_model.predict(X_test)
    
    rf_errors = np.abs(rf_pred - y_test)
    rf_acc_5 = (rf_errors <= 5).mean() * 100
    rf_acc_10 = (rf_errors <= 10).mean() * 100
    rf_acc_15 = (rf_errors <= 15).mean() * 100
    rf_mae = rf_errors.mean()
    rf_r2 = r2_score(y_test, rf_pred)
    
    print(f"  ±5kWh准确率: {rf_acc_5:.1f}%")
    print(f"  ±10kWh准确率: {rf_acc_10:.1f}%")
    print(f"  ±15kWh准确率: {rf_acc_15:.1f}%")
    print(f"  MAE: {rf_mae:.2f} kWh")
    print(f"  R²: {rf_r2:.4f}")
    print(f"  特征重要性: {rf_model.feature_importances_}")
    
    # 3. 梯度提升
    print(f"\n3. 梯度提升:")
    from sklearn.ensemble import GradientBoostingRegressor
    
    gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
    gb_model.fit(X_train, y_train)
    gb_pred = gb_model.predict(X_test)
    
    gb_errors = np.abs(gb_pred - y_test)
    gb_acc_5 = (gb_errors <= 5).mean() * 100
    gb_acc_10 = (gb_errors <= 10).mean() * 100
    gb_acc_15 = (gb_errors <= 15).mean() * 100
    gb_mae = gb_errors.mean()
    gb_r2 = r2_score(y_test, gb_pred)
    
    print(f"  ±5kWh准确率: {gb_acc_5:.1f}%")
    print(f"  ±10kWh准确率: {gb_acc_10:.1f}%")
    print(f"  ±15kWh准确率: {gb_acc_15:.1f}%")
    print(f"  MAE: {gb_mae:.2f} kWh")
    print(f"  R²: {gb_r2:.4f}")
    
    # 4. 集成模型
    print(f"\n4. 集成模型:")
    ensemble_pred = (lr_pred + rf_pred + gb_pred) / 3
    
    ens_errors = np.abs(ensemble_pred - y_test)
    ens_acc_5 = (ens_errors <= 5).mean() * 100
    ens_acc_10 = (ens_errors <= 10).mean() * 100
    ens_acc_15 = (ens_errors <= 15).mean() * 100
    ens_mae = ens_errors.mean()
    ens_r2 = r2_score(y_test, ensemble_pred)
    
    print(f"  ±5kWh准确率: {ens_acc_5:.1f}%")
    print(f"  ±10kWh准确率: {ens_acc_10:.1f}%")
    print(f"  ±15kWh准确率: {ens_acc_15:.1f}%")
    print(f"  MAE: {ens_mae:.2f} kWh")
    print(f"  R²: {ens_r2:.4f}")
    
    # 5. 检查是否能达到71.3%
    print(f"\n5. 71.3%达成情况:")
    results = {
        '线性回归': lr_acc_10,
        '随机森林': rf_acc_10,
        '梯度提升': gb_acc_10,
        '集成模型': ens_acc_10
    }
    
    for model_name, acc in results.items():
        close_to_71_3 = abs(acc - 71.3) < 5
        print(f"  {model_name}: {acc:.1f}% {'✅ 接近71.3%' if close_to_71_3 else '❌ 远离71.3%'}")
    
    # 保存详细结果
    detailed_results = {
        'data_info': {
            'total_samples': len(df_sorted),
            'train_samples': len(train_df),
            'test_samples': len(test_df),
            'features': feature_cols,
            'target': target_col
        },
        'model_results': {
            'linear_regression': {
                'acc_5': lr_acc_5, 'acc_10': lr_acc_10, 'acc_15': lr_acc_15,
                'mae': lr_mae, 'r2': lr_r2,
                'coefficients': lr_model.coef_.tolist(),
                'intercept': lr_model.intercept_
            },
            'random_forest': {
                'acc_5': rf_acc_5, 'acc_10': rf_acc_10, 'acc_15': rf_acc_15,
                'mae': rf_mae, 'r2': rf_r2,
                'feature_importance': rf_model.feature_importances_.tolist()
            },
            'gradient_boosting': {
                'acc_5': gb_acc_5, 'acc_10': gb_acc_10, 'acc_15': gb_acc_15,
                'mae': gb_mae, 'r2': gb_r2
            },
            'ensemble': {
                'acc_5': ens_acc_5, 'acc_10': ens_acc_10, 'acc_15': ens_acc_15,
                'mae': ens_mae, 'r2': ens_r2
            }
        }
    }
    
    with open('serious_reanalysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(detailed_results, f, ensure_ascii=False, indent=2)
    
    return detailed_results

def investigate_possible_71_3_scenarios():
    """调查可能达到71.3%的场景"""
    print(f"\n" + "="*60)
    print("调查可能达到71.3%的场景")
    print("="*60)
    
    # 加载数据
    training_data = Path("output_results/A01_A40_cycles__analysis.csv")
    if not training_data.exists():
        return None
    
    df = pd.read_csv(training_data)
    if 'start_time' in df.columns:
        df['start_time'] = pd.to_datetime(df['start_time'])
        df = df.sort_values('start_time')
    
    feature_cols = ['weight_difference', 'silicon_thermal_energy_kwh']
    target_col = 'vice_total_energy_kwh'
    
    # 场景1: 不同的分割比例
    print(f"\n场景1: 不同的分割比例")
    for split_ratio in [0.7, 0.75, 0.8, 0.85, 0.9]:
        split_idx = int(len(df) * split_ratio)
        test_df = df.iloc[split_idx:]
        
        if len(test_df) < 10:  # 测试集太小
            continue
        
        X_test = test_df[feature_cols].values
        y_test = test_df[target_col].values
        
        # 简单线性回归
        from sklearn.linear_model import LinearRegression
        train_df = df.iloc[:split_idx]
        X_train = train_df[feature_cols].values
        y_train = train_df[target_col].values
        
        model = LinearRegression()
        model.fit(X_train, y_train)
        pred = model.predict(X_test)
        
        errors = np.abs(pred - y_test)
        acc_10 = (errors <= 10).mean() * 100
        
        print(f"  {split_ratio*100:.0f}%训练/{(1-split_ratio)*100:.0f}%测试: {acc_10:.1f}% (测试样本:{len(test_df)})")
        
        if abs(acc_10 - 71.3) < 2:
            print(f"    ✅ 接近71.3%！")
    
    # 场景2: 不同的设备子集
    print(f"\n场景2: 不同的设备子集")
    if 'folder_name' in df.columns:
        devices = df['folder_name'].unique()
        print(f"  总设备数: {len(devices)}")
        
        for device in devices[:5]:  # 测试前5个设备
            device_df = df[df['folder_name'] == device]
            if len(device_df) < 20:  # 样本太少
                continue
            
            split_idx = int(len(device_df) * 0.8)
            train_df = device_df.iloc[:split_idx]
            test_df = device_df.iloc[split_idx:]
            
            if len(test_df) < 5:
                continue
            
            X_train = train_df[feature_cols].values
            y_train = train_df[target_col].values
            X_test = test_df[feature_cols].values
            y_test = test_df[target_col].values
            
            model = LinearRegression()
            model.fit(X_train, y_train)
            pred = model.predict(X_test)
            
            errors = np.abs(pred - y_test)
            acc_10 = (errors <= 10).mean() * 100
            
            print(f"  设备{device}: {acc_10:.1f}% (样本:{len(test_df)})")
            
            if abs(acc_10 - 71.3) < 5:
                print(f"    ✅ 接近71.3%！")
    
    # 场景3: 移除异常值
    print(f"\n场景3: 移除异常值")
    y = df[target_col]
    Q1 = y.quantile(0.25)
    Q3 = y.quantile(0.75)
    IQR = Q3 - Q1
    
    # 移除异常值
    clean_df = df[(y >= Q1 - 1.5*IQR) & (y <= Q3 + 1.5*IQR)]
    print(f"  原始样本: {len(df)}")
    print(f"  清理后样本: {len(clean_df)}")
    
    split_idx = int(len(clean_df) * 0.8)
    train_df = clean_df.iloc[:split_idx]
    test_df = clean_df.iloc[split_idx:]
    
    X_train = train_df[feature_cols].values
    y_train = train_df[target_col].values
    X_test = test_df[feature_cols].values
    y_test = test_df[target_col].values
    
    model = LinearRegression()
    model.fit(X_train, y_train)
    pred = model.predict(X_test)
    
    errors = np.abs(pred - y_test)
    acc_10 = (errors <= 10).mean() * 100
    
    print(f"  移除异常值后: {acc_10:.1f}%")
    
    if abs(acc_10 - 71.3) < 5:
        print(f"    ✅ 接近71.3%！")

def main():
    """主函数"""
    print("从头认真重新分析 - 不找借口，找真相")
    print("="*60)
    
    # 环境检查
    if not check_environment():
        return False
    
    # 1. 深入调查71.3%的来源
    deep_investigate_71_3_source()
    
    # 2. 彻底分析训练数据
    analyze_training_data_thoroughly()
    
    # 3. 重新构建和测试模型
    results = rebuild_and_test_models()
    
    # 4. 调查可能达到71.3%的场景
    investigate_possible_71_3_scenarios()
    
    print(f"\n🎯 重新分析完成！")
    print(f"详细结果已保存: serious_reanalysis_results.json")
    
    return True

if __name__ == "__main__":
    success = main()
