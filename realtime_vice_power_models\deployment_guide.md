
# 实时副功率预测模型部署指南

## 模型概述

- **模型类型**: 集成学习（随机森林 + 梯度提升 + 岭回归）
- **特征数量**: 26个实时可获取特征
- **数据泄露**: 无（严格验证）
- **性能指标**: ±10kWh准确率 71.3%

## 部署要求

### 环境要求
- Python 3.8+
- 必须使用 lj_env_1 环境
- 依赖包：scikit-learn, pandas, numpy, joblib

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 100MB以上

## 部署步骤

### 1. 环境准备
```bash
conda activate lj_env_1
pip install scikit-learn pandas numpy joblib
```

### 2. 模型文件部署
将以下文件复制到生产环境：
- ensemble_model.joblib
- feature_engineer.joblib
- scaler.joblib
- realtime_predictor.py

### 3. 实时数据接口
确保能够获取以下实时数据：
- 重量差异 (weight_difference)
- 硅热能需求 (silicon_thermal_energy_kwh)
- 工艺类型 (feed_type: '首投' 或 '复投')
- 设备名称 (folder_name)
- 开始时间 (start_time)

### 4. 预测调用
```python
from realtime_predictor import RealtimePredictor

# 初始化预测器
predictor = RealtimePredictor()

# 进行预测
result = predictor.predict(
    weight_difference=300.5,
    silicon_thermal_energy_kwh=250.8,
    feed_type='复投',
    folder_name='analoga01',
    start_time='2025-01-01 10:00:00'
)

print(f"预测副功率: {result['predicted_power']:.2f} kWh")
print(f"置信度: {result['confidence']}")
```

## 性能监控

### 关键指标监控
- 预测准确率（±10kWh范围）
- 平均绝对误差（MAE）
- 预测响应时间
- 模型可用性

### 预警阈值
- MAE > 15 kWh: 需要检查
- 准确率 < 60%: 需要重新训练
- 响应时间 > 1秒: 需要优化

## 维护建议

### 定期维护
- 每月评估模型性能
- 每季度收集新数据重训练
- 每年进行模型架构优化

### 数据质量监控
- 检查输入数据完整性
- 监控异常值和缺失值
- 验证数据时间一致性

## 故障排除

### 常见问题
1. **预测结果异常**
   - 检查输入数据格式
   - 验证特征值范围
   - 确认模型文件完整性

2. **性能下降**
   - 检查数据分布变化
   - 评估是否需要重新训练
   - 验证特征工程逻辑

3. **环境问题**
   - 确认使用lj_env_1环境
   - 检查依赖包版本
   - 验证文件路径正确性

## 联系信息

如有问题，请联系模型开发团队。
