# 副功率预测项目Conda环境配置文件
# 环境名称: lj_env_1
# 生成时间: 2025-01-31
# 使用方法: conda env create -f environment.yml

name: lj_env_1

channels:
  - conda-forge
  - defaults

dependencies:
  # Python版本
  - python=3.8
  
  # 系统工具
  - pip
  
  # 通过pip安装的包
  - pip:
    # 核心数据处理包
    - pandas==2.0.3
    - numpy==1.24.3
    
    # 机器学习核心包
    - scikit-learn==1.3.0
    - joblib==1.3.2
    
    # 高级机器学习算法
    - xgboost==1.7.6
    - lightgbm==4.0.0
    
    # 数据可视化（可选）
    - matplotlib>=3.5.0
    - seaborn>=0.11.0
    
    # 性能优化（可选）
    - numba>=0.56.0
    
    # 开发和调试工具（可选）
    - jupyter>=1.0.0
    - ipython>=8.0.0

# 使用说明:
# 1. 创建环境: conda env create -f environment.yml
# 2. 激活环境: conda activate lj_env_1
# 3. 验证安装: python quick_env_check.py
# 4. 运行训练: python realtime_model_training.py
