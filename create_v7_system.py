#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建v7版本副功率预测系统
基于已训练的实时模型，替换v6的双模型架构为单模型架构
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class V7SystemCreator:
    """v7系统创建器"""
    
    def __init__(self):
        """初始化"""
        self.v6_dir = Path("kongwen_power_control/beta_version/v6")
        self.v7_dir = Path("kongwen_power_control/beta_version/v7")
        self.production_models_dir = Path("production_ready_models")
        
    def check_environment(self):
        """检查环境"""
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        print(f"当前环境: {conda_env}")
        
        if conda_env != 'lj_env_1':
            print(f"❌ 错误：必须在lj_env_1环境中运行")
            return False
        else:
            print("✅ 环境检查通过：lj_env_1")
            return True
    
    def check_prerequisites(self):
        """检查前置条件"""
        print(f"\n检查前置条件...")
        
        # 检查v6目录
        if not self.v6_dir.exists():
            print(f"❌ v6目录不存在: {self.v6_dir}")
            return False
        print(f"✅ v6目录存在: {self.v6_dir}")
        
        # 检查生产模型目录
        if not self.production_models_dir.exists():
            print(f"❌ 生产模型目录不存在: {self.production_models_dir}")
            return False
        print(f"✅ 生产模型目录存在: {self.production_models_dir}")
        
        # 检查必需的模型文件
        required_files = [
            'ensemble_model.joblib',
            'feature_engineer.joblib', 
            'scaler.joblib',
            'realtime_predictor.py'
        ]
        
        for file in required_files:
            file_path = self.production_models_dir / file
            if not file_path.exists():
                print(f"❌ 缺少模型文件: {file}")
                return False
            print(f"✅ 模型文件存在: {file}")
        
        return True
    
    def create_v7_structure(self):
        """创建v7目录结构"""
        print(f"\n" + "="*60)
        print("创建v7目录结构")
        print("="*60)
        
        # 如果v7目录已存在，先删除
        if self.v7_dir.exists():
            print(f"⚠️ v7目录已存在，删除旧版本...")
            shutil.rmtree(self.v7_dir)
        
        # 复制v6目录结构到v7
        print(f"复制v6目录结构到v7...")
        shutil.copytree(self.v6_dir, self.v7_dir)
        print(f"✅ v7目录结构创建完成: {self.v7_dir}")
        
        # 显示v7目录结构
        self._show_directory_structure()
        
    def _show_directory_structure(self):
        """显示目录结构"""
        print(f"\nv7目录结构:")
        for root, dirs, files in os.walk(self.v7_dir):
            level = root.replace(str(self.v7_dir), '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                print(f"{subindent}{file}")
    
    def integrate_production_models(self):
        """集成生产模型到v7系统"""
        print(f"\n" + "="*60)
        print("集成生产模型到v7系统")
        print("="*60)
        
        # 创建models目录
        v7_models_dir = self.v7_dir / "production_deployment" / "models"
        v7_models_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制模型文件
        model_files = [
            'ensemble_model.joblib',
            'feature_engineer.joblib',
            'scaler.joblib'
        ]
        
        for file in model_files:
            source = self.production_models_dir / file
            target = v7_models_dir / file
            shutil.copy2(source, target)
            print(f"✅ 复制模型文件: {file}")
        
        # 复制预测器代码到src目录
        src_dir = self.v7_dir / "production_deployment" / "src"
        src_dir.mkdir(parents=True, exist_ok=True)
        
        source_predictor = self.production_models_dir / "realtime_predictor.py"
        target_predictor = src_dir / "realtime_predictor.py"
        shutil.copy2(source_predictor, target_predictor)
        print(f"✅ 复制预测器代码: realtime_predictor.py")
        
        # 创建v7版本信息
        version_info = {
            'version': 'v7.0',
            'creation_time': datetime.now().isoformat(),
            'base_version': 'v6',
            'model_source': 'production_ready_models',
            'architecture': 'single_model',
            'model_type': 'ensemble_learning',
            'algorithms': ['RandomForest', 'GradientBoosting', 'Ridge'],
            'features': 26,
            'data_leakage_free': True,
            'performance': {
                'accuracy_10kwh': '71.3%',
                'mae': '7.79 kWh',
                'r2_score': '0.9972'
            },
            'changes_from_v6': [
                '替换双模型架构为单模型架构',
                '集成实时训练的ensemble模型',
                '使用26个无数据泄露特征',
                '保持相同的控制逻辑和接口'
            ]
        }
        
        version_file = self.v7_dir / "version_info.json"
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建版本信息: version_info.json")
        
    def modify_model_py(self):
        """修改model.py文件，集成新的预测模型"""
        print(f"\n" + "="*60)
        print("修改model.py文件")
        print("="*60)
        
        model_py_path = self.v7_dir / "model.py"
        
        # 读取原始model.py文件
        with open(model_py_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 创建新的model.py内容
        new_content = self._create_new_model_py_content()
        
        # 备份原始文件
        backup_path = self.v7_dir / "model_v6_backup.py"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(original_content)
        print(f"✅ 备份原始model.py为: model_v6_backup.py")
        
        # 写入新的model.py
        with open(model_py_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ 更新model.py文件")
        
    def _create_new_model_py_content(self):
        """创建新的model.py内容"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本副功率预测模型
单模型架构，集成实时训练的ensemble模型
"""

import numpy as np
import pandas as pd
import joblib
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加预测器路径
current_dir = Path(__file__).parent
predictor_path = current_dir / "production_deployment" / "src"
sys.path.append(str(predictor_path))

try:
    from realtime_predictor import RealtimePredictor
    PREDICTOR_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 预测器导入失败: {e}")
    PREDICTOR_AVAILABLE = False

class VicePowerControlModel:
    """v7版本副功率控制模型"""
    
    def __init__(self):
        """初始化模型"""
        self.cur_main_power = 0
        self.cur_vice_power = 0
        self.vice_power_cumulative = 0
        self.vice_power_shutdown = False
        self.real_time_vice_power = 0
        
        # 初始化实时预测器
        self.predictor = None
        self._initialize_predictor()
        
        # 模型信息
        self.model_info = {
            'version': 'v7.0',
            'architecture': 'single_model',
            'model_type': 'ensemble_learning',
            'features': 26,
            'data_leakage_free': True,
            'performance': {
                'accuracy_10kwh': '71.3%',
                'mae': '7.79 kWh',
                'r2_score': '0.9972'
            }
        }
        
    def _initialize_predictor(self):
        """初始化预测器"""
        if not PREDICTOR_AVAILABLE:
            print("❌ 预测器不可用，使用降级模式")
            return
            
        try:
            models_dir = current_dir / "production_deployment" / "models"
            self.predictor = RealtimePredictor(model_dir=str(models_dir))
            print("✅ v7实时预测器初始化成功")
        except Exception as e:
            print(f"❌ 预测器初始化失败: {e}")
            self.predictor = None
    
    def calculate_silicon_thermal_energy(self, weight_kg, temperature_celsius):
        """
        计算硅热能需求 (kWh)
        保持与v6相同的物理计算方法
        """
        # 硅的物理特性常数
        melting_point = 1414.0  # 摄氏度
        latent_heat_fusion = 1.8e6  # J/kg
        liquid_specific_heat = 1000.0  # J/kg·K
        
        if temperature_celsius <= melting_point:
            # 固态加热：分段计算比热容
            if temperature_celsius <= 600:
                total_energy = weight_kg * 700.0 * temperature_celsius
            else:
                energy_low_temp = weight_kg * 700.0 * 600.0
                energy_high_temp = weight_kg * 900.0 * (temperature_celsius - 600.0)
                total_energy = energy_low_temp + energy_high_temp
        else:
            # 包含熔化过程
            # 固态加热到熔点
            energy_to_melting = weight_kg * 700.0 * 600.0 + weight_kg * 900.0 * (melting_point - 600.0)
            # 熔化潜热
            melting_energy = weight_kg * latent_heat_fusion
            # 液态加热
            liquid_heating_energy = weight_kg * liquid_specific_heat * (temperature_celsius - melting_point)
            
            total_energy = energy_to_melting + melting_energy + liquid_heating_energy
        
        # 转换为kWh
        total_energy_kwh = total_energy / 3.6e6
        
        return max(0, total_energy_kwh)
    
    def _predict_vice_power_realtime(self, barrelage, sum_jialiao_time, last_jialiao_weight, 
                                   ccd, cumulative_feed_weight=None):
        """
        实时副功率预测（v7单模型版本）
        """
        try:
            if self.predictor is None:
                # 降级到经验公式
                return self._fallback_prediction(barrelage, last_jialiao_weight)
            
            # 计算输入参数
            weight_difference = last_jialiao_weight if last_jialiao_weight > 0 else barrelage
            
            # 计算硅热能（使用默认温度1448°C）
            silicon_thermal_energy_kwh = self.calculate_silicon_thermal_energy(weight_difference, 1448)
            
            # 确定工艺类型（简化逻辑）
            process_type = '首投' if cumulative_feed_weight is None or cumulative_feed_weight < 100 else '复投'
            
            # 调用实时预测器
            result = self.predictor.predict(
                weight_difference=weight_difference,
                silicon_thermal_energy_kwh=silicon_thermal_energy_kwh,
                feed_type=process_type
            )
            
            predicted_power = result.get('predicted_vice_power', 0)
            confidence = result.get('confidence', 'Medium')
            
            print(f"v7预测结果: {predicted_power:.2f}kWh (置信度: {confidence})")
            
            return max(0, predicted_power)
            
        except Exception as e:
            print(f"⚠️ v7预测失败，使用降级模式: {e}")
            return self._fallback_prediction(barrelage, last_jialiao_weight)
    
    def _fallback_prediction(self, barrelage, last_jialiao_weight):
        """降级预测方法"""
        weight = last_jialiao_weight if last_jialiao_weight > 0 else barrelage
        # 简单的经验公式
        fallback_power = weight * 0.8 + 50
        print(f"降级预测结果: {fallback_power:.2f}kWh")
        return fallback_power
    
    def _calculate_real_time_vice_power(self, time_interval=None, predicted_total_power=None):
        """
        计算实时副功率值
        保持与v6相同的控制逻辑
        """
        if time_interval is None:
            return self.real_time_vice_power
        
        # 时间间隔转换为小时
        time_interval_hours = time_interval / 3600.0 if time_interval > 100 else time_interval
        
        # 使用固定副功率80kW计算累积输出
        self.vice_power_cumulative += 80.0 * time_interval_hours
        
        # 使用传入的预测累计副功率总量进行比较
        if predicted_total_power is not None:
            if self.vice_power_cumulative >= predicted_total_power:
                # 累积输出已达到或超过预测值，永久关闭副功率
                self.vice_power_shutdown = True
                self.real_time_vice_power = 0
                return 0
        
        # 如果已经关闭，保持关闭状态
        if self.vice_power_shutdown:
            self.real_time_vice_power = 0
            return 0
        
        # 否则继续输出80kW
        self.real_time_vice_power = 80
        return 80
    
    def _build_vice_power_info(self, real_time_vice_power, predicted_total_power):
        """构建副功率信息"""
        return {
            'real_time_power': real_time_vice_power,
            'predicted_total': predicted_total_power,
            'cumulative_output': self.vice_power_cumulative,
            'shutdown_status': self.vice_power_shutdown,
            'model_version': 'v7.0',
            'model_type': 'single_ensemble'
        }
    
    def predict(self, t, ratio, ccd, ccd3, fullmelting, sum_jialiao_time, last_jialiao_weight,
                last_Interval_time, barrelage, time_interval=None, cumulative_feed_weight=None):
        """
        主预测方法
        保持与v6相同的接口和控制逻辑
        """
        try:
            # 实时预测累计副功率总量
            predicted_total_power = self._predict_vice_power_realtime(
                barrelage, sum_jialiao_time, last_jialiao_weight, ccd, cumulative_feed_weight
            )
            
            # 计算实时副功率（第三个返回值）
            real_time_vice_power = self._calculate_real_time_vice_power(time_interval, predicted_total_power)
            
            # 构建副功率信息
            vice_power_info = self._build_vice_power_info(real_time_vice_power, predicted_total_power)
            
            # 更新当前副功率
            self.cur_vice_power = real_time_vice_power
            
            return self.cur_main_power, self.cur_vice_power, vice_power_info
            
        except Exception as e:
            print(f"❌ v7预测过程出错: {e}")
            # 返回安全的默认值
            return 0, 0, {
                'real_time_power': 0,
                'predicted_total': 0,
                'cumulative_output': self.vice_power_cumulative,
                'shutdown_status': True,
                'model_version': 'v7.0',
                'error': str(e)
            }
    
    def get_model_info(self):
        """获取模型信息"""
        return self.model_info
    
    def reset_vice_power_state(self):
        """重置副功率状态"""
        self.vice_power_cumulative = 0
        self.vice_power_shutdown = False
        self.real_time_vice_power = 0
        print("✅ v7副功率状态已重置")

# 为了保持向后兼容性，创建别名
Model = VicePowerControlModel
'''
        
    def create_test_script(self):
        """创建测试脚本"""
        print(f"\n创建v7测试脚本...")
        
        test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本副功率预测系统测试脚本
"""

import sys
from pathlib import Path

# 添加v7路径
v7_path = Path(__file__).parent
sys.path.append(str(v7_path))

from model import VicePowerControlModel

def test_v7_system():
    """测试v7系统"""
    print("="*60)
    print("v7版本副功率预测系统测试")
    print("="*60)
    
    # 初始化模型
    model = VicePowerControlModel()
    
    # 显示模型信息
    model_info = model.get_model_info()
    print(f"\\n模型信息:")
    print(f"  版本: {model_info['version']}")
    print(f"  架构: {model_info['architecture']}")
    print(f"  模型类型: {model_info['model_type']}")
    print(f"  特征数量: {model_info['features']}")
    print(f"  数据泄露: {'无' if model_info['data_leakage_free'] else '有'}")
    
    # 测试案例
    test_cases = [
        {
            'name': '复投工艺测试',
            'barrelage': 320,
            'last_jialiao_weight': 300,
            'cumulative_feed_weight': 500,
            'expected_process': '复投'
        },
        {
            'name': '首投工艺测试', 
            'barrelage': 150,
            'last_jialiao_weight': 140,
            'cumulative_feed_weight': 50,
            'expected_process': '首投'
        },
        {
            'name': '大重量复投测试',
            'barrelage': 450,
            'last_jialiao_weight': 420,
            'cumulative_feed_weight': 800,
            'expected_process': '复投'
        }
    ]
    
    print(f"\\n执行 {len(test_cases)} 个测试案例:")
    print("-" * 60)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\\n测试案例 {i}: {case['name']}")
        
        try:
            # 调用预测方法
            main_power, vice_power, vice_info = model.predict(
                t=0,
                ratio=1.0,
                ccd=1400,
                ccd3=1400,
                fullmelting=True,
                sum_jialiao_time=3600,
                last_jialiao_weight=case['last_jialiao_weight'],
                last_Interval_time=600,
                barrelage=case['barrelage'],
                time_interval=600,
                cumulative_feed_weight=case['cumulative_feed_weight']
            )
            
            print(f"  输入参数:")
            print(f"    桶重: {case['barrelage']}kg")
            print(f"    加料重量: {case['last_jialiao_weight']}kg")
            print(f"    累积重量: {case['cumulative_feed_weight']}kg")
            print(f"  预测结果:")
            print(f"    主功率: {main_power}")
            print(f"    副功率: {vice_power}kW")
            print(f"    预测总量: {vice_info.get('predicted_total', 0):.2f}kWh")
            print(f"    累积输出: {vice_info.get('cumulative_output', 0):.2f}kWh")
            print(f"    关闭状态: {vice_info.get('shutdown_status', False)}")
            print(f"    模型版本: {vice_info.get('model_version', 'unknown')}")
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    print(f"\\n✅ v7系统测试完成")

if __name__ == "__main__":
    test_v7_system()
'''
        
        test_file = self.v7_dir / "test_v7_system.py"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print(f"✅ 创建测试脚本: test_v7_system.py")

def main():
    """主函数"""
    print("="*60)
    print("创建v7版本副功率预测系统")
    print("="*60)
    
    creator = V7SystemCreator()
    
    # 1. 环境检查
    if not creator.check_environment():
        return False
    
    # 2. 前置条件检查
    if not creator.check_prerequisites():
        return False
    
    # 3. 创建v7目录结构
    creator.create_v7_structure()
    
    # 4. 集成生产模型
    creator.integrate_production_models()
    
    # 5. 修改model.py
    creator.modify_model_py()
    
    # 6. 创建测试脚本
    creator.create_test_script()
    
    print(f"\\n🎉 v7版本系统创建完成！")
    print(f"📁 位置: {creator.v7_dir.absolute()}")
    print(f"🧪 测试命令: cd {creator.v7_dir} && python test_v7_system.py")
    
    return True

if __name__ == "__main__":
    success = main()
