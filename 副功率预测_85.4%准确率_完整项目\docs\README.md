# 副功率预测项目 - 85.4%准确率

## 🎯 项目概述

本项目成功实现了工业硅拉晶生产过程中副功率的高精度预测，达到85.4%的±10kWh准确率，超出70%目标15.4%。

## 📊 核心成果

- **最佳准确率**: 85.4% (±10kWh范围)
- **平均绝对误差**: 7.96 kWh
- **最佳模型**: 支持向量回归 (SVR)
- **数据规模**: 2,119个真实生产样本
- **特征数量**: 30个优选特征

## 📁 项目结构

```
副功率预测_85.4%准确率_完整项目/
├── models/                    # 模型文件
│   ├── best_model_svr.joblib     # 最佳SVR模型
│   ├── scaler.joblib             # 标准化器
│   ├── feature_selector.joblib   # 特征选择器
│   └── results.json              # 模型结果
├── code/                      # 代码文件
│   ├── focused_improvement_analysis.py  # 核心训练代码
│   └── ...                       # 其他分析代码
├── data/                      # 数据文件
│   ├── all_folders_summary.csv   # 完整训练数据
│   ├── test_dataset_424_samples.csv  # 测试数据集
│   └── ...                       # 其他数据文件
├── results/                   # 结果文件
│   ├── performance_report.md     # 性能报告
│   ├── task_completion_report.md # 任务完成报告
│   └── ...                       # 其他分析报告
└── docs/                      # 文档
    ├── README.md                 # 本文件
    ├── model_usage_guide.md      # 模型使用指南
    └── reproduction_guide.md     # 复现指南
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- scikit-learn
- pandas
- numpy
- joblib

### 模型使用
```python
import joblib
import pandas as pd
import numpy as np

# 加载模型和预处理器
model = joblib.load('models/best_model_svr.joblib')
scaler = joblib.load('models/scaler.joblib')
selector = joblib.load('models/feature_selector.joblib')

# 预测新数据
# new_data = pd.DataFrame(...)  # 您的新数据
# predictions = model.predict(selector.transform(scaler.transform(new_data)))
```

## 📈 性能指标

| 指标 | 数值 |
|------|------|
| ±5kWh准确率 | 81.6% |
| ±10kWh准确率 | **85.4%** |
| ±15kWh准确率 | 88.0% |
| ±20kWh准确率 | 89.4% |
| 平均绝对误差 | 7.96 kWh |
| 均方根误差 | 21.60 kWh |

## 🔧 技术特点

1. **多维特征工程**: 从12个基础特征扩展到32个工程特征
2. **物理约束建模**: 基于拉晶工艺物理原理的特征构建
3. **严格验证**: 时间序列分割避免数据泄露
4. **模型优化**: 支持向量回归参数精细调优

## 📞 联系信息

如有问题请参考docs目录下的详细文档或联系项目维护者。
