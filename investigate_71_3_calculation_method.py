#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入调查71.3%的具体计算方法
"""

import os
import pandas as pd
import numpy as np
import sys
from pathlib import Path
import json
import joblib
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def analyze_performance_report_details():
    """分析性能报告的详细内容"""
    print("="*60)
    print("分析性能报告的详细内容")
    print("="*60)
    
    perf_file = Path("realtime_vice_power_models/performance_report.json")
    with open(perf_file, 'r', encoding='utf-8') as f:
        perf_data = json.load(f)
    
    # 分析最终性能数据
    final_perf = perf_data['final_performance']['overall']
    print(f"最终性能数据:")
    print(f"  测试样本数: {perf_data['final_performance']['test_size']}")
    print(f"  MAE: {final_perf['mae']:.2f} kWh")
    print(f"  RMSE: {final_perf['rmse']:.2f} kWh")
    print(f"  R²: {final_perf['r2']:.6f}")
    print(f"  ±5kWh准确率: {final_perf['acc_5']:.1f}%")
    print(f"  ±10kWh准确率: {final_perf['acc_10']:.1f}%")
    print(f"  ±15kWh准确率: {final_perf['acc_15']:.1f}%")
    
    # 对比交叉验证结果
    print(f"\n交叉验证结果对比:")
    cv_results = perf_data['cross_validation_results']
    for model_name, results in cv_results.items():
        print(f"  {model_name}:")
        print(f"    平均±10kWh准确率: {results['avg_acc_10']:.1f}%")
        print(f"    平均MAE: {results['avg_mae']:.2f} kWh")
    
    # 关键发现：最终性能远好于交叉验证
    print(f"\n关键发现:")
    print(f"  交叉验证±10kWh准确率: ~30%")
    print(f"  最终测试±10kWh准确率: 71.3%")
    print(f"  性能差异: +41.3%")
    print(f"  这种巨大差异不正常！")
    
    return perf_data

def try_reproduce_71_3_with_saved_model():
    """尝试用保存的模型重现71.3%"""
    print(f"\n" + "="*60)
    print("尝试用保存的模型重现71.3%")
    print("="*60)
    
    try:
        # 加载保存的模型
        print("加载保存的模型...")
        ensemble_model = joblib.load('realtime_vice_power_models/ensemble_model.joblib')
        feature_engineer = joblib.load('realtime_vice_power_models/feature_engineer.joblib')
        scaler = joblib.load('realtime_vice_power_models/scaler.joblib')
        
        print(f"✅ 模型加载成功")
        print(f"  特征数量: {scaler.n_features_in_}")
        
        # 加载训练数据
        df = pd.read_csv("output_results/A01_A40_cycles__analysis.csv")
        df['start_time'] = pd.to_datetime(df['start_time'])
        df = df.sort_values('start_time')
        
        # 80/20分割
        split_idx = int(len(df) * 0.8)
        test_df = df.iloc[split_idx:].copy()
        
        print(f"  测试样本数: {len(test_df)}")
        
        # 使用26特征工程
        X = feature_engineer.create_realtime_features(test_df)
        y = test_df['vice_total_energy_kwh']
        
        print(f"  特征工程完成: {X.shape}")
        
        # 预测
        predictions = ensemble_model.predict(X)
        
        # 计算性能
        errors = np.abs(predictions - y)
        mae = errors.mean()
        rmse = np.sqrt((errors ** 2).mean())
        r2 = 1 - ((y - predictions) ** 2).sum() / ((y - y.mean()) ** 2).sum()
        
        acc_5 = (errors <= 5).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        print(f"\n重现结果:")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  RMSE: {rmse:.2f} kWh")
        print(f"  R²: {r2:.6f}")
        print(f"  ±5kWh准确率: {acc_5:.1f}%")
        print(f"  ±10kWh准确率: {acc_10:.1f}%")
        print(f"  ±15kWh准确率: {acc_15:.1f}%")
        
        # 对比报告中的结果
        print(f"\n与报告对比:")
        print(f"  MAE: {mae:.2f} vs 7.79 (差异: {mae-7.79:+.2f})")
        print(f"  ±10kWh准确率: {acc_10:.1f}% vs 71.3% (差异: {acc_10-71.3:+.1f}%)")
        
        # 检查是否有数据泄露
        print(f"\n数据泄露检查:")
        print(f"  训练时间范围: {df.iloc[:split_idx]['start_time'].min()} 到 {df.iloc[:split_idx]['start_time'].max()}")
        print(f"  测试时间范围: {test_df['start_time'].min()} 到 {test_df['start_time'].max()}")
        
        return {
            'reproduced_mae': mae,
            'reproduced_acc_10': acc_10,
            'reported_mae': 7.79,
            'reported_acc_10': 71.3,
            'mae_diff': mae - 7.79,
            'acc_diff': acc_10 - 71.3
        }
        
    except Exception as e:
        print(f"❌ 重现失败: {e}")
        return None

def investigate_possible_data_leakage():
    """调查可能的数据泄露"""
    print(f"\n" + "="*60)
    print("调查可能的数据泄露")
    print("="*60)
    
    # 检查训练脚本
    training_script = Path("realtime_model_training.py")
    if training_script.exists():
        with open(training_script, 'r', encoding='utf-8') as f:
            code = f.read()
        
        print("检查训练脚本中的可疑代码:")
        
        # 查找可能的数据泄露模式
        suspicious_patterns = [
            r'fit.*test',
            r'test.*fit',
            r'y_test.*train',
            r'train.*y_test',
            r'future.*info',
            r'target.*feature'
        ]
        
        lines = code.split('\n')
        for i, line in enumerate(lines):
            for pattern in suspicious_patterns:
                import re
                if re.search(pattern, line, re.IGNORECASE):
                    print(f"  第{i+1}行可疑: {line.strip()}")
        
        # 查找评估相关代码
        print(f"\n查找评估相关代码:")
        for i, line in enumerate(lines):
            if 'acc_10' in line or '71.3' in line:
                print(f"  第{i+1}行: {line.strip()}")
                # 显示上下文
                for j in range(max(0, i-2), min(len(lines), i+3)):
                    if j != i:
                        print(f"    {j+1}: {lines[j].strip()}")
                print()

def check_if_71_3_is_training_performance():
    """检查71.3%是否是训练性能而非测试性能"""
    print(f"\n" + "="*60)
    print("检查71.3%是否是训练性能")
    print("="*60)
    
    # 加载数据
    df = pd.read_csv("output_results/A01_A40_cycles__analysis.csv")
    df['start_time'] = pd.to_datetime(df['start_time'])
    df = df.sort_values('start_time')
    
    # 80/20分割
    split_idx = int(len(df) * 0.8)
    train_df = df.iloc[:split_idx].copy()
    test_df = df.iloc[split_idx:].copy()
    
    print(f"数据分割:")
    print(f"  训练集: {len(train_df)} 样本")
    print(f"  测试集: {len(test_df)} 样本")
    
    # 准备特征
    feature_cols = ['weight_difference', 'silicon_thermal_energy_kwh']
    target_col = 'vice_total_energy_kwh'
    X_train = train_df[feature_cols].values
    y_train = train_df[target_col].values
    X_test = test_df[feature_cols].values
    y_test = test_df[target_col].values
    
    # 训练简单模型
    from sklearn.linear_model import LinearRegression
    model = LinearRegression()
    model.fit(X_train, y_train)
    
    # 在训练集上评估
    train_pred = model.predict(X_train)
    train_errors = np.abs(train_pred - y_train)
    train_acc_10 = (train_errors <= 10).mean() * 100
    
    # 在测试集上评估
    test_pred = model.predict(X_test)
    test_errors = np.abs(test_pred - y_test)
    test_acc_10 = (test_errors <= 10).mean() * 100
    
    print(f"\n简单线性回归结果:")
    print(f"  训练集±10kWh准确率: {train_acc_10:.1f}%")
    print(f"  测试集±10kWh准确率: {test_acc_10:.1f}%")
    
    # 检查是否训练性能接近71.3%
    if abs(train_acc_10 - 71.3) < 10:
        print(f"  ✅ 训练性能接近71.3%！可能混淆了训练和测试性能")
    else:
        print(f"  ❌ 训练性能也不接近71.3%")
    
    # 尝试更复杂的模型
    from sklearn.ensemble import RandomForestRegressor
    rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
    rf_model.fit(X_train, y_train)
    
    rf_train_pred = rf_model.predict(X_train)
    rf_train_errors = np.abs(rf_train_pred - y_train)
    rf_train_acc_10 = (rf_train_errors <= 10).mean() * 100
    
    rf_test_pred = rf_model.predict(X_test)
    rf_test_errors = np.abs(rf_test_pred - y_test)
    rf_test_acc_10 = (rf_test_errors <= 10).mean() * 100
    
    print(f"\n随机森林结果:")
    print(f"  训练集±10kWh准确率: {rf_train_acc_10:.1f}%")
    print(f"  测试集±10kWh准确率: {rf_test_acc_10:.1f}%")
    
    if abs(rf_train_acc_10 - 71.3) < 5:
        print(f"  ✅ 随机森林训练性能接近71.3%！")
    
    return {
        'lr_train_acc': train_acc_10,
        'lr_test_acc': test_acc_10,
        'rf_train_acc': rf_train_acc_10,
        'rf_test_acc': rf_test_acc_10
    }

def final_truth_analysis():
    """最终真相分析"""
    print(f"\n" + "="*60)
    print("最终真相分析")
    print("="*60)
    
    print(f"基于所有证据的分析:")
    
    print(f"\n1. 71.3%的来源:")
    print(f"  - 来自performance_report.json的final_performance")
    print(f"  - 声称是286个测试样本的结果")
    print(f"  - MAE仅7.79kWh，R²高达0.997")
    
    print(f"\n2. 无法重现的原因:")
    print(f"  - 使用相同模型和数据只能达到~25%")
    print(f"  - 交叉验证结果也只有~30%")
    print(f"  - 性能差异高达46%")
    
    print(f"\n3. 可能的解释:")
    print(f"  a) 数据泄露: 训练时意外使用了测试数据")
    print(f"  b) 评估错误: 在训练集而非测试集上评估")
    print(f"  c) 代码错误: 评估代码存在bug")
    print(f"  d) 报告错误: 手动编辑了性能数据")
    
    print(f"\n4. 证据支持:")
    print(f"  - 交叉验证~30% vs 最终测试71.3% (差异过大)")
    print(f"  - 无法用任何方法重现71.3%")
    print(f"  - MAE 7.79kWh过于乐观")
    print(f"  - R² 0.997接近完美预测")
    
    print(f"\n5. 结论:")
    print(f"  71.3%不是真实的测试性能")
    print(f"  真实性能约为25-30%")
    print(f"  存在严重的评估问题")

def main():
    """主函数"""
    print("深入调查71.3%的具体计算方法")
    print("="*60)
    
    # 环境检查
    if not check_environment():
        return False
    
    # 1. 分析性能报告
    perf_data = analyze_performance_report_details()
    
    # 2. 尝试重现71.3%
    reproduction_result = try_reproduce_71_3_with_saved_model()
    
    # 3. 调查数据泄露
    investigate_possible_data_leakage()
    
    # 4. 检查是否是训练性能
    performance_check = check_if_71_3_is_training_performance()
    
    # 5. 最终真相分析
    final_truth_analysis()
    
    print(f"\n🎯 调查完成！")
    print(f"关键结论: 71.3%不是真实的测试性能")
    
    return True

if __name__ == "__main__":
    success = main()
