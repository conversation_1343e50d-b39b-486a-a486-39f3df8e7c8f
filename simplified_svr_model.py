#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的SVR副功率预测模型
只使用重量差异和硅热能两个输入特征
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import joblib
from pathlib import Path
from datetime import datetime
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class SimplifiedSVRModel:
    """简化的SVR模型类 - 只使用重量差异和硅热能"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.feature_names = ['weight_difference', 'silicon_thermal_energy_kwh']
        self.is_trained = False
        
    def prepare_features(self, weight_difference, silicon_thermal_energy):
        """
        准备特征向量
        
        参数:
        weight_difference: 重量差异 (kg)
        silicon_thermal_energy: 硅热能 (kWh)
        
        返回:
        feature_vector: 包含基础特征和工程特征的向量
        """
        # 基础特征
        base_features = [weight_difference, silicon_thermal_energy]
        
        # 工程特征 - 基于两个输入创建更多特征
        engineered_features = [
            weight_difference ** 2,                    # 重量差异平方
            silicon_thermal_energy ** 2,               # 硅热能平方
            np.sqrt(abs(weight_difference)),           # 重量差异开方
            np.sqrt(abs(silicon_thermal_energy)),      # 硅热能开方
            np.log1p(abs(weight_difference)),          # 重量差异对数
            np.log1p(abs(silicon_thermal_energy)),     # 硅热能对数
            weight_difference * silicon_thermal_energy, # 交互项
            weight_difference / max(silicon_thermal_energy, 0.1), # 比率1
            silicon_thermal_energy / max(weight_difference, 0.1), # 比率2
            (weight_difference + silicon_thermal_energy) / 2,     # 平均值
        ]
        
        return np.array(base_features + engineered_features)
    
    def load_and_prepare_data(self):
        """加载和准备训练数据"""
        print("📊 加载训练数据...")
        
        data_file = Path("output_results/A01_A40_cycles__analysis.csv")
        if not data_file.exists():
            raise FileNotFoundError(f"数据文件不存在: {data_file}")
        
        df = pd.read_csv(data_file)
        print(f"✅ 数据加载成功: {df.shape}")
        
        # 检查必要列
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"缺少必要列: {missing_cols}")
        
        # 数据清洗
        df_clean = df.dropna(subset=required_cols)
        
        # 过滤异常值
        df_filtered = df_clean[
            (df_clean['weight_difference'] > 0) &
            (df_clean['weight_difference'] < 1000) &
            (df_clean['silicon_thermal_energy_kwh'] > 0) &
            (df_clean['silicon_thermal_energy_kwh'] < 1000) &
            (df_clean['vice_total_energy_kwh'] > 0) &
            (df_clean['vice_total_energy_kwh'] < 2000)
        ]
        
        print(f"📊 清洗后数据: {df_filtered.shape}")
        
        # 准备特征矩阵
        X = []
        for _, row in df_filtered.iterrows():
            features = self.prepare_features(
                row['weight_difference'], 
                row['silicon_thermal_energy_kwh']
            )
            X.append(features)
        
        X = np.array(X)
        y = df_filtered['vice_total_energy_kwh'].values
        
        print(f"📊 特征矩阵: {X.shape}")
        print(f"📊 目标变量范围: {y.min():.1f} - {y.max():.1f} kWh")
        
        return X, y, df_filtered
    
    def train(self, optimize_hyperparameters=True):
        """训练模型"""
        print("\n🚀 开始训练简化SVR模型...")
        
        # 加载数据
        X, y, df = self.load_and_prepare_data()
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        print(f"📊 训练集: {X_train.shape}")
        print(f"📊 测试集: {X_test.shape}")
        
        # 数据标准化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 模型训练
        if optimize_hyperparameters:
            print("🔧 超参数优化...")
            param_grid = {
                'C': [10, 50, 100, 200],
                'gamma': ['scale', 'auto', 0.001, 0.01, 0.1],
                'epsilon': [0.01, 0.1, 0.2, 0.5]
            }
            
            svr_base = SVR(kernel='rbf')
            grid_search = GridSearchCV(
                svr_base, param_grid, 
                cv=5, scoring='neg_mean_absolute_error',
                n_jobs=-1
            )
            
            grid_search.fit(X_train_scaled, y_train)
            self.model = grid_search.best_estimator_
            
            print(f"✅ 最佳参数: {grid_search.best_params_}")
            print(f"✅ 最佳CV分数: {-grid_search.best_score_:.2f}")
        else:
            print("🔧 使用默认参数训练...")
            self.model = SVR(kernel='rbf', C=100, gamma='scale', epsilon=0.1)
            self.model.fit(X_train_scaled, y_train)
        
        # 模型评估
        y_pred = self.model.predict(X_test_scaled)
        
        # 计算评估指标
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        errors = np.abs(y_pred - y_test)
        acc_5 = (errors <= 5).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        self.training_results = {
            'mae': float(mae),
            'rmse': float(rmse),
            'r2_score': float(r2),
            'accuracy_5kwh': float(acc_5),
            'accuracy_10kwh': float(acc_10),
            'accuracy_15kwh': float(acc_15),
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'feature_count': X.shape[1]
        }
        
        print(f"\n📊 训练结果:")
        print(f"  ±5kWh准确率: {acc_5:.2f}%")
        print(f"  ±10kWh准确率: {acc_10:.2f}%")
        print(f"  ±15kWh准确率: {acc_15:.2f}%")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  RMSE: {rmse:.2f} kWh")
        print(f"  R²: {r2:.4f}")
        
        self.is_trained = True
        return self.training_results
    
    def predict(self, weight_difference, silicon_thermal_energy):
        """
        预测副功率
        
        参数:
        weight_difference: 重量差异 (kg)
        silicon_thermal_energy: 硅热能 (kWh)
        
        返回:
        predicted_vice_power: 预测的副功率 (kWh)
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，请先调用train()方法")
        
        # 准备特征
        features = self.prepare_features(weight_difference, silicon_thermal_energy)
        
        # 标准化
        features_scaled = self.scaler.transform([features])
        
        # 预测
        prediction = self.model.predict(features_scaled)[0]
        
        return prediction
    
    def batch_predict(self, weight_differences, silicon_thermal_energies):
        """
        批量预测
        
        参数:
        weight_differences: 重量差异列表
        silicon_thermal_energies: 硅热能列表
        
        返回:
        predictions: 预测结果列表
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，请先调用train()方法")
        
        predictions = []
        for wd, ste in zip(weight_differences, silicon_thermal_energies):
            pred = self.predict(wd, ste)
            predictions.append(pred)
        
        return predictions
    
    def save_model(self, save_dir=None):
        """保存模型"""
        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")
        
        if save_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_dir = Path(f"simplified_svr_model_{timestamp}")
        else:
            save_dir = Path(save_dir)
        
        save_dir.mkdir(exist_ok=True)
        
        # 保存模型组件
        joblib.dump(self.model, save_dir / "svr_model.joblib")
        joblib.dump(self.scaler, save_dir / "scaler.joblib")
        
        # 保存模型信息
        model_info = {
            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
            'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'sklearn_version': __import__('sklearn').__version__,
            'model_type': 'Simplified SVR (2 input features)',
            'input_features': self.feature_names,
            'feature_engineering': '10 additional engineered features',
            'training_results': self.training_results
        }
        
        with open(save_dir / "model_info.json", 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        # 创建使用说明
        readme_content = f"""# 简化SVR副功率预测模型

## 模型信息
- 训练时间: {model_info['timestamp']}
- 输入特征: 重量差异, 硅热能
- 模型类型: Support Vector Regression (RBF kernel)

## 性能指标
- ±10kWh准确率: {self.training_results['accuracy_10kwh']:.2f}%
- MAE: {self.training_results['mae']:.2f} kWh
- RMSE: {self.training_results['rmse']:.2f} kWh
- R²: {self.training_results['r2_score']:.4f}

## 使用方法

### 加载模型
```python
import joblib
from simplified_svr_model import SimplifiedSVRModel

# 创建模型实例
model = SimplifiedSVRModel()

# 加载训练好的组件
model.model = joblib.load('svr_model.joblib')
model.scaler = joblib.load('scaler.joblib')
model.is_trained = True
```

### 预测
```python
# 单次预测
weight_diff = 200.0  # kg
silicon_energy = 150.0  # kWh
predicted_vice_power = model.predict(weight_diff, silicon_energy)
print(f"预测副功率: {{predicted_vice_power:.2f}} kWh")

# 批量预测
weight_diffs = [200, 180, 220]
silicon_energies = [150, 140, 160]
predictions = model.batch_predict(weight_diffs, silicon_energies)
```

## 输入范围建议
- 重量差异: 20-300 kg
- 硅热能: 20-200 kWh
"""
        
        with open(save_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"💾 模型已保存到: {save_dir}")
        return save_dir
    
    @classmethod
    def load_model(cls, model_dir):
        """加载已保存的模型"""
        model_dir = Path(model_dir)
        
        if not model_dir.exists():
            raise FileNotFoundError(f"模型目录不存在: {model_dir}")
        
        # 创建模型实例
        instance = cls()
        
        # 加载模型组件
        instance.model = joblib.load(model_dir / "svr_model.joblib")
        instance.scaler = joblib.load(model_dir / "scaler.joblib")
        instance.is_trained = True
        
        # 加载模型信息
        with open(model_dir / "model_info.json", 'r', encoding='utf-8') as f:
            model_info = json.load(f)
            instance.training_results = model_info.get('training_results', {})
        
        print(f"✅ 模型加载成功: {model_dir}")
        return instance

def main():
    """主函数 - 演示简化模型的使用"""
    print("="*60)
    print("🧪 简化SVR副功率预测模型")
    print("="*60)
    print("输入特征: 重量差异 + 硅热能")
    
    # 检查环境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print(f"当前环境: {conda_env}")
    
    try:
        import sklearn
        print(f"scikit-learn版本: {sklearn.__version__}")
    except ImportError:
        print("❌ scikit-learn未安装")
        return
    
    # 创建和训练模型
    model = SimplifiedSVRModel()
    
    try:
        # 训练模型
        results = model.train(optimize_hyperparameters=True)
        
        # 保存模型
        save_dir = model.save_model()
        
        # 演示预测
        print(f"\n🎯 预测演示:")
        test_cases = [
            (200.0, 150.0),  # 重量差异200kg, 硅热能150kWh
            (180.0, 140.0),  # 重量差异180kg, 硅热能140kWh
            (220.0, 160.0),  # 重量差异220kg, 硅热能160kWh
        ]
        
        for weight_diff, silicon_energy in test_cases:
            pred = model.predict(weight_diff, silicon_energy)
            print(f"  重量差异: {weight_diff}kg, 硅热能: {silicon_energy}kWh → 预测副功率: {pred:.2f}kWh")
        
        print(f"\n✅ 简化模型训练完成!")
        print(f"📁 模型保存位置: {save_dir}")
        print(f"📊 ±10kWh准确率: {results['accuracy_10kwh']:.2f}%")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")

if __name__ == "__main__":
    main()
