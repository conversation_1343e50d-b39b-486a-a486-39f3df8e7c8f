#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试生产就绪模型
"""

import sys
sys.path.append('production_ready_models')
from realtime_predictor import RealtimePredictor

def test_production_model():
    """测试生产就绪模型"""
    print('🧪 测试生产就绪模型...')
    
    # 初始化预测器
    predictor = RealtimePredictor(model_dir='production_ready_models')
    
    # 测试案例
    test_cases = [
        (320.5, 280.3, '复投', '中等重量复投'),
        (150.2, 130.8, '首投', '轻量首投'),
        (450.0, 380.5, '复投', '重量复投')
    ]
    
    print(f"\n执行 {len(test_cases)} 个测试案例:")
    print("-" * 50)
    
    for weight, energy, process, desc in test_cases:
        try:
            result = predictor.predict(weight, energy, process)
            predicted_power = result['predicted_power']
            confidence = result['confidence']
            print(f'{desc}: 预测{predicted_power}kWh (置信度:{confidence})')
        except Exception as e:
            print(f'{desc}: 测试失败 - {e}')
    
    print('\n✅ 生产就绪模型测试完成！')

if __name__ == "__main__":
    test_production_model()
