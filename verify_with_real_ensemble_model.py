#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真正的26特征集成模型进行验证
"""

import os
import pandas as pd
import numpy as np
import sys
from pathlib import Path
import joblib
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def load_real_ensemble_model():
    """加载真正的26特征集成模型"""
    print("🤖 加载真正的26特征集成模型...")
    
    try:
        # 加载模型文件
        ensemble_model = joblib.load('realtime_vice_power_models/ensemble_model.joblib')
        feature_engineer = joblib.load('realtime_vice_power_models/feature_engineer.joblib')
        scaler = joblib.load('realtime_vice_power_models/scaler.joblib')
        
        print(f"✅ 集成模型加载成功")
        print(f"  - 模型类型: {type(ensemble_model)}")
        print(f"  - 特征工程器: {type(feature_engineer)}")
        print(f"  - 标准化器: {type(scaler)}")
        print(f"  - 特征数量: {scaler.n_features_in_}")
        
        return ensemble_model, feature_engineer, scaler
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None, None

def reproduce_71_3_result():
    """重现71.3%的结果"""
    print(f"\n📊 重现71.3%结果...")
    
    # 加载模型
    ensemble_model, feature_engineer, scaler = load_real_ensemble_model()
    if ensemble_model is None:
        return None
    
    # 加载训练数据
    training_data = Path("output_results/A01_A40_cycles__analysis.csv")
    if not training_data.exists():
        print(f"❌ 训练数据不存在: {training_data}")
        return None
    
    df = pd.read_csv(training_data)
    print(f"✅ 加载训练数据: {len(df)} 行")
    
    # 时间序列分割：前80%训练，后20%测试
    split_idx = int(len(df) * 0.8)
    test_df = df.iloc[split_idx:].copy()
    
    print(f"  - 分割点: 第{split_idx}行")
    print(f"  - 测试样本: {len(test_df)} 行")
    
    try:
        # 特征工程 (26个特征)
        print(f"  - 开始特征工程...")
        X = feature_engineer.create_realtime_features(test_df)
        y = test_df['vice_total_energy_kwh']
        
        print(f"  - 特征矩阵: {X.shape}")
        print(f"  - 特征列: {list(X.columns)}")
        
        # 预测
        print(f"  - 开始预测...")
        predictions = ensemble_model.predict(X)
        
        # 评估
        errors = np.abs(predictions - y)
        acc_5 = (errors <= 5).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        mae = errors.mean()
        
        print(f"\n📈 重现结果:")
        print(f"  - ±5kWh准确率: {acc_5:.1f}%")
        print(f"  - ±10kWh准确率: {acc_10:.1f}%")
        print(f"  - ±15kWh准确率: {acc_15:.1f}%")
        print(f"  - 平均绝对误差: {mae:.2f} kWh")
        print(f"  - 是否重现71.3%: {abs(acc_10 - 71.3) < 1}")
        
        return {
            'acc_5': acc_5,
            'acc_10': acc_10,
            'acc_15': acc_15,
            'mae': mae,
            'reproduced_71_3': abs(acc_10 - 71.3) < 1
        }
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        return None

def test_ensemble_on_new_data():
    """在新数据上测试集成模型"""
    print(f"\n🧪 在新数据上测试26特征集成模型...")
    
    # 加载模型
    ensemble_model, feature_engineer, scaler = load_real_ensemble_model()
    if ensemble_model is None:
        return None
    
    # 加载新数据
    new_data = Path("output_results/all_folders_summary.csv")
    if not new_data.exists():
        print(f"❌ 新数据不存在: {new_data}")
        return None
    
    df = pd.read_csv(new_data)
    print(f"✅ 加载新数据: {len(df)} 行")
    
    # 去重
    df_clean = df.drop_duplicates(subset=['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh'])
    print(f"  - 去重后: {len(df_clean)} 行")
    
    # 随机抽取300个样本
    if len(df_clean) >= 300:
        test_sample = df_clean.sample(n=300, random_state=42)
    else:
        test_sample = df_clean
    
    print(f"  - 测试样本: {len(test_sample)} 行")
    
    try:
        # 特征工程
        print(f"  - 开始特征工程...")
        X = feature_engineer.create_realtime_features(test_sample)
        y = test_sample['vice_total_energy_kwh']
        
        print(f"  - 特征矩阵: {X.shape}")
        
        # 预测
        print(f"  - 开始预测...")
        predictions = ensemble_model.predict(X)
        
        # 评估
        errors = np.abs(predictions - y)
        acc_5 = (errors <= 5).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        mae = errors.mean()
        
        print(f"\n📈 新数据测试结果:")
        print(f"  - ±5kWh准确率: {acc_5:.1f}%")
        print(f"  - ±10kWh准确率: {acc_10:.1f}%")
        print(f"  - ±15kWh准确率: {acc_15:.1f}%")
        print(f"  - 平均绝对误差: {mae:.2f} kWh")
        
        # 与之前结果对比
        print(f"\n📊 与之前结果对比:")
        print(f"  - 训练数据71.3% vs 新数据{acc_10:.1f}%: 差异{acc_10-71.3:+.1f}%")
        print(f"  - 线性回归25.3% vs 集成模型{acc_10:.1f}%: 差异{acc_10-25.3:+.1f}%")
        
        return {
            'acc_5': acc_5,
            'acc_10': acc_10,
            'acc_15': acc_15,
            'mae': mae,
            'vs_71_3': acc_10 - 71.3,
            'vs_25_3': acc_10 - 25.3
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def analyze_feature_importance():
    """分析特征重要性"""
    print(f"\n🔍 分析26特征的重要性...")
    
    # 加载模型
    ensemble_model, feature_engineer, scaler = load_real_ensemble_model()
    if ensemble_model is None:
        return None
    
    try:
        # 检查模型是否有特征重要性
        if hasattr(ensemble_model, 'models'):
            print(f"  - 集成模型包含: {list(ensemble_model.models.keys())}")
            
            # 获取随机森林的特征重要性
            if 'random_forest' in ensemble_model.models:
                rf_model = ensemble_model.models['random_forest']
                if hasattr(rf_model, 'feature_importances_'):
                    importances = rf_model.feature_importances_
                    feature_names = feature_engineer.feature_names
                    
                    # 创建特征重要性DataFrame
                    importance_df = pd.DataFrame({
                        'feature': feature_names,
                        'importance': importances
                    }).sort_values('importance', ascending=False)
                    
                    print(f"  - 前10个重要特征:")
                    for i, row in importance_df.head(10).iterrows():
                        print(f"    {row['feature']}: {row['importance']:.4f}")
                    
                    return importance_df
        
        print(f"  - 无法获取特征重要性")
        return None
        
    except Exception as e:
        print(f"❌ 特征重要性分析失败: {e}")
        return None

def compare_model_architectures():
    """对比不同模型架构"""
    print(f"\n⚖️ 对比不同模型架构...")
    
    comparison = {
        '26特征集成模型': {
            'features': 26,
            'algorithms': 'RandomForest + GradientBoosting + Ridge',
            'complexity': '高',
            'training_acc_10': 71.3,
            'new_data_acc_10': None,  # 待测试
            'model_size': '17.9MB',
            'deployment_complexity': '高'
        },
        '2特征线性模型': {
            'features': 2,
            'algorithms': 'LinearRegression',
            'complexity': '低',
            'training_acc_10': None,  # 未在训练数据上测试
            'new_data_acc_10': 25.3,
            'model_size': '<1KB',
            'deployment_complexity': '低'
        }
    }
    
    print(f"模型架构对比:")
    for model_name, specs in comparison.items():
        print(f"\n{model_name}:")
        for key, value in specs.items():
            print(f"  - {key}: {value}")
    
    return comparison

def main():
    """主函数"""
    print("使用真正的26特征集成模型进行验证")
    print("="*60)
    
    # 环境检查
    if not check_environment():
        return False
    
    # 1. 重现71.3%结果
    reproduction_result = reproduce_71_3_result()
    
    # 2. 在新数据上测试
    new_data_result = test_ensemble_on_new_data()
    
    # 3. 分析特征重要性
    importance_result = analyze_feature_importance()
    
    # 4. 对比模型架构
    comparison = compare_model_architectures()
    
    # 5. 生成最终报告
    print(f"\n🎯 验证完成！")
    print(f"关键发现:")
    
    if reproduction_result:
        print(f"  - 71.3%重现: {'✅ 成功' if reproduction_result['reproduced_71_3'] else '❌ 失败'}")
        print(f"  - 重现结果: {reproduction_result['acc_10']:.1f}%")
    
    if new_data_result:
        print(f"  - 新数据测试: {new_data_result['acc_10']:.1f}%")
        print(f"  - 与71.3%差异: {new_data_result['vs_71_3']:+.1f}%")
        print(f"  - 与25.3%差异: {new_data_result['vs_25_3']:+.1f}%")
    
    # 保存结果
    results = {
        'reproduction': reproduction_result,
        'new_data_test': new_data_result,
        'feature_importance': importance_result.to_dict() if importance_result is not None else None,
        'model_comparison': comparison
    }
    
    import json
    with open('ensemble_model_verification_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"✅ 验证结果已保存: ensemble_model_verification_results.json")
    
    return True

if __name__ == "__main__":
    success = main()
