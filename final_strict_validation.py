#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终严格验证 - 防止数据泄露的模型训练和保存
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime

def strict_data_analysis():
    """严格的数据分析和模型验证"""
    print("="*60)
    print("🔒 严格模型验证 - 防止数据泄露")
    print("="*60)
    
    # 1. 加载数据
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return None
    
    df = pd.read_csv(data_file)
    print(f"✅ 数据加载: {df.shape}")
    
    # 2. 数据清洗
    required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
    df_clean = df.dropna(subset=required_cols)
    df_filtered = df_clean[
        (df_clean['weight_difference'] > 0) &
        (df_clean['weight_difference'] < 1000) &
        (df_clean['silicon_thermal_energy_kwh'] > 0) &
        (df_clean['silicon_thermal_energy_kwh'] < 1000) &
        (df_clean['vice_total_energy_kwh'] > 0) &
        (df_clean['vice_total_energy_kwh'] < 2000)
    ]
    
    print(f"📊 数据清洗: {df.shape[0]} → {df_filtered.shape[0]} 条")
    
    # 3. 严格数据分割 - 在任何特征工程之前
    print(f"\n🔒 严格数据分割 (防止数据泄露):")
    
    # 使用固定随机种子确保可重现
    np.random.seed(42)
    indices = np.random.permutation(len(df_filtered))
    
    # 80/20分割
    split_point = int(len(df_filtered) * 0.8)
    train_indices = indices[:split_point]
    test_indices = indices[split_point:]
    
    df_train = df_filtered.iloc[train_indices].copy()
    df_test = df_filtered.iloc[test_indices].copy()
    
    print(f"  训练集: {len(df_train)} 条")
    print(f"  测试集: {len(df_test)} 条")
    
    # 验证分割质量
    train_mean = df_train['vice_total_energy_kwh'].mean()
    test_mean = df_test['vice_total_energy_kwh'].mean()
    print(f"  训练集副功率均值: {train_mean:.2f} kWh")
    print(f"  测试集副功率均值: {test_mean:.2f} kWh")
    print(f"  分布差异: {abs(train_mean - test_mean):.2f} kWh")
    
    return df_train, df_test

def create_prediction_model(df_train):
    """基于训练数据创建预测模型"""
    print(f"\n🚀 基于训练数据创建模型:")
    
    # 分析训练数据的统计特征
    weight_stats = {
        'mean': df_train['weight_difference'].mean(),
        'std': df_train['weight_difference'].std(),
        'min': df_train['weight_difference'].min(),
        'max': df_train['weight_difference'].max()
    }
    
    silicon_stats = {
        'mean': df_train['silicon_thermal_energy_kwh'].mean(),
        'std': df_train['silicon_thermal_energy_kwh'].std(),
        'min': df_train['silicon_thermal_energy_kwh'].min(),
        'max': df_train['silicon_thermal_energy_kwh'].max()
    }
    
    vice_stats = {
        'mean': df_train['vice_total_energy_kwh'].mean(),
        'std': df_train['vice_total_energy_kwh'].std(),
        'min': df_train['vice_total_energy_kwh'].min(),
        'max': df_train['vice_total_energy_kwh'].max()
    }
    
    # 计算相关性 (只在训练数据上)
    corr_matrix = df_train[['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']].corr()
    weight_vice_corr = corr_matrix.loc['weight_difference', 'vice_total_energy_kwh']
    silicon_vice_corr = corr_matrix.loc['silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
    
    print(f"  训练数据统计:")
    print(f"    重量差异相关性: {weight_vice_corr:.3f}")
    print(f"    硅热能相关性: {silicon_vice_corr:.3f}")
    
    # 基于训练数据的线性回归系数估算
    # 使用最小二乘法估算系数
    X_train = df_train[['weight_difference', 'silicon_thermal_energy_kwh']].values
    y_train = df_train['vice_total_energy_kwh'].values
    
    # 添加常数项
    X_train_with_const = np.column_stack([np.ones(len(X_train)), X_train])
    
    # 最小二乘解
    coeffs = np.linalg.lstsq(X_train_with_const, y_train, rcond=None)[0]
    
    model_params = {
        'intercept': float(coeffs[0]),
        'weight_coef': float(coeffs[1]),
        'silicon_coef': float(coeffs[2]),
        'weight_stats': weight_stats,
        'silicon_stats': silicon_stats,
        'vice_stats': vice_stats,
        'correlations': {
            'weight_vice': float(weight_vice_corr),
            'silicon_vice': float(silicon_vice_corr)
        }
    }
    
    print(f"  模型参数:")
    print(f"    截距: {model_params['intercept']:.2f}")
    print(f"    重量系数: {model_params['weight_coef']:.4f}")
    print(f"    硅热能系数: {model_params['silicon_coef']:.4f}")
    
    return model_params

def test_model_strict(model_params, df_test):
    """严格测试模型 - 只使用测试数据"""
    print(f"\n🧪 严格模型测试 (仅使用测试数据):")
    
    predictions = []
    actual_values = []
    
    for _, row in df_test.iterrows():
        weight_diff = row['weight_difference']
        silicon_energy = row['silicon_thermal_energy_kwh']
        actual_vice = row['vice_total_energy_kwh']
        
        # 使用训练得到的参数进行预测
        predicted_vice = (
            model_params['intercept'] +
            model_params['weight_coef'] * weight_diff +
            model_params['silicon_coef'] * silicon_energy
        )
        
        # 限制在合理范围内
        predicted_vice = max(
            model_params['vice_stats']['min'],
            min(predicted_vice, model_params['vice_stats']['max'])
        )
        
        predictions.append(predicted_vice)
        actual_values.append(actual_vice)
    
    predictions = np.array(predictions)
    actual_values = np.array(actual_values)
    
    # 计算评估指标
    errors = np.abs(predictions - actual_values)
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean((predictions - actual_values) ** 2))
    
    # 准确率指标
    acc_5 = (errors <= 5).mean() * 100
    acc_10 = (errors <= 10).mean() * 100
    acc_15 = (errors <= 15).mean() * 100
    
    test_results = {
        'test_samples': len(df_test),
        'mae': float(mae),
        'rmse': float(rmse),
        'accuracy_5kwh': float(acc_5),
        'accuracy_10kwh': float(acc_10),
        'accuracy_15kwh': float(acc_15),
        'predictions': predictions.tolist(),
        'actual_values': actual_values.tolist(),
        'errors': errors.tolist()
    }
    
    print(f"  📊 测试结果:")
    print(f"    测试样本: {len(df_test)}")
    print(f"    MAE: {mae:.2f} kWh")
    print(f"    RMSE: {rmse:.2f} kWh")
    print(f"    ±5kWh准确率: {acc_5:.2f}%")
    print(f"    ±10kWh准确率: {acc_10:.2f}%")
    print(f"    ±15kWh准确率: {acc_15:.2f}%")
    
    return test_results

def save_validated_model(model_params, test_results, df_train, df_test):
    """保存验证过的模型"""
    print(f"\n💾 保存验证模型:")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_dir = Path(f"validated_lj_env_1_model_{timestamp}")
    model_dir.mkdir(exist_ok=True)
    
    # 完整的模型信息
    model_info = {
        'model_metadata': {
            'timestamp': timestamp,
            'training_environment': 'lj_env_1',
            'model_type': 'Linear Regression (Strict Validation)',
            'data_leakage_prevention': 'STRICT_TRAIN_TEST_SPLIT',
            'validation_method': 'HOLDOUT_TEST_SET'
        },
        'data_info': {
            'total_samples': len(df_train) + len(df_test),
            'train_samples': len(df_train),
            'test_samples': len(df_test),
            'split_method': 'Random 80/20 split with seed=42'
        },
        'model_parameters': model_params,
        'test_results': test_results,
        'data_leakage_checks': {
            'split_before_feature_engineering': True,
            'no_test_data_in_training': True,
            'independent_validation': True,
            'reproducible_results': True
        }
    }
    
    # 保存模型信息
    with open(model_dir / "model_info.json", 'w', encoding='utf-8') as f:
        json.dump(model_info, f, indent=2, ensure_ascii=False)
    
    # 创建预测器代码
    predictor_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格验证的lj_env_1副功率预测器
训练时间: {timestamp}
测试准确率: {test_results['accuracy_10kwh']:.2f}% (±10kWh)
数据泄露检查: 通过
"""

import json
from pathlib import Path

class ValidatedVicePowerPredictor:
    def __init__(self, model_dir="{model_dir}"):
        self.model_dir = Path(model_dir)
        
        # 加载模型参数
        with open(self.model_dir / "model_info.json", 'r', encoding='utf-8') as f:
            model_info = json.load(f)
        
        self.params = model_info['model_parameters']
        self.test_results = model_info['test_results']
        
    def predict(self, weight_difference, silicon_thermal_energy):
        """
        预测副功率
        
        参数:
        weight_difference: 重量差异 (kg)
        silicon_thermal_energy: 硅热能 (kWh)
        
        返回:
        predicted_vice_power: 预测的副功率 (kWh)
        """
        
        # 输入验证
        if weight_difference <= 0 or silicon_thermal_energy <= 0:
            raise ValueError("重量差异和硅热能必须大于0")
        
        # 线性预测
        predicted_power = (
            self.params['intercept'] +
            self.params['weight_coef'] * weight_difference +
            self.params['silicon_coef'] * silicon_thermal_energy
        )
        
        # 限制在训练数据范围内
        min_val = self.params['vice_stats']['min']
        max_val = self.params['vice_stats']['max']
        predicted_power = max(min_val, min(predicted_power, max_val))
        
        return predicted_power
    
    def get_model_info(self):
        """获取模型信息"""
        return {{
            'accuracy_10kwh': self.test_results['accuracy_10kwh'],
            'mae': self.test_results['mae'],
            'test_samples': self.test_results['test_samples'],
            'data_leakage_check': 'PASSED'
        }}

# 使用示例
if __name__ == "__main__":
    predictor = ValidatedVicePowerPredictor()
    
    # 测试预测
    test_cases = [
        (200, 150, "标准批量"),
        (100, 80, "小批量"),
        (300, 250, "大批量")
    ]
    
    print("验证模型预测结果:")
    print("重量差异  硅热能   预测副功率  场景")
    print("-" * 40)
    
    for weight, silicon, desc in test_cases:
        pred = predictor.predict(weight, silicon)
        print(f"{{weight:>6}}kg {{silicon:>6}}kWh {{pred:>8.1f}}kWh  {{desc}}")
    
    # 显示模型信息
    info = predictor.get_model_info()
    print(f"\\n模型信息:")
    print(f"±10kWh准确率: {{info['accuracy_10kwh']:.2f}}%")
    print(f"MAE: {{info['mae']:.2f}} kWh")
    print(f"数据泄露检查: {{info['data_leakage_check']}}")
'''
    
    with open(model_dir / "validated_predictor.py", 'w', encoding='utf-8') as f:
        f.write(predictor_code)
    
    # 保存测试数据样本
    test_sample = df_test.sample(n=min(20, len(df_test)), random_state=42)
    test_sample.to_csv(model_dir / "test_sample.csv", index=False, encoding='utf-8')
    
    print(f"  ✅ 模型已保存到: {model_dir}")
    print(f"  📁 包含文件:")
    print(f"    - model_info.json (完整模型信息)")
    print(f"    - validated_predictor.py (预测器)")
    print(f"    - test_sample.csv (测试样本)")
    
    return model_dir

def main():
    """主函数"""
    print("🔒 最终严格验证 - 确保无数据泄露")
    
    try:
        # 1. 严格数据分割
        df_train, df_test = strict_data_analysis()
        if df_train is None:
            return None
        
        # 2. 基于训练数据创建模型
        model_params = create_prediction_model(df_train)
        
        # 3. 严格测试
        test_results = test_model_strict(model_params, df_test)
        
        # 4. 保存验证模型
        model_dir = save_validated_model(model_params, test_results, df_train, df_test)
        
        # 5. 数据泄露检查总结
        print(f"\n" + "="*60)
        print("🔍 数据泄露检查总结")
        print("="*60)
        print(f"✅ 数据分割在任何分析之前完成")
        print(f"✅ 模型参数只基于训练数据")
        print(f"✅ 测试数据完全独立")
        print(f"✅ 结果可重现 (random_state=42)")
        print(f"✅ 无信息泄露")
        
        # 6. 最终结果
        print(f"\n📊 最终验证结果:")
        print(f"  训练样本: {len(df_train)}")
        print(f"  测试样本: {len(df_test)}")
        print(f"  测试MAE: {test_results['mae']:.2f} kWh")
        print(f"  测试±10kWh准确率: {test_results['accuracy_10kwh']:.2f}%")
        print(f"  模型保存: {model_dir}")
        print(f"  数据泄露检查: ✅ 通过")
        
        return {
            'model_dir': model_dir,
            'test_results': test_results,
            'data_leakage_check': 'PASSED'
        }
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🎉 严格验证完成！模型可信度极高！")
        print(f"📁 模型位置: {result['model_dir']}")
        print(f"📊 ±10kWh准确率: {result['test_results']['accuracy_10kwh']:.2f}%")
    else:
        print(f"\n❌ 验证失败")
