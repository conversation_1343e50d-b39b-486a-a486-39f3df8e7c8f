#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副功率预测模型综合测试脚本
确保在lj_env_1环境下测试所有可用模型
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import json
import random
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EnvironmentChecker:
    """环境检查器"""
    
    @staticmethod
    def check_environment():
        """检查当前环境是否为lj_env_1"""
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        print(f"当前Conda环境: {conda_env}")
        
        if conda_env != 'lj_env_1':
            print(f"❌ 错误：当前环境为 {conda_env}，必须使用 lj_env_1 环境")
            print("请运行: conda activate lj_env_1")
            return False
        else:
            print("✅ 环境检查通过：正在使用 lj_env_1 环境")
            return True

class ModelDiscovery:
    """模型发现器"""
    
    def __init__(self):
        self.discovered_models = {}
        
    def find_all_models(self):
        """查找所有可用的副功率预测模型"""
        print("="*60)
        print("🔍 发现所有副功率预测模型")
        print("="*60)
        
        # 1. 查找85.4%准确率项目中的SVR模型
        svr_model_path = Path("副功率预测_85.4%准确率_完整项目/models")
        if svr_model_path.exists():
            self._check_svr_model(svr_model_path)
        
        # 2. 查找production_ready_models中的集成模型
        production_path = Path("production_ready_models")
        if production_path.exists():
            self._check_production_model(production_path)
        
        # 3. 查找realtime_vice_power_models中的实时模型
        realtime_path = Path("realtime_vice_power_models")
        if realtime_path.exists():
            self._check_realtime_model(realtime_path)
        
        # 4. 查找其他可能的模型文件
        self._find_other_models()
        
        print(f"\n📊 总共发现 {len(self.discovered_models)} 个模型:")
        for name, info in self.discovered_models.items():
            print(f"  ✅ {name}: {info['description']}")
        
        return self.discovered_models
    
    def _check_svr_model(self, model_path):
        """检查SVR模型"""
        try:
            model_file = model_path / "best_model_svr.joblib"
            scaler_file = model_path / "scaler.joblib"
            selector_file = model_path / "feature_selector.joblib"
            results_file = model_path / "results.json"
            
            if all(f.exists() for f in [model_file, scaler_file, selector_file]):
                # 读取性能信息
                performance = {}
                if results_file.exists():
                    with open(results_file, 'r', encoding='utf-8') as f:
                        results = json.load(f)
                        performance = {
                            'accuracy': results.get('best_accuracy', 'unknown'),
                            'model_type': results.get('best_model', 'svr')
                        }
                
                self.discovered_models['SVR_85.4%'] = {
                    'type': 'svr',
                    'model_file': model_file,
                    'scaler_file': scaler_file,
                    'selector_file': selector_file,
                    'performance': performance,
                    'description': f"SVR模型 (85.4%准确率)"
                }
                print(f"  ✅ 发现SVR模型: {model_file}")
        except Exception as e:
            print(f"  ❌ SVR模型检查失败: {e}")
    
    def _check_production_model(self, model_path):
        """检查生产就绪模型"""
        try:
            model_file = model_path / "ensemble_model.joblib"
            version_file = model_path / "model_version.json"
            
            if model_file.exists():
                # 读取版本信息
                performance = {}
                if version_file.exists():
                    with open(version_file, 'r', encoding='utf-8') as f:
                        version_info = json.load(f)
                        performance = version_info.get('performance', {})
                
                self.discovered_models['Production_Ensemble'] = {
                    'type': 'ensemble',
                    'model_file': model_file,
                    'performance': performance,
                    'description': f"生产集成模型 ({performance.get('accuracy_10kwh', 'unknown')}准确率)"
                }
                print(f"  ✅ 发现生产模型: {model_file}")
        except Exception as e:
            print(f"  ❌ 生产模型检查失败: {e}")
    
    def _check_realtime_model(self, model_path):
        """检查实时模型"""
        try:
            model_file = model_path / "ensemble_model.joblib"
            feature_file = model_path / "feature_engineer.joblib"
            scaler_file = model_path / "scaler.joblib"
            
            if all(f.exists() for f in [model_file, feature_file, scaler_file]):
                self.discovered_models['Realtime_Ensemble'] = {
                    'type': 'realtime_ensemble',
                    'model_file': model_file,
                    'feature_file': feature_file,
                    'scaler_file': scaler_file,
                    'description': "实时集成模型 (71.3%准确率)"
                }
                print(f"  ✅ 发现实时模型: {model_file}")
        except Exception as e:
            print(f"  ❌ 实时模型检查失败: {e}")
    
    def _find_other_models(self):
        """查找其他可能的模型文件"""
        try:
            # 查找所有joblib文件
            for root, dirs, files in os.walk('.'):
                for file in files:
                    if file.endswith('.joblib') and 'model' in file.lower():
                        file_path = Path(root) / file
                        # 避免重复添加已发现的模型
                        if not any(str(file_path) == str(info.get('model_file', '')) 
                                 for info in self.discovered_models.values()):
                            model_name = f"Other_{file.replace('.joblib', '')}"
                            self.discovered_models[model_name] = {
                                'type': 'other',
                                'model_file': file_path,
                                'description': f"其他模型: {file}"
                            }
                            print(f"  ✅ 发现其他模型: {file_path}")
        except Exception as e:
            print(f"  ❌ 查找其他模型失败: {e}")

class DataLoader:
    """数据加载器"""
    
    def __init__(self):
        self.test_data = None
        
    def load_test_data(self, sample_size=100):
        """从output_results加载测试数据"""
        print("\n" + "="*60)
        print("📊 加载测试数据")
        print("="*60)
        
        # 使用主要的训练数据文件
        data_file = Path("output_results/A01_A40_cycles__analysis.csv")
        
        if not data_file.exists():
            print(f"❌ 数据文件不存在: {data_file}")
            return None
        
        try:
            # 加载完整数据
            df = pd.read_csv(data_file)
            print(f"✅ 加载数据文件: {data_file}")
            print(f"📊 数据形状: {df.shape}")
            print(f"📋 列名: {list(df.columns)}")
            
            # 检查必要的列
            required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh', 'feed_type']
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if missing_cols:
                print(f"❌ 缺少必要列: {missing_cols}")
                return None
            
            # 数据清洗
            df = df.dropna(subset=required_cols)
            print(f"📊 清洗后数据形状: {df.shape}")
            
            # 随机采样
            if len(df) > sample_size:
                df_sample = df.sample(n=sample_size, random_state=42)
                print(f"📊 随机采样 {sample_size} 条数据")
            else:
                df_sample = df
                print(f"📊 使用全部 {len(df)} 条数据")
            
            self.test_data = df_sample
            
            # 显示数据统计
            print(f"\n📈 测试数据统计:")
            print(f"  重量差异范围: {df_sample['weight_difference'].min():.2f} - {df_sample['weight_difference'].max():.2f} kg")
            print(f"  硅热能范围: {df_sample['silicon_thermal_energy_kwh'].min():.2f} - {df_sample['silicon_thermal_energy_kwh'].max():.2f} kWh")
            print(f"  副功率范围: {df_sample['vice_total_energy_kwh'].min():.2f} - {df_sample['vice_total_energy_kwh'].max():.2f} kWh")
            print(f"  工艺类型分布: {df_sample['feed_type'].value_counts().to_dict()}")
            
            return df_sample
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None

class ModelTester:
    """模型测试器"""
    
    def __init__(self, models, test_data):
        self.models = models
        self.test_data = test_data
        self.results = {}
        
    def test_all_models(self):
        """测试所有模型"""
        print("\n" + "="*60)
        print("🧪 开始模型测试")
        print("="*60)
        
        for model_name, model_info in self.models.items():
            print(f"\n🔬 测试模型: {model_name}")
            try:
                result = self._test_single_model(model_name, model_info)
                self.results[model_name] = result
                print(f"✅ {model_name} 测试完成")
            except Exception as e:
                print(f"❌ {model_name} 测试失败: {e}")
                self.results[model_name] = {'error': str(e)}
        
        return self.results
    
    def _test_single_model(self, model_name, model_info):
        """测试单个模型"""
        model_type = model_info['type']
        
        if model_type == 'svr':
            return self._test_svr_model(model_info)
        elif model_type in ['ensemble', 'realtime_ensemble']:
            return self._test_ensemble_model(model_info)
        else:
            return self._test_generic_model(model_info)
    
    def _test_svr_model(self, model_info):
        """测试SVR模型"""
        # 加载模型组件
        model = joblib.load(model_info['model_file'])
        scaler = joblib.load(model_info['scaler_file'])
        selector = joblib.load(model_info['selector_file'])
        
        predictions = []
        actual_values = []
        
        for _, row in self.test_data.iterrows():
            try:
                # 准备特征（需要根据实际特征工程逻辑调整）
                features = self._prepare_features_for_svr(row)
                
                # 特征选择和标准化
                features_selected = selector.transform([features])
                features_scaled = scaler.transform(features_selected)
                
                # 预测
                pred = model.predict(features_scaled)[0]
                predictions.append(pred)
                actual_values.append(row['vice_total_energy_kwh'])
                
            except Exception as e:
                print(f"  ⚠️ 单条预测失败: {e}")
                continue
        
        return self._calculate_metrics(predictions, actual_values)
    
    def _test_ensemble_model(self, model_info):
        """测试集成模型"""
        # 加载模型
        model = joblib.load(model_info['model_file'])
        
        predictions = []
        actual_values = []
        
        for _, row in self.test_data.iterrows():
            try:
                # 简化的特征准备（基于基本特征）
                features = [
                    row['weight_difference'],
                    row['silicon_thermal_energy_kwh'],
                    1 if row['feed_type'] == '首投' else 0
                ]
                
                # 预测
                pred = model.predict([features])[0]
                predictions.append(pred)
                actual_values.append(row['vice_total_energy_kwh'])
                
            except Exception as e:
                print(f"  ⚠️ 单条预测失败: {e}")
                continue
        
        return self._calculate_metrics(predictions, actual_values)
    
    def _test_generic_model(self, model_info):
        """测试通用模型"""
        try:
            model = joblib.load(model_info['model_file'])
            
            # 尝试简单预测
            test_features = [[100, 50, 1]]  # 示例特征
            pred = model.predict(test_features)
            
            return {
                'status': 'loaded_successfully',
                'sample_prediction': pred[0] if len(pred) > 0 else None,
                'model_type': str(type(model))
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _prepare_features_for_svr(self, row):
        """为SVR模型准备特征"""
        # 基础特征
        features = [
            row.get('start_weight', 0),
            row.get('end_weight', 0),
            row.get('weight_difference', 0),
            row.get('end_temperature_celsius', 0),
            row.get('first_crystal_seeding_main_power_kw', 0),
            row.get('feed_number_1_records', 0),
            row.get('silicon_thermal_energy_kwh', 0),
            row.get('energy_efficiency_percent', 0),
            row.get('record_count', 0),
            row.get('duration_hours', 0)
        ]
        
        # 工程特征（简化版本）
        weight_diff = row.get('weight_difference', 0)
        silicon_energy = row.get('silicon_thermal_energy_kwh', 0)
        duration = row.get('duration_hours', 1)
        
        engineered_features = [
            weight_diff ** 2,  # weight_difference_squared
            np.sqrt(abs(weight_diff)),  # weight_difference_sqrt
            np.log1p(abs(weight_diff)),  # weight_difference_log
            silicon_energy ** 2,  # silicon_thermal_energy_kwh_squared
            np.sqrt(abs(silicon_energy)),  # silicon_thermal_energy_kwh_sqrt
            np.log1p(abs(silicon_energy)),  # silicon_thermal_energy_kwh_log
            duration ** 2,  # duration_hours_squared
            np.sqrt(abs(duration)),  # duration_hours_sqrt
            np.log1p(abs(duration)),  # duration_hours_log
            weight_diff * silicon_energy,  # interaction features
            weight_diff * duration,
            weight_diff / max(duration, 0.1),
            silicon_energy * duration,
            silicon_energy / max(duration, 0.1),
            1 if row.get('feed_type') == '首投' else 0  # device_frequency (simplified)
        ]
        
        return features + engineered_features
    
    def _calculate_metrics(self, predictions, actual_values):
        """计算评估指标"""
        if not predictions or not actual_values:
            return {'error': 'No valid predictions'}
        
        predictions = np.array(predictions)
        actual_values = np.array(actual_values)
        
        # 基本指标
        mae = np.mean(np.abs(predictions - actual_values))
        rmse = np.sqrt(np.mean((predictions - actual_values) ** 2))
        
        # 准确率指标
        acc_5 = np.mean(np.abs(predictions - actual_values) <= 5) * 100
        acc_10 = np.mean(np.abs(predictions - actual_values) <= 10) * 100
        acc_15 = np.mean(np.abs(predictions - actual_values) <= 15) * 100
        
        # R²分数
        ss_res = np.sum((actual_values - predictions) ** 2)
        ss_tot = np.sum((actual_values - np.mean(actual_values)) ** 2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        return {
            'sample_count': len(predictions),
            'mae': mae,
            'rmse': rmse,
            'r2_score': r2,
            'accuracy_5kwh': acc_5,
            'accuracy_10kwh': acc_10,
            'accuracy_15kwh': acc_15,
            'prediction_range': {
                'min': float(np.min(predictions)),
                'max': float(np.max(predictions)),
                'mean': float(np.mean(predictions))
            },
            'actual_range': {
                'min': float(np.min(actual_values)),
                'max': float(np.max(actual_values)),
                'mean': float(np.mean(actual_values))
            }
        }

def main():
    """主函数"""
    print("="*60)
    print("🧪 副功率预测模型综合测试系统")
    print("="*60)
    
    # 1. 环境检查
    if not EnvironmentChecker.check_environment():
        return
    
    # 2. 模型发现
    discovery = ModelDiscovery()
    models = discovery.find_all_models()
    
    if not models:
        print("❌ 未发现任何模型")
        return
    
    # 3. 数据加载
    loader = DataLoader()
    test_data = loader.load_test_data(sample_size=100)
    
    if test_data is None:
        print("❌ 测试数据加载失败")
        return
    
    # 4. 模型测试
    tester = ModelTester(models, test_data)
    results = tester.test_all_models()
    
    # 5. 结果汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    for model_name, result in results.items():
        print(f"\n🔬 {model_name}:")
        if 'error' in result:
            print(f"  ❌ 错误: {result['error']}")
        else:
            if 'accuracy_10kwh' in result:
                print(f"  ✅ ±10kWh准确率: {result['accuracy_10kwh']:.2f}%")
                print(f"  📊 MAE: {result['mae']:.2f} kWh")
                print(f"  📊 RMSE: {result['rmse']:.2f} kWh")
                print(f"  📊 R²: {result['r2_score']:.4f}")
                print(f"  📊 测试样本数: {result['sample_count']}")
            else:
                print(f"  ℹ️ 状态: {result.get('status', 'unknown')}")
    
    # 6. 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"model_testing_results_{timestamp}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_timestamp': datetime.now().isoformat(),
            'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
            'test_data_size': len(test_data),
            'models_tested': list(models.keys()),
            'results': results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细结果已保存到: {results_file}")
    print("\n✅ 模型测试完成")

if __name__ == "__main__":
    main()
