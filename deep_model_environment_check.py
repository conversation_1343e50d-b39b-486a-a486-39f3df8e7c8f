#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度检查模型文件的实际训练环境
不依赖代码标注，从模型文件本身获取环境信息
"""

import os
import sys
import json
import pickle
import joblib
import numpy as np
from pathlib import Path
from datetime import datetime

class DeepModelEnvironmentChecker:
    """深度模型环境检查器"""
    
    def __init__(self):
        self.current_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        self.python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        self.findings = {}
        
    def check_package_versions(self):
        """检查当前环境的包版本"""
        print("🔍 检查当前环境包版本...")
        
        packages = {}
        
        # 检查核心包
        try:
            import pandas as pd
            packages['pandas'] = pd.__version__
        except ImportError:
            packages['pandas'] = 'NOT_INSTALLED'
        
        try:
            import numpy as np
            packages['numpy'] = np.__version__
        except ImportError:
            packages['numpy'] = 'NOT_INSTALLED'
        
        try:
            import sklearn
            packages['scikit-learn'] = sklearn.__version__
        except ImportError:
            packages['scikit-learn'] = 'NOT_INSTALLED'
        
        try:
            import joblib
            packages['joblib'] = joblib.__version__
        except ImportError:
            packages['joblib'] = 'NOT_INSTALLED'
        
        try:
            import xgboost
            packages['xgboost'] = xgboost.__version__
        except ImportError:
            packages['xgboost'] = 'NOT_INSTALLED'
        
        try:
            import lightgbm
            packages['lightgbm'] = lightgbm.__version__
        except ImportError:
            packages['lightgbm'] = 'NOT_INSTALLED'
        
        return packages
    
    def analyze_model_files(self):
        """分析模型文件获取训练环境信息"""
        print("🔍 分析模型文件...")
        
        model_analysis = {}
        
        # 查找所有模型文件
        model_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith(('.joblib', '.pkl', '.pickle')):
                    model_files.append(Path(root) / file)
        
        print(f"找到 {len(model_files)} 个模型文件")
        
        for model_file in model_files[:10]:  # 检查前10个
            try:
                print(f"\n📋 分析: {model_file}")
                
                # 尝试加载模型
                if str(model_file).endswith('.joblib'):
                    model = joblib.load(model_file)
                else:
                    with open(model_file, 'rb') as f:
                        model = pickle.load(f)
                
                file_analysis = {
                    'file_path': str(model_file),
                    'file_size_mb': model_file.stat().st_size / (1024*1024),
                    'model_type': str(type(model)),
                    'creation_time': datetime.fromtimestamp(model_file.stat().st_mtime).isoformat()
                }
                
                # 检查sklearn版本信息
                if hasattr(model, '__sklearn_version__'):
                    file_analysis['sklearn_version'] = model.__sklearn_version__
                    print(f"  ✅ 发现sklearn版本: {model.__sklearn_version__}")
                
                # 检查模型属性
                if hasattr(model, '__dict__'):
                    attrs = [attr for attr in dir(model) if not attr.startswith('_')]
                    file_analysis['model_attributes'] = attrs[:20]  # 前20个属性
                
                # 检查是否有环境相关属性
                env_attrs = ['training_environment', 'environment', 'env_info', 'creation_info']
                for attr in env_attrs:
                    if hasattr(model, attr):
                        file_analysis[attr] = getattr(model, attr)
                        print(f"  ✅ 发现环境信息 {attr}: {getattr(model, attr)}")
                
                # 检查numpy数据类型（可能反映训练环境）
                if hasattr(model, 'coef_') and hasattr(model.coef_, 'dtype'):
                    file_analysis['numpy_dtype'] = str(model.coef_.dtype)
                
                model_analysis[str(model_file)] = file_analysis
                
            except Exception as e:
                print(f"  ❌ 无法分析: {e}")
                model_analysis[str(model_file)] = {'error': str(e)}
        
        return model_analysis
    
    def check_python_cache_files(self):
        """检查Python缓存文件获取环境信息"""
        print("🔍 检查Python缓存文件...")
        
        cache_analysis = {}
        
        # 查找__pycache__目录
        pycache_dirs = []
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                pycache_dirs.append(Path(root) / '__pycache__')
        
        print(f"找到 {len(pycache_dirs)} 个缓存目录")
        
        for cache_dir in pycache_dirs:
            try:
                cache_files = list(cache_dir.glob('*.pyc'))
                if cache_files:
                    # 从文件名推断Python版本
                    for cache_file in cache_files:
                        if 'cpython-' in cache_file.name:
                            python_version = cache_file.name.split('cpython-')[1].split('.')[0]
                            cache_analysis[str(cache_dir)] = {
                                'python_version': python_version,
                                'cache_files': len(cache_files),
                                'latest_modified': max(f.stat().st_mtime for f in cache_files)
                            }
                            print(f"  发现Python版本: {python_version}")
                            break
            except Exception as e:
                print(f"  ❌ 检查缓存失败: {e}")
        
        return cache_analysis
    
    def analyze_json_metadata(self):
        """分析JSON元数据文件"""
        print("🔍 分析JSON元数据文件...")
        
        json_analysis = {}
        
        # 查找所有JSON文件
        json_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.json') and any(keyword in file.lower() for keyword in 
                    ['model', 'result', 'performance', 'version', 'environment']):
                    json_files.append(Path(root) / file)
        
        print(f"找到 {len(json_files)} 个相关JSON文件")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 查找环境相关信息
                env_info = {}
                
                def extract_env_info(obj, prefix=''):
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            if any(keyword in key.lower() for keyword in 
                                ['env', 'environment', 'version', 'python', 'sklearn', 'numpy']):
                                env_info[f"{prefix}{key}"] = value
                            elif isinstance(value, (dict, list)):
                                extract_env_info(value, f"{prefix}{key}.")
                
                extract_env_info(data)
                
                if env_info:
                    json_analysis[str(json_file)] = env_info
                    print(f"  ✅ {json_file.name}: 发现环境信息")
                    for key, value in env_info.items():
                        print(f"    {key}: {value}")
                
            except Exception as e:
                print(f"  ❌ 无法读取 {json_file}: {e}")
        
        return json_analysis
    
    def compare_with_current_environment(self, model_analysis, json_analysis):
        """与当前环境进行比较"""
        print("\n" + "="*60)
        print("🔄 环境一致性分析")
        print("="*60)
        
        current_packages = self.check_package_versions()
        
        # 从模型文件中提取的环境信息
        extracted_env_info = {}
        
        # 从模型文件提取
        for file_path, analysis in model_analysis.items():
            if 'sklearn_version' in analysis:
                extracted_env_info['sklearn_from_model'] = analysis['sklearn_version']
        
        # 从JSON文件提取
        for file_path, env_info in json_analysis.items():
            for key, value in env_info.items():
                if 'environment' in key.lower():
                    extracted_env_info[f'json_{key}'] = value
        
        # 比较分析
        comparison = {
            'current_environment': {
                'conda_env': self.current_env,
                'python_version': self.python_version,
                'packages': current_packages
            },
            'extracted_environment': extracted_env_info,
            'consistency_check': {}
        }
        
        # 检查一致性
        if 'sklearn_from_model' in extracted_env_info:
            current_sklearn = current_packages.get('scikit-learn', 'UNKNOWN')
            model_sklearn = extracted_env_info['sklearn_from_model']
            
            sklearn_match = current_sklearn == model_sklearn
            comparison['consistency_check']['sklearn_version'] = {
                'current': current_sklearn,
                'model': model_sklearn,
                'match': sklearn_match
            }
            
            if sklearn_match:
                print(f"✅ scikit-learn版本一致: {current_sklearn}")
            else:
                print(f"⚠️  scikit-learn版本不一致:")
                print(f"   当前环境: {current_sklearn}")
                print(f"   模型训练: {model_sklearn}")
        
        # 检查环境名称
        for key, value in extracted_env_info.items():
            if 'environment' in key and isinstance(value, str):
                if value == self.current_env:
                    print(f"✅ 环境名称一致: {value}")
                    comparison['consistency_check']['environment_name'] = {
                        'current': self.current_env,
                        'extracted': value,
                        'match': True
                    }
                else:
                    print(f"⚠️  环境名称不一致:")
                    print(f"   当前环境: {self.current_env}")
                    print(f"   提取到的: {value}")
                    comparison['consistency_check']['environment_name'] = {
                        'current': self.current_env,
                        'extracted': value,
                        'match': False
                    }
        
        return comparison

def main():
    """主函数"""
    checker = DeepModelEnvironmentChecker()
    
    print("="*60)
    print("🔍 深度模型环境检查")
    print("="*60)
    print("此检查不依赖代码标注，直接从模型文件获取环境信息\n")
    
    # 执行检查
    current_packages = checker.check_package_versions()
    model_analysis = checker.analyze_model_files()
    cache_analysis = checker.check_python_cache_files()
    json_analysis = checker.analyze_json_metadata()
    comparison = checker.compare_with_current_environment(model_analysis, json_analysis)
    
    print("\n" + "="*60)
    print("✅ 深度检查完成")
    print("="*60)

if __name__ == "__main__":
    main()
