#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副功率预测项目环境配置检查和设置脚本
检查当前环境配置，提供lj_env_1环境切换指导
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from datetime import datetime

class EnvironmentAnalyzer:
    """环境分析器"""
    
    def __init__(self):
        self.current_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        self.python_path = sys.executable
        self.project_root = Path.cwd()
        
    def check_current_environment(self):
        """检查当前环境配置"""
        print("="*60)
        print("🔍 副功率预测项目环境配置检查")
        print("="*60)
        
        env_info = {
            'current_conda_env': self.current_env,
            'python_executable': self.python_path,
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'project_directory': str(self.project_root),
            'check_time': datetime.now().isoformat()
        }
        
        print(f"📍 当前Conda环境: {env_info['current_conda_env']}")
        print(f"🐍 Python版本: {env_info['python_version']}")
        print(f"📂 项目目录: {env_info['project_directory']}")
        print(f"🔧 Python路径: {env_info['python_executable']}")
        
        return env_info
    
    def analyze_model_training_environment(self):
        """分析模型训练环境要求"""
        print("\n" + "="*60)
        print("🤖 模型训练环境分析")
        print("="*60)
        
        # 检查项目中的环境要求
        required_env = "lj_env_1"
        
        # 分析代码中的环境检查
        env_checks = self._scan_environment_checks()
        
        print(f"📋 项目要求环境: {required_env}")
        print(f"✅ 当前环境: {self.current_env}")
        
        if self.current_env == required_env:
            print("🎉 环境匹配！当前正在使用正确的训练环境")
            return True
        else:
            print(f"⚠️  环境不匹配！需要切换到 {required_env} 环境")
            return False
    
    def _scan_environment_checks(self):
        """扫描代码中的环境检查"""
        env_checks = []
        
        # 扫描主要的Python文件
        python_files = [
            "realtime_model_training.py",
            "副功率预测_85.4%准确率_完整项目/code/serious_reanalysis_from_scratch.py",
            "副功率预测_85.4%准确率_完整项目/code/serious_data_analysis_and_improvement.py"
        ]
        
        for file_path in python_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'lj_env_1' in content:
                            env_checks.append({
                                'file': file_path,
                                'requires_lj_env_1': True,
                                'has_env_check': 'check_environment' in content
                            })
                except Exception as e:
                    print(f"⚠️  无法读取文件 {file_path}: {e}")
        
        return env_checks
    
    def check_dependencies(self):
        """检查依赖包"""
        print("\n" + "="*60)
        print("📦 依赖包检查")
        print("="*60)
        
        required_packages = {
            'pandas': '>=1.3.0',
            'numpy': '>=1.20.0',
            'scikit-learn': '>=1.0.0',
            'joblib': '>=1.0.0',
            'xgboost': '>=1.5.0',
            'lightgbm': '>=3.0.0'
        }
        
        installed_packages = {}
        missing_packages = []
        
        for package in required_packages:
            try:
                module = __import__(package.replace('-', '_'))
                version = getattr(module, '__version__', 'unknown')
                installed_packages[package] = version
                print(f"✅ {package}: {version}")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package}: 未安装")
        
        return {
            'installed': installed_packages,
            'missing': missing_packages,
            'required': required_packages
        }
    
    def provide_environment_setup_guide(self):
        """提供环境设置指导"""
        print("\n" + "="*60)
        print("🛠️  环境设置指导")
        print("="*60)
        
        if self.current_env != 'lj_env_1':
            print("📋 切换到lj_env_1环境的步骤：")
            print()
            print("1️⃣ 检查lj_env_1环境是否存在：")
            print("   conda env list")
            print()
            print("2️⃣ 如果环境存在，激活环境：")
            print("   conda activate lj_env_1")
            print()
            print("3️⃣ 如果环境不存在，创建环境：")
            print("   conda create -n lj_env_1 python=3.8")
            print("   conda activate lj_env_1")
            print()
            print("4️⃣ 安装必要的依赖包：")
            print("   pip install pandas numpy scikit-learn joblib xgboost lightgbm")
            print()
            print("5️⃣ 验证环境：")
            print("   python environment_check_and_setup.py")
            
        else:
            print("✅ 当前已在lj_env_1环境中，可以直接运行模型训练")
    
    def check_model_files(self):
        """检查模型文件"""
        print("\n" + "="*60)
        print("🗂️  模型文件检查")
        print("="*60)
        
        model_paths = [
            "副功率预测_85.4%准确率_完整项目/models/best_model_svr.joblib",
            "副功率预测_85.4%准确率_完整项目/models/scaler.joblib",
            "副功率预测_85.4%准确率_完整项目/models/feature_selector.joblib",
            "副功率预测_85.4%准确率_完整项目/models/results.json",
            "production_ready_models/ensemble_model.joblib",
            "production_ready_models/model_version.json"
        ]
        
        model_status = {}
        
        for model_path in model_paths:
            path = Path(model_path)
            if path.exists():
                size = path.stat().st_size
                model_status[model_path] = {
                    'exists': True,
                    'size_mb': round(size / (1024*1024), 2),
                    'modified': datetime.fromtimestamp(path.stat().st_mtime).isoformat()
                }
                print(f"✅ {model_path} ({model_status[model_path]['size_mb']} MB)")
            else:
                model_status[model_path] = {'exists': False}
                print(f"❌ {model_path} (不存在)")
        
        return model_status
    
    def analyze_training_environment_impact(self):
        """分析环境配置对模型性能的潜在影响"""
        print("\n" + "="*60)
        print("📊 环境配置对模型性能的影响分析")
        print("="*60)
        
        impact_analysis = {
            'environment_consistency': {
                'importance': 'HIGH',
                'description': '使用一致的环境确保模型可复现性',
                'current_status': 'GOOD' if self.current_env == 'lj_env_1' else 'NEEDS_ATTENTION'
            },
            'dependency_versions': {
                'importance': 'MEDIUM',
                'description': '依赖包版本影响算法行为和数值精度',
                'recommendation': '使用固定版本的scikit-learn和numpy'
            },
            'python_version': {
                'importance': 'LOW',
                'description': 'Python版本对模型性能影响较小',
                'current_version': f"{sys.version_info.major}.{sys.version_info.minor}"
            }
        }
        
        for factor, details in impact_analysis.items():
            print(f"\n🔍 {factor.replace('_', ' ').title()}:")
            print(f"   重要性: {details['importance']}")
            print(f"   说明: {details['description']}")
            if 'current_status' in details:
                print(f"   当前状态: {details['current_status']}")
            if 'recommendation' in details:
                print(f"   建议: {details['recommendation']}")
        
        return impact_analysis

def main():
    """主函数"""
    analyzer = EnvironmentAnalyzer()
    
    # 执行基本检查
    env_info = analyzer.check_current_environment()
    env_match = analyzer.analyze_model_training_environment()
    dependencies = analyzer.check_dependencies()
    model_files = analyzer.check_model_files()
    impact_analysis = analyzer.analyze_training_environment_impact()
    
    # 提供设置指导
    analyzer.provide_environment_setup_guide()
    
    # 输出总结
    print("\n" + "="*60)
    print("📝 总结")
    print("="*60)
    
    if env_match:
        print("✅ 环境配置正确，可以运行模型训练")
        print("🚀 建议的下一步操作：")
        print("   python 副功率预测_85.4%准确率_完整项目/code/focused_improvement_analysis.py")
    else:
        print("⚠️  需要切换环境后再运行模型训练")
        print("🔧 请按照上述指导切换到lj_env_1环境")

if __name__ == "__main__":
    main()
