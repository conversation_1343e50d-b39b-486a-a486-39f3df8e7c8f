#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建Excel友好的对比文件
"""

import pandas as pd
import numpy as np

def create_excel_friendly_files():
    """创建Excel友好的对比文件"""
    print("📊 创建Excel友好的对比文件...")
    
    # 1. 简化训练测试样本
    training_df = pd.read_csv('training_test_samples_71_3_percent.csv')
    
    training_simple = pd.DataFrame({
        '样本编号': training_df['sample_id'],
        '设备名称': training_df['folder_name'],
        '开始时间': training_df['start_time'],
        '工艺类型': training_df['feed_type'],
        '重量差异_kg': training_df['weight_difference'].round(1),
        '硅热能_kWh': training_df['silicon_thermal_energy_kwh'].round(1),
        '实际副功率_kWh': training_df['vice_total_energy_kwh'].round(1),
        '持续时间_小时': training_df['duration_hours'].round(2),
        '数据来源': '训练测试集'
    })
    
    training_simple.to_csv('训练测试样本_71.3%准确率.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 训练测试样本已保存: 训练测试样本_71.3%准确率.csv ({len(training_simple)}行)")
    
    # 2. 简化真实验证样本
    validation_df = pd.read_csv('real_validation_samples_22_5_percent.csv')
    
    validation_simple = pd.DataFrame({
        '样本编号': validation_df['sample_id'],
        '设备名称': '多设备随机',
        '开始时间': '随机时间',
        '工艺类型': validation_df['feed_type'],
        '重量差异_kg': validation_df['weight_difference'].round(1),
        '硅热能_kWh': validation_df['silicon_thermal_energy_kwh'].round(1),
        '实际副功率_kWh': validation_df['actual_vice_power'].round(1),
        '预测副功率_kWh': validation_df['predicted_vice_power'].round(1),
        '绝对误差_kWh': validation_df['absolute_error'].round(1),
        '相对误差_%': validation_df['relative_error'].round(1),
        '±10kWh准确': validation_df['within_10kwh'].map({True: '是', False: '否'}),
        '数据来源': '真实验证集'
    })
    
    validation_simple.to_csv('真实验证样本_22.5%准确率.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 真实验证样本已保存: 真实验证样本_22.5%准确率.csv ({len(validation_simple)}行)")
    
    # 3. 创建统计对比表
    stats_comparison = pd.DataFrame({
        '统计指标': [
            '样本数量',
            '数据来源',
            '设备数量',
            '时间范围',
            '重量差异_平均值_kg',
            '重量差异_最小值_kg', 
            '重量差异_最大值_kg',
            '硅热能_平均值_kWh',
            '硅热能_最小值_kWh',
            '硅热能_最大值_kWh',
            '副功率_平均值_kWh',
            '副功率_最小值_kWh',
            '副功率_最大值_kWh',
            '首投比例_%',
            '复投比例_%',
            '±10kWh准确率_%'
        ],
        '训练测试集_71.3%': [
            len(training_df),
            'A01-A40周期文件',
            len(training_df['folder_name'].unique()),
            '2025-05-02 到 2025-07-05',
            round(training_df['weight_difference'].mean(), 1),
            round(training_df['weight_difference'].min(), 1),
            round(training_df['weight_difference'].max(), 1),
            round(training_df['silicon_thermal_energy_kwh'].mean(), 1),
            round(training_df['silicon_thermal_energy_kwh'].min(), 1),
            round(training_df['silicon_thermal_energy_kwh'].max(), 1),
            round(training_df['vice_total_energy_kwh'].mean(), 1),
            round(training_df['vice_total_energy_kwh'].min(), 1),
            round(training_df['vice_total_energy_kwh'].max(), 1),
            round(training_df['feed_type'].value_counts(normalize=True).get('首投', 0) * 100, 1),
            round(training_df['feed_type'].value_counts(normalize=True).get('复投', 0) * 100, 1),
            71.3
        ],
        '真实验证集_22.5%': [
            len(validation_df),
            '17134条记录随机抽取',
            '多设备随机分布',
            '更广泛时间范围',
            round(validation_df['weight_difference'].mean(), 1),
            round(validation_df['weight_difference'].min(), 1),
            round(validation_df['weight_difference'].max(), 1),
            round(validation_df['silicon_thermal_energy_kwh'].mean(), 1),
            round(validation_df['silicon_thermal_energy_kwh'].min(), 1),
            round(validation_df['silicon_thermal_energy_kwh'].max(), 1),
            round(validation_df['actual_vice_power'].mean(), 1),
            round(validation_df['actual_vice_power'].min(), 1),
            round(validation_df['actual_vice_power'].max(), 1),
            round(validation_df['feed_type'].value_counts(normalize=True).get('首投', 0) * 100, 1),
            round(validation_df['feed_type'].value_counts(normalize=True).get('复投', 0) * 100, 1),
            22.5
        ]
    })
    
    # 计算差异
    stats_comparison['差异'] = ''
    for i, row in stats_comparison.iterrows():
        if i >= 4 and i <= 12:  # 数值指标
            try:
                val1 = float(row['训练测试集_71.3%'])
                val2 = float(row['真实验证集_22.5%'])
                diff = val2 - val1
                pct_diff = (diff / val1) * 100 if val1 != 0 else 0
                stats_comparison.at[i, '差异'] = f"{diff:+.1f} ({pct_diff:+.1f}%)"
            except:
                stats_comparison.at[i, '差异'] = '不可比较'
        elif i == 15:  # 准确率
            stats_comparison.at[i, '差异'] = f"{22.5 - 71.3:+.1f}%"
    
    stats_comparison.to_csv('统计对比表.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 统计对比表已保存: 统计对比表.csv")
    
    # 4. 创建关键发现摘要
    key_findings = pd.DataFrame({
        '关键差异': [
            '样本数量',
            '数据来源',
            '设备覆盖',
            '重量差异均值',
            '副功率均值', 
            '首投比例',
            '数值范围',
            '准确率差异'
        ],
        '具体表现': [
            f"训练集286个 vs 验证集200个",
            f"特定文件 vs 随机抽取",
            f"3个设备 vs 多设备随机",
            f"318.8kg vs 433.9kg (差异+36%)",
            f"369.1kWh vs 452.1kWh (差异+22%)",
            f"8.7% vs 17.6% (首投样本不足)",
            f"训练集数值普遍较小",
            f"71.3% vs 22.5% (差异-48.8%)"
        ],
        '影响分析': [
            '样本量相当，不是主要因素',
            '数据来源单一 vs 全面覆盖',
            '设备局限性严重',
            '训练数据偏向小重量',
            '训练数据偏向低功率',
            '工艺类型分布不均',
            '模型学习范围受限',
            '泛化能力严重不足'
        ]
    })
    
    key_findings.to_csv('关键差异分析.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 关键差异分析已保存: 关键差异分析.csv")
    
    return True

def main():
    """主函数"""
    print("="*50)
    print("创建Excel友好的对比文件")
    print("="*50)
    
    create_excel_friendly_files()
    
    print(f"\n🎯 Excel友好文件创建完成！")
    print(f"生成的文件:")
    print(f"  1. 训练测试样本_71.3%准确率.csv")
    print(f"  2. 真实验证样本_22.5%准确率.csv") 
    print(f"  3. 统计对比表.csv")
    print(f"  4. 关键差异分析.csv")
    
    print(f"\n💡 使用建议:")
    print(f"  - 用Excel打开这些文件更容易查看")
    print(f"  - 文件名使用中文，便于识别")
    print(f"  - 数值已四舍五入，便于阅读")
    print(f"  - 统计对比表直观显示差异")

if __name__ == "__main__":
    success = main()
