import datetime

import DBUtil.share
from kongwen_power_control.beta_version.v6.model import KongwenGonglvCorrectionModel
import gevent
from gevent import Greenlet


model_name = 'kw_p'
def kongwen_realtime_powerfix_test_setup(request, model_map,r,model_version):
    if request.method == 'POST' or request.method == 'GET':
        device_id = request.json.get('device_id')
        buckets = request.json.get('buckets')
        times = request.json.get('times')
        jialiao = request.json.get('jialiao')
        power_yinjing = request.json.get('yinjing_power', 0)
        init_power = (request.json.get('init_main_power', 0), request.json.get('init_vice_power', 0))
        config = request.json.get('config', None)
        field_size = request.json.get('field_size')
        product_type = request.json.get('product_type')
        target_ccd = request.json.get('target_ccd', 1448)
        history_data = request.json.get('history_data')
        if config is None or not isinstance(config, dict):
            config = {}
        model_data = {
            'model':  KongwenGonglvCorrectionModel.from_path(
            config_path=f'kongwen_power_control//beta_version//{model_version}//model_data//config.yaml'
        ),
            'last_loaded': datetime.datetime.now()  # 更新加载时间

        }
        # 从请求中获取新增的参数
        feeding_type = request.json.get('feeding_type', 1)  # 投料类型，默认为1（复投）

        model_data['model'].setup(device_id, jialiao, times, power_yinjing, init_power, config, field_size, product_type, target_ccd, history_data, feeding_type)
        DBUtil.share.save_model_to_db(model_map,"kongwen",device_id,model_data)
        Greenlet.spawn(r.save_model, model_name, device_id, model_data,model_version)

        Greenlet.spawn(r.delete_model_other, model_name, device_id)

        return {}

def kongwen_realtime_powerfix_test(request, model_map,r,model_version):
    if request.method == 'POST' or request.method == 'GET':
        time1 = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        device_id = request.json.get('device_id')
        t = request.json.get('t')
        ratio = request.json.get('ratio')
        ccd = request.json.get('ccd')
        ccd3 = request.json.get('ccd3')
        fullmelting = request.json.get('fullmelting')
        sum_jialiao_time = request.json.get('sum_jialiao_time',0)
        last_jialiao_time = request.json.get('last_jialiao_time',0)
        last_jialiao_weight = request.json.get('last_jialiao_weight',0)
        last_Interval_time = request.json.get('last_Interval_time',0)
        barrelage = request.json.get('barrelage',0)
        last_but_one_jialiao_weight = request.json.get('last_but_one_jialiao_weight', 0)
        last_but_one_jialiao_time = request.json.get('last_but_one_jialiao_time', 0)
        last_but_one_jialiao_interval_time = request.json.get('last_but_one_Interval_time', 0)
        film_ratio = request.json.get('film_ratio', 0)
        turnover_ratio = request.json.get('turnover_ratio',0)
        # 新增参数：时间间隔和累积加料重量
        time_interval = request.json.get('time_interval')  # 时间间隔（秒）
        cumulative_feed_weight = request.json.get('cumulative_feed_weight')  # 累积加料重量（kg）
        data = [[t, ratio, ccd]]
        if isinstance(last_but_one_jialiao_interval_time, tuple):
            last_but_one_jialiao_interval_time = last_but_one_jialiao_interval_time[0]
        if isinstance(last_but_one_jialiao_time, tuple):
            last_but_one_jialiao_time = last_but_one_jialiao_time[0]

        ccd3 = ccd3 if ccd3 is not None else 1455
        fullmelting = fullmelting if fullmelting is not None else 0

        _, _, model_data = DBUtil.share.load_model_from_db(model_map, "kongwen", device_id)
        print("当前时间：炉台号", datetime.datetime.now(), device_id, model_name)
        if model_data is None or (datetime.datetime.now() - model_data['last_loaded']).total_seconds() > 180:

            if model_data is None:
                print(f"{device_id} not in model_map['kongwen']")
            else:
                print("超过三分钟")
            print('模型不在内存中，或者已超过180秒，重新加载', model_name, device_id, model_version)
            model = Greenlet.spawn(r.load_model, model_name, device_id,model_version)
            gevent.joinall([model])  # 等待异步任务完成
            model = model.value
            print("redis缓存时间", model['last_loaded'], device_id, model_name)
            if model:
                model_data = {
                    'model': model['model'],
                    'last_loaded': datetime.datetime.now()  # 更新加载时间
                }
        print("本地缓存时间", model_data['last_loaded'], device_id, model_name)
        # 获取模型并进行预测（现在返回三个值，第三个值是列表）
        main_power, vice_power, vice_power_info = model_data['model'].predict(t, ratio, ccd, ccd3,fullmelting,sum_jialiao_time,
                                                                         last_jialiao_time,last_jialiao_weight,last_Interval_time,barrelage,
                                                                         last_but_one_jialiao_weight,last_but_one_jialiao_time,last_but_one_jialiao_interval_time,film_ratio,turnover_ratio,time_interval,cumulative_feed_weight)

        # 解包副功率信息列表
        real_time_vice_power, current_cumulative_power, predicted_total_power = vice_power_info

        model_data['last_loaded'] = datetime.datetime.now()
        DBUtil.share.save_model_to_db(model_map,"kongwen",device_id,model_data)
        # 异步保存模型
        Greenlet.spawn(r.save_model, model_name, device_id, model_data,model_version)

        return {
            "main_power": float(main_power),
            "vice_power": float(vice_power),
            "real_time_vice_power": float(real_time_vice_power),
            "current_cumulative_power": float(current_cumulative_power),
            "predicted_total_power": float(predicted_total_power) if predicted_total_power is not None else None
        }


def kongwen_realtime_powerfix_test_finish(request, model_map,r,model_version):
    if request.method == 'POST' or request.method == 'GET':
        device_id = request.json.get('device_id')
        end_code = request.json.get('end_code')
        _, _, model_data = DBUtil.share.load_model_from_db(model_map, "kongwen", device_id)
        if model_data is None or (datetime.datetime.now() - model_data['last_loaded']).total_seconds() > 180:
            print('模型不在内存中，或者已超过180秒，重新加载')
            model = Greenlet.spawn(r.load_model, model_name, device_id, model_version)
            gevent.joinall([model])  # 等待异步任务完成
            model = model.value
            if model:
                model_data = {
                    'model': model['model'],
                    'last_loaded': datetime.datetime.now(),
                }
        result = model_data['model'].finish(end_code)
        DBUtil.share.delete_from_db(model_map,"kongwen",device_id)
        Greenlet.spawn(r.delete_model, model_name, device_id)
        return result

    


