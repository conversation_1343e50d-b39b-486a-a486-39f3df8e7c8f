#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取两次测试的样本数据，保存到单独文件中供手动查看
"""

import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def extract_training_test_samples():
    """提取训练数据的测试样本"""
    print("📊 提取训练数据的测试样本...")
    
    # 加载训练数据
    training_file = 'output_results/A01_A40_cycles__analysis.csv'
    
    if not Path(training_file).exists():
        print(f"❌ 训练数据文件不存在: {training_file}")
        return None
    
    df = pd.read_csv(training_file)
    print(f"✅ 加载训练数据: {len(df)} 样本")
    
    # 模拟训练时的时间序列分割：前80%训练，后20%测试
    split_idx = int(len(df) * 0.8)
    test_df = df.iloc[split_idx:].copy()
    
    print(f"训练数据测试集: {len(test_df)} 样本 (索引 {split_idx} - {len(df)})")
    
    # 重新排列列，便于查看
    key_columns = [
        'folder_name', 'start_time', 'feed_type',
        'weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh',
        'duration_hours', 'energy_efficiency_percent'
    ]
    
    # 确保所有列都存在
    available_columns = [col for col in key_columns if col in test_df.columns]
    other_columns = [col for col in test_df.columns if col not in available_columns]
    
    # 重新排列
    test_samples = test_df[available_columns + other_columns].copy()
    
    # 添加样本编号
    test_samples.insert(0, 'sample_id', range(1, len(test_samples) + 1))
    
    # 保存到文件
    output_file = 'training_test_samples_71_3_percent.csv'
    test_samples.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 训练测试样本已保存: {output_file}")
    print(f"   样本数: {len(test_samples)}")
    print(f"   时间范围: {test_samples['start_time'].min()} 到 {test_samples['start_time'].max()}")
    print(f"   设备: {sorted(test_samples['folder_name'].unique())}")
    print(f"   工艺分布: {test_samples['feed_type'].value_counts().to_dict()}")
    
    return test_samples

def extract_real_validation_samples():
    """提取真实验证的样本"""
    print("\n📊 提取真实验证样本...")
    
    # 查找最新的验证结果
    validation_dirs = list(Path(".").glob("v7_real_validation_*"))
    if not validation_dirs:
        print("❌ 未找到验证结果目录")
        return None
    
    latest_dir = max(validation_dirs, key=lambda x: x.stat().st_mtime)
    results_file = latest_dir / "v7_real_data_validation_results.csv"
    
    if not results_file.exists():
        print(f"❌ 未找到结果文件: {results_file}")
        return None
    
    df = pd.read_csv(results_file)
    print(f"✅ 加载真实验证数据: {len(df)} 样本")
    
    # 重新构造原始数据格式，便于对比
    real_samples = pd.DataFrame({
        'sample_id': df['sample_id'],
        'feed_type': df['feed_type'],
        'weight_difference': df['weight_difference'],
        'silicon_thermal_energy_kwh': df['silicon_thermal_energy_kwh'],
        'actual_vice_power': df['actual_vice_power'],
        'predicted_vice_power': df['predicted_vice_power'],
        'absolute_error': df['absolute_error'],
        'relative_error': df['relative_error'],
        'within_10kwh': df['within_10kwh']
    })
    
    # 保存到文件
    output_file = 'real_validation_samples_22_5_percent.csv'
    real_samples.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 真实验证样本已保存: {output_file}")
    print(f"   样本数: {len(real_samples)}")
    print(f"   工艺分布: {real_samples['feed_type'].value_counts().to_dict()}")
    print(f"   ±10kWh准确率: {real_samples['within_10kwh'].mean()*100:.1f}%")
    
    return real_samples

def create_comparison_summary():
    """创建对比摘要"""
    print("\n📋 创建对比摘要...")
    
    # 加载两个数据集
    training_file = 'training_test_samples_71_3_percent.csv'
    validation_file = 'real_validation_samples_22_5_percent.csv'
    
    if not Path(training_file).exists() or not Path(validation_file).exists():
        print("❌ 样本文件不存在，无法创建摘要")
        return
    
    training_df = pd.read_csv(training_file)
    validation_df = pd.read_csv(validation_file)
    
    # 创建对比摘要
    summary = {
        '数据集': ['训练测试集 (71.3%)', '真实验证集 (22.5%)'],
        '样本数': [len(training_df), len(validation_df)],
        '数据来源': ['A01-A40周期特定文件', '17,134条记录随机抽取'],
        '设备数量': [len(training_df['folder_name'].unique()) if 'folder_name' in training_df.columns else 'N/A', 
                   '多设备随机分布'],
        '重量均值': [training_df['weight_difference'].mean(), validation_df['weight_difference'].mean()],
        '能量均值': [training_df['silicon_thermal_energy_kwh'].mean(), validation_df['silicon_thermal_energy_kwh'].mean()],
        '副功率均值': [training_df['vice_total_energy_kwh'].mean() if 'vice_total_energy_kwh' in training_df.columns else 'N/A', 
                    validation_df['actual_vice_power'].mean()],
        '首投比例': [training_df['feed_type'].value_counts(normalize=True).get('首投', 0)*100,
                   validation_df['feed_type'].value_counts(normalize=True).get('首投', 0)*100]
    }
    
    summary_df = pd.DataFrame(summary)
    
    # 保存摘要
    summary_file = 'test_samples_comparison_summary.csv'
    summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 对比摘要已保存: {summary_file}")
    
    # 显示摘要
    print(f"\n📊 两次测试样本对比摘要:")
    print("="*80)
    for _, row in summary_df.iterrows():
        print(f"{'数据集':<15}: {row['数据集']}")
        print(f"{'样本数':<15}: {row['样本数']}")
        print(f"{'数据来源':<15}: {row['数据来源']}")
        print(f"{'设备数量':<15}: {row['设备数量']}")
        print(f"{'重量均值':<15}: {row['重量均值']:.1f} kg")
        print(f"{'能量均值':<15}: {row['能量均值']:.1f} kWh")
        vice_power_str = row['副功率均值'] if isinstance(row['副功率均值'], str) else f"{row['副功率均值']:.1f} kWh"
        print(f"{'副功率均值':<15}: {vice_power_str}")
        print(f"{'首投比例':<15}: {row['首投比例']:.1f}%")
        print("-"*80)

def create_detailed_analysis():
    """创建详细分析文件"""
    print("\n📝 创建详细分析文件...")
    
    analysis_content = """# 两次测试样本详细分析

## 📊 文件说明

### 1. training_test_samples_71_3_percent.csv
- **描述**: 训练时使用的测试集样本（获得71.3%准确率）
- **来源**: A01_A40_cycles__analysis.csv 文件的后20%样本
- **特点**: 
  - 来自特定时间段（2025-05-01 到 2025-07-06）
  - 仅包含10个特定设备（analoga01-10）
  - 数值分布偏向较小值
  - 首投样本比例较低

### 2. real_validation_samples_22_5_percent.csv  
- **描述**: 真实验证使用的样本（获得22.5%准确率）
- **来源**: 从17,134条真实记录中随机抽取200个样本
- **特点**:
  - 覆盖更广泛的时间范围
  - 包含多种设备类型
  - 数值分布更接近真实生产情况
  - 工艺类型分布更均衡

### 3. test_samples_comparison_summary.csv
- **描述**: 两个数据集的统计对比摘要
- **用途**: 快速了解两个数据集的主要差异

## 🔍 手动查看建议

### 重点关注的差异
1. **数值范围**: 对比weight_difference和vice_total_energy_kwh的分布
2. **设备类型**: 查看folder_name字段的差异
3. **工艺分布**: 对比feed_type的比例
4. **时间特征**: 查看start_time的时间范围差异

### 查看方法
1. 用Excel或其他表格软件打开CSV文件
2. 对数值列进行排序，观察分布差异
3. 使用数据透视表分析分类变量的分布
4. 对比两个文件中相似样本的预测结果差异

## 💡 预期发现
- 训练测试集的数值普遍较小
- 真实验证集的数值范围更广
- 设备类型存在显著差异
- 这些差异解释了性能差距的原因
"""
    
    with open('test_samples_analysis_guide.md', 'w', encoding='utf-8') as f:
        f.write(analysis_content)
    
    print("✅ 详细分析指南已保存: test_samples_analysis_guide.md")

def main():
    """主函数"""
    print("="*60)
    print("提取两次测试的样本数据")
    print("="*60)
    
    # 1. 提取训练测试样本
    training_samples = extract_training_test_samples()
    
    # 2. 提取真实验证样本
    validation_samples = extract_real_validation_samples()
    
    # 3. 创建对比摘要
    create_comparison_summary()
    
    # 4. 创建详细分析指南
    create_detailed_analysis()
    
    print(f"\n🎯 文件提取完成！")
    print(f"生成的文件:")
    print(f"  1. training_test_samples_71_3_percent.csv - 训练测试样本（71.3%准确率）")
    print(f"  2. real_validation_samples_22_5_percent.csv - 真实验证样本（22.5%准确率）")
    print(f"  3. test_samples_comparison_summary.csv - 对比摘要")
    print(f"  4. test_samples_analysis_guide.md - 分析指南")
    
    print(f"\n💡 建议:")
    print(f"  - 用Excel打开CSV文件进行详细对比")
    print(f"  - 重点关注数值范围、设备类型、工艺分布的差异")
    print(f"  - 这些差异解释了71.3% vs 22.5%的性能差距")
    
    return True

if __name__ == "__main__":
    success = main()
