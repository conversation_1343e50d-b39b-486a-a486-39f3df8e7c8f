#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速环境检查脚本
"""

import os
import sys
from pathlib import Path

def quick_environment_check():
    """快速环境检查"""
    print("="*60)
    print("🔍 快速环境检查")
    print("="*60)
    
    # 检查当前环境
    current_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    python_path = sys.executable
    
    print(f"📍 当前Conda环境: {current_env}")
    print(f"🐍 Python版本: {python_version}")
    print(f"🔧 Python路径: {python_path}")
    
    # 检查是否在lj_env_1环境中
    required_env = "lj_env_1"
    env_match = current_env == required_env
    
    print(f"\n📋 项目要求环境: {required_env}")
    
    if env_match:
        print("✅ 环境匹配！当前正在使用正确的训练环境")
    else:
        print(f"⚠️  环境不匹配！当前环境: {current_env}")
        print(f"   需要切换到: {required_env}")
    
    # 检查关键依赖包
    print(f"\n📦 关键依赖包检查:")
    packages = ['pandas', 'numpy', 'sklearn', 'joblib']
    
    for package in packages:
        try:
            if package == 'sklearn':
                import sklearn
                version = sklearn.__version__
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
            print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: 未安装")
    
    # 检查模型文件
    print(f"\n🗂️  关键模型文件检查:")
    model_files = [
        "副功率预测_85.4%准确率_完整项目/models/best_model_svr.joblib",
        "副功率预测_85.4%准确率_完整项目/models/results.json",
        "production_ready_models/model_version.json"
    ]
    
    for model_file in model_files:
        if Path(model_file).exists():
            print(f"✅ {model_file}")
        else:
            print(f"❌ {model_file} (不存在)")
    
    # 总结和建议
    print(f"\n📝 总结:")
    if env_match:
        print("✅ 环境配置正确，可以运行模型训练")
    else:
        print("⚠️  需要切换到lj_env_1环境")
        print("\n🔧 切换步骤:")
        print("1. conda activate lj_env_1")
        print("2. 如果环境不存在: conda create -n lj_env_1 python=3.8")
        print("3. 安装依赖: pip install pandas numpy scikit-learn joblib")
    
    return {
        'current_env': current_env,
        'env_match': env_match,
        'python_version': python_version
    }

if __name__ == "__main__":
    result = quick_environment_check()
