#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专注于output_results数据，提高副功率预测准确率到70%以上
"""

import os
import pandas as pd
import numpy as np
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def load_and_analyze_output_data():
    """加载并分析output_results数据"""
    print("="*60)
    print("加载并分析output_results数据")
    print("="*60)
    
    # 1. 加载主数据集
    main_file = Path("output_results/all_folders_summary.csv")
    if not main_file.exists():
        print(f"❌ 主数据文件不存在: {main_file}")
        return None
    
    df = pd.read_csv(main_file)
    print(f"✅ 加载主数据集: {len(df)} 行, {len(df.columns)} 列")
    print(f"列名: {list(df.columns)}")
    
    # 2. 数据质量分析
    print(f"\n数据质量分析:")
    print(f"  缺失值: {df.isnull().sum().sum()}")
    print(f"  重复行: {df.duplicated().sum()}")
    
    # 3. 目标变量分析
    target_col = 'vice_total_energy_kwh'
    if target_col not in df.columns:
        print(f"❌ 未找到目标变量: {target_col}")
        return None
    
    y = df[target_col]
    print(f"\n目标变量分析 ({target_col}):")
    print(f"  数值范围: {y.min():.1f} - {y.max():.1f} kWh")
    print(f"  均值: {y.mean():.1f} kWh")
    print(f"  标准差: {y.std():.1f} kWh")
    print(f"  分位数: {y.quantile([0.25, 0.5, 0.75]).to_dict()}")
    
    # 4. 特征变量分析
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    feature_cols = [col for col in numeric_cols if col != target_col]
    
    print(f"\n特征变量分析 ({len(feature_cols)}个数值特征):")
    correlations = []
    for col in feature_cols:
        corr = df[col].corr(df[target_col])
        correlations.append((col, corr))
        print(f"  {col}: 相关性 {corr:.4f}")
    
    # 按相关性排序
    correlations.sort(key=lambda x: abs(x[1]), reverse=True)
    top_features = [col for col, corr in correlations[:10]]
    
    print(f"\n前10个最相关特征:")
    for col, corr in correlations[:10]:
        print(f"  {col}: {corr:.4f}")
    
    return df, target_col, feature_cols, top_features

def comprehensive_feature_engineering(df, target_col, feature_cols):
    """全面的特征工程"""
    print(f"\n" + "="*60)
    print("全面的特征工程")
    print("="*60)
    
    df_features = df.copy()
    
    # 1. 基础特征处理
    print(f"1. 基础特征处理:")
    numeric_features = []
    for col in feature_cols:
        if df[col].dtype in ['int64', 'float64'] and not df[col].isnull().all():
            # 异常值处理
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            if IQR > 0:
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                if outliers > 0:
                    print(f"  {col}: 处理 {outliers} 个异常值")
                    df_features[col] = df[col].clip(lower_bound, upper_bound)
            numeric_features.append(col)
    
    print(f"  保留 {len(numeric_features)} 个基础特征")
    
    # 2. 物理意义特征
    print(f"\n2. 创建物理意义特征:")
    physics_features = 0
    
    # 能量效率相关
    if 'silicon_thermal_energy_kwh' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['energy_per_kg'] = df_features['silicon_thermal_energy_kwh'] / (df_features['weight_difference'] + 1e-6)
        physics_features += 1
    
    # 功率密度
    if 'first_crystal_seeding_main_power_kw' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['power_density'] = df_features['first_crystal_seeding_main_power_kw'] / (df_features['weight_difference'] + 1e-6)
        physics_features += 1
    
    # 时间效率
    if 'duration_hours' in df_features.columns and 'weight_difference' in df_features.columns:
        df_features['kg_per_hour'] = df_features['weight_difference'] / (df_features['duration_hours'] + 1e-6)
        physics_features += 1
    
    # 能量比率
    if 'silicon_thermal_energy_kwh' in df_features.columns and 'vice_total_energy_kwh' in df_features.columns:
        df_features['main_vice_energy_ratio'] = df_features['silicon_thermal_energy_kwh'] / (df_features['vice_total_energy_kwh'] + 1e-6)
        physics_features += 1
    
    print(f"  创建 {physics_features} 个物理意义特征")
    
    # 3. 多项式特征
    print(f"\n3. 创建多项式特征:")
    poly_features = 0
    key_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'duration_hours']
    
    for col in key_features:
        if col in df_features.columns:
            # 平方特征
            df_features[f"{col}_squared"] = df_features[col] ** 2
            # 平方根特征
            df_features[f"{col}_sqrt"] = np.sqrt(np.abs(df_features[col]))
            # 对数特征
            df_features[f"{col}_log"] = np.log(np.abs(df_features[col]) + 1)
            poly_features += 3
    
    print(f"  创建 {poly_features} 个多项式特征")
    
    # 4. 交互特征
    print(f"\n4. 创建交互特征:")
    interaction_features = 0
    important_pairs = [
        ('weight_difference', 'silicon_thermal_energy_kwh'),
        ('weight_difference', 'duration_hours'),
        ('silicon_thermal_energy_kwh', 'duration_hours')
    ]
    
    for col1, col2 in important_pairs:
        if col1 in df_features.columns and col2 in df_features.columns:
            # 乘积特征
            df_features[f"{col1}_x_{col2}"] = df_features[col1] * df_features[col2]
            # 比值特征
            df_features[f"{col1}_div_{col2}"] = df_features[col1] / (df_features[col2] + 1e-6)
            interaction_features += 2
    
    print(f"  创建 {interaction_features} 个交互特征")
    
    # 5. 分类特征编码
    print(f"\n5. 分类特征编码:")
    categorical_features = 0
    
    if 'feed_type' in df_features.columns:
        # One-hot编码
        feed_dummies = pd.get_dummies(df_features['feed_type'], prefix='feed_type')
        df_features = pd.concat([df_features, feed_dummies], axis=1)
        categorical_features += len(feed_dummies.columns)
    
    if 'folder_name' in df_features.columns:
        # 设备编码（频率编码）
        device_counts = df_features['folder_name'].value_counts()
        df_features['device_frequency'] = df_features['folder_name'].map(device_counts)
        categorical_features += 1
    
    print(f"  创建 {categorical_features} 个分类特征")
    
    # 6. 获取所有特征
    all_features = [col for col in df_features.columns 
                   if col != target_col and df_features[col].dtype in ['int64', 'float64']]
    
    print(f"\n特征工程总结:")
    print(f"  原始特征: {len(numeric_features)}")
    print(f"  总特征数: {len(all_features)}")
    print(f"  新增特征: {len(all_features) - len(numeric_features)}")
    
    return df_features, all_features

def advanced_model_training(df_features, target_col, all_features):
    """高级模型训练"""
    print(f"\n" + "="*60)
    print("高级模型训练")
    print("="*60)
    
    # 准备数据
    X = df_features[all_features].fillna(0)
    y = df_features[target_col]
    
    # 移除无限值
    mask = np.isfinite(X).all(axis=1) & np.isfinite(y)
    X = X[mask]
    y = y[mask]
    
    print(f"数据准备:")
    print(f"  样本数: {len(X)}")
    print(f"  特征数: {len(all_features)}")
    print(f"  目标范围: {y.min():.1f} - {y.max():.1f} kWh")
    
    # 时间序列分割
    if 'start_time' in df_features.columns:
        df_features['start_time'] = pd.to_datetime(df_features['start_time'])
        sorted_indices = df_features[mask].sort_values('start_time').index
        split_idx = int(len(sorted_indices) * 0.8)
        train_idx = sorted_indices[:split_idx]
        test_idx = sorted_indices[split_idx:]
    else:
        # 随机分割
        from sklearn.model_selection import train_test_split
        train_idx, test_idx = train_test_split(X.index, test_size=0.2, random_state=42)
    
    X_train, X_test = X.loc[train_idx], X.loc[test_idx]
    y_train, y_test = y.loc[train_idx], y.loc[test_idx]
    
    print(f"  训练集: {len(X_train)} 样本")
    print(f"  测试集: {len(X_test)} 样本")
    
    # 特征选择和预处理
    from sklearn.feature_selection import SelectKBest, f_regression
    from sklearn.preprocessing import StandardScaler
    
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 特征选择
    k_best = min(30, len(all_features))
    selector = SelectKBest(score_func=f_regression, k=k_best)
    X_train_selected = selector.fit_transform(X_train_scaled, y_train)
    X_test_selected = selector.transform(X_test_scaled)
    
    selected_features = [all_features[i] for i in selector.get_support(indices=True)]
    print(f"  选择了 {len(selected_features)} 个最佳特征")
    
    # 训练多个高级模型
    models = {}
    results = {}
    
    # 1. 随机森林（优化参数）
    print(f"\n1. 优化随机森林:")
    from sklearn.ensemble import RandomForestRegressor
    
    rf = RandomForestRegressor(
        n_estimators=300,
        max_depth=20,
        min_samples_split=5,
        min_samples_leaf=2,
        max_features='sqrt',
        random_state=42,
        n_jobs=-1
    )
    rf.fit(X_train_selected, y_train)
    rf_pred = rf.predict(X_test_selected)
    rf_acc = evaluate_model(y_test, rf_pred, "优化随机森林")
    models['random_forest'] = rf
    results['random_forest'] = rf_acc
    
    # 2. 梯度提升（优化参数）
    print(f"\n2. 优化梯度提升:")
    from sklearn.ensemble import GradientBoostingRegressor
    
    gb = GradientBoostingRegressor(
        n_estimators=300,
        max_depth=10,
        learning_rate=0.05,
        min_samples_split=5,
        min_samples_leaf=2,
        subsample=0.8,
        random_state=42
    )
    gb.fit(X_train_selected, y_train)
    gb_pred = gb.predict(X_test_selected)
    gb_acc = evaluate_model(y_test, gb_pred, "优化梯度提升")
    models['gradient_boosting'] = gb
    results['gradient_boosting'] = gb_acc
    
    # 3. XGBoost
    print(f"\n3. XGBoost:")
    try:
        import xgboost as xgb
        
        xgb_model = xgb.XGBRegressor(
            n_estimators=300,
            max_depth=10,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1
        )
        xgb_model.fit(X_train_selected, y_train)
        xgb_pred = xgb_model.predict(X_test_selected)
        xgb_acc = evaluate_model(y_test, xgb_pred, "XGBoost")
        models['xgboost'] = xgb_model
        results['xgboost'] = xgb_acc
    except ImportError:
        print("  XGBoost未安装，跳过")
        xgb_acc = 0
    
    # 4. LightGBM
    print(f"\n4. LightGBM:")
    try:
        import lightgbm as lgb
        
        lgb_model = lgb.LGBMRegressor(
            n_estimators=300,
            max_depth=10,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1,
            verbose=-1
        )
        lgb_model.fit(X_train_selected, y_train)
        lgb_pred = lgb_model.predict(X_test_selected)
        lgb_acc = evaluate_model(y_test, lgb_pred, "LightGBM")
        models['lightgbm'] = lgb_model
        results['lightgbm'] = lgb_acc
    except ImportError:
        print("  LightGBM未安装，跳过")
        lgb_acc = 0
    
    # 5. 支持向量回归
    print(f"\n5. 支持向量回归:")
    from sklearn.svm import SVR
    
    svr = SVR(kernel='rbf', C=100, gamma='scale', epsilon=0.1)
    svr.fit(X_train_selected, y_train)
    svr_pred = svr.predict(X_test_selected)
    svr_acc = evaluate_model(y_test, svr_pred, "支持向量回归")
    models['svr'] = svr
    results['svr'] = svr_acc
    
    # 6. 神经网络
    print(f"\n6. 神经网络:")
    from sklearn.neural_network import MLPRegressor
    
    mlp = MLPRegressor(
        hidden_layer_sizes=(100, 50, 25),
        activation='relu',
        solver='adam',
        alpha=0.01,
        learning_rate='adaptive',
        max_iter=500,
        random_state=42
    )
    mlp.fit(X_train_selected, y_train)
    mlp_pred = mlp.predict(X_test_selected)
    mlp_acc = evaluate_model(y_test, mlp_pred, "神经网络")
    models['neural_network'] = mlp
    results['neural_network'] = mlp_acc
    
    # 7. 加权集成
    print(f"\n7. 加权集成:")
    predictions = []
    weights = []
    
    for name, acc in results.items():
        if name in models and acc > 0:
            pred = models[name].predict(X_test_selected)
            predictions.append(pred)
            weights.append(acc)  # 使用准确率作为权重
    
    if predictions:
        weights = np.array(weights)
        weights = weights / weights.sum()  # 归一化权重
        
        ensemble_pred = np.zeros(len(y_test))
        for pred, weight in zip(predictions, weights):
            ensemble_pred += pred * weight
        
        ensemble_acc = evaluate_model(y_test, ensemble_pred, "加权集成")
        results['weighted_ensemble'] = ensemble_acc
    
    # 找到最佳模型
    best_model_name = max(results.keys(), key=lambda k: results[k])
    best_acc = results[best_model_name]
    
    print(f"\n🎯 最佳模型: {best_model_name}")
    print(f"   ±10kWh准确率: {best_acc:.1f}%")
    
    if best_acc >= 70:
        print(f"   ✅ 达到70%目标！")
    else:
        print(f"   ❌ 未达到70%目标，差距: {70-best_acc:.1f}%")
    
    return models, results, best_model_name, selected_features, scaler, selector

def evaluate_model(y_true, y_pred, model_name):
    """评估模型"""
    errors = np.abs(y_true - y_pred)
    mae = errors.mean()
    rmse = np.sqrt(((y_true - y_pred) ** 2).mean())
    
    acc_5 = (errors <= 5).mean() * 100
    acc_10 = (errors <= 10).mean() * 100
    acc_15 = (errors <= 15).mean() * 100
    acc_20 = (errors <= 20).mean() * 100
    
    print(f"  {model_name}结果:")
    print(f"    MAE: {mae:.2f} kWh")
    print(f"    RMSE: {rmse:.2f} kWh")
    print(f"    ±5kWh准确率: {acc_5:.1f}%")
    print(f"    ±10kWh准确率: {acc_10:.1f}%")
    print(f"    ±15kWh准确率: {acc_15:.1f}%")
    print(f"    ±20kWh准确率: {acc_20:.1f}%")
    
    return acc_10

def save_results(models, results, best_model_name, selected_features, scaler, selector):
    """保存结果"""
    print(f"\n" + "="*60)
    print("保存结果")
    print("="*60)
    
    import joblib
    import json
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建结果目录
    result_dir = Path(f"improved_models_{timestamp}")
    result_dir.mkdir(exist_ok=True)
    
    # 保存最佳模型
    if best_model_name in models:
        joblib.dump(models[best_model_name], result_dir / f"best_model_{best_model_name}.joblib")
        print(f"✅ 保存最佳模型: {best_model_name}")
    
    # 保存预处理器
    joblib.dump(scaler, result_dir / "scaler.joblib")
    joblib.dump(selector, result_dir / "feature_selector.joblib")
    
    # 保存结果报告
    report = {
        'timestamp': timestamp,
        'best_model': best_model_name,
        'best_accuracy': float(results[best_model_name]),
        'all_results': {k: float(v) for k, v in results.items()},
        'selected_features': selected_features,
        'achieved_70_percent': bool(results[best_model_name] >= 70)
    }
    
    with open(result_dir / "results.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成报告
    markdown_report = f"""# 副功率预测改进结果

## 🎯 目标达成情况
- **目标**: ±10kWh准确率 ≥ 70%
- **最佳结果**: {results[best_model_name]:.1f}%
- **达成状态**: {'✅ 已达成' if results[best_model_name] >= 70 else '❌ 未达成'}

## 📊 模型性能排行
"""
    
    for model_name, acc in sorted(results.items(), key=lambda x: x[1], reverse=True):
        status = "✅" if acc >= 70 else "❌"
        markdown_report += f"- **{model_name}**: {acc:.1f}% {status}\n"
    
    markdown_report += f"""
## 🔧 最佳模型详情
- **模型**: {best_model_name}
- **准确率**: {results[best_model_name]:.1f}%
- **特征数**: {len(selected_features)}

## 📋 选择的特征
{chr(10).join([f"- {feature}" for feature in selected_features[:15]])}
{'...' if len(selected_features) > 15 else ''}
"""
    
    with open(result_dir / "report.md", 'w', encoding='utf-8') as f:
        f.write(markdown_report)
    
    print(f"✅ 结果已保存到: {result_dir}")
    
    return result_dir

def main():
    """主函数"""
    print("专注于output_results数据，提高副功率预测准确率到70%以上")
    print("="*60)
    
    # 环境检查
    if not check_environment():
        return False
    
    # 1. 加载并分析数据
    result = load_and_analyze_output_data()
    if result is None:
        return False
    
    df, target_col, feature_cols, top_features = result
    
    # 2. 全面特征工程
    df_features, all_features = comprehensive_feature_engineering(df, target_col, feature_cols)
    
    # 3. 高级模型训练
    models, results, best_model_name, selected_features, scaler, selector = advanced_model_training(df_features, target_col, all_features)
    
    # 4. 保存结果
    result_dir = save_results(models, results, best_model_name, selected_features, scaler, selector)
    
    # 5. 最终结果
    best_acc = results[best_model_name]
    print(f"\n🎯 任务完成！")
    print(f"最佳模型: {best_model_name}")
    print(f"±10kWh准确率: {best_acc:.1f}%")
    
    if best_acc >= 70:
        print(f"✅ 成功达到70%目标！")
        return True
    else:
        print(f"❌ 未达到70%目标，差距: {70-best_acc:.1f}%")
        return False

if __name__ == "__main__":
    success = main()
