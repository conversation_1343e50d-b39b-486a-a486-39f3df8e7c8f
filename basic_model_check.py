#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础模型检查脚本
"""

import os
import sys
import pandas as pd
import json
from pathlib import Path
from datetime import datetime

def main():
    """主函数"""
    print("="*60)
    print("🔍 基础模型和数据检查")
    print("="*60)
    
    # 1. 环境检查
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print(f"当前环境: {conda_env}")
    
    if conda_env == 'lj_env_1':
        print("✅ 环境正确：lj_env_1")
    else:
        print(f"⚠️ 环境警告：当前为 {conda_env}，建议使用 lj_env_1")
    
    # 2. 检查数据文件
    print(f"\n📊 检查数据文件:")
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    
    if data_file.exists():
        print(f"✅ 数据文件存在: {data_file}")
        try:
            df = pd.read_csv(data_file)
            print(f"📊 数据形状: {df.shape}")
            print(f"📋 主要列: {list(df.columns)[:10]}")
            
            # 检查关键列
            key_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
            for col in key_cols:
                if col in df.columns:
                    print(f"  ✅ {col}: {df[col].min():.1f} - {df[col].max():.1f}")
                else:
                    print(f"  ❌ 缺少列: {col}")
        except Exception as e:
            print(f"❌ 数据读取失败: {e}")
    else:
        print(f"❌ 数据文件不存在: {data_file}")
    
    # 3. 检查模型文件
    print(f"\n🔍 检查模型文件:")
    
    # SVR模型
    svr_path = Path("副功率预测_85.4%准确率_完整项目/models")
    svr_model = svr_path / "best_model_svr.joblib"
    if svr_model.exists():
        print(f"✅ SVR模型存在: {svr_model}")
        print(f"  文件大小: {svr_model.stat().st_size / (1024*1024):.2f} MB")
    else:
        print(f"❌ SVR模型不存在: {svr_model}")
    
    # 生产模型
    prod_path = Path("production_ready_models")
    prod_model = prod_path / "ensemble_model.joblib"
    if prod_model.exists():
        print(f"✅ 生产模型存在: {prod_model}")
        print(f"  文件大小: {prod_model.stat().st_size / (1024*1024):.2f} MB")
    else:
        print(f"❌ 生产模型不存在: {prod_model}")
    
    # 实时模型
    realtime_path = Path("realtime_vice_power_models")
    realtime_model = realtime_path / "ensemble_model.joblib"
    if realtime_model.exists():
        print(f"✅ 实时模型存在: {realtime_model}")
        print(f"  文件大小: {realtime_model.stat().st_size / (1024*1024):.2f} MB")
    else:
        print(f"❌ 实时模型不存在: {realtime_model}")
    
    # 4. 检查依赖包
    print(f"\n📦 检查依赖包:")
    packages = ['pandas', 'numpy', 'sklearn', 'joblib']
    
    for package in packages:
        try:
            if package == 'sklearn':
                import sklearn
                version = sklearn.__version__
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
            print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: 未安装")
    
    # 5. 简单测试
    print(f"\n🧪 简单功能测试:")
    try:
        import joblib
        import numpy as np
        
        # 测试基本功能
        test_array = np.array([1, 2, 3])
        print(f"✅ numpy测试: {test_array}")
        
        # 如果SVR模型存在，尝试加载
        if svr_model.exists():
            try:
                model = joblib.load(svr_model)
                print(f"✅ SVR模型加载成功: {type(model)}")
            except Exception as e:
                print(f"❌ SVR模型加载失败: {e}")
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
    
    # 6. 生成报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        'timestamp': timestamp,
        'environment': conda_env,
        'data_file_exists': data_file.exists(),
        'svr_model_exists': svr_model.exists(),
        'production_model_exists': prod_model.exists(),
        'realtime_model_exists': realtime_model.exists()
    }
    
    report_file = f"basic_check_report_{timestamp}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 检查报告已保存: {report_file}")
    print("✅ 基础检查完成")

if __name__ == "__main__":
    main()
