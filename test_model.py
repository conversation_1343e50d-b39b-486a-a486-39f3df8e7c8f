#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保存的实时副功率预测模型
"""

import sys
sys.path.append('.')
from realtime_vice_power_models.realtime_predictor import RealtimePredictor

def test_model():
    """测试模型功能"""
    print("开始测试保存的实时副功率预测模型...")
    
    # 初始化预测器
    predictor = RealtimePredictor(model_dir='realtime_vice_power_models')
    
    # 测试案例
    test_cases = [
        {
            'name': '复投工艺 - 中等重量',
            'weight_difference': 320.5,
            'silicon_thermal_energy_kwh': 280.3,
            'feed_type': '复投'
        },
        {
            'name': '首投工艺 - 轻量',
            'weight_difference': 150.2,
            'silicon_thermal_energy_kwh': 130.8,
            'feed_type': '首投'
        },
        {
            'name': '复投工艺 - 重量',
            'weight_difference': 450.0,
            'silicon_thermal_energy_kwh': 380.5,
            'feed_type': '复投'
        }
    ]
    
    print(f"\n执行 {len(test_cases)} 个测试案例:")
    print("-" * 60)
    
    for i, case in enumerate(test_cases, 1):
        try:
            result = predictor.predict(
                weight_difference=case['weight_difference'],
                silicon_thermal_energy_kwh=case['silicon_thermal_energy_kwh'],
                feed_type=case['feed_type']
            )
            
            print(f"测试案例 {i}: {case['name']}")
            print(f"  输入: 重量差异={case['weight_difference']}kg, 硅热能={case['silicon_thermal_energy_kwh']}kWh")
            print(f"  预测副功率: {result['predicted_power']} kWh")
            print(f"  置信度: {result['confidence']}")
            print()
            
        except Exception as e:
            print(f"测试案例 {i} 失败: {e}")
    
    print("✅ 模型测试完成！")

if __name__ == "__main__":
    test_model()
