#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的副功率预测模型准确率测试脚本
使用output_results中的数据，随机选择100组进行测试
确保在lj_env_1环境下运行
"""

import os
import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EnvironmentValidator:
    """环境验证器"""
    
    @staticmethod
    def validate_lj_env_1():
        """验证是否在lj_env_1环境中"""
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        
        print("="*60)
        print("🔍 环境验证")
        print("="*60)
        print(f"当前Conda环境: {conda_env}")
        print(f"Python版本: {python_version}")
        
        if conda_env != 'lj_env_1':
            print(f"❌ 错误：当前环境为 {conda_env}")
            print("必须在 lj_env_1 环境中运行此测试")
            print("请执行: conda activate lj_env_1")
            return False
        
        print("✅ 环境验证通过：lj_env_1")
        return True

class TestDataManager:
    """测试数据管理器"""
    
    def __init__(self):
        self.test_data = None
        self.data_stats = {}
    
    def load_and_prepare_data(self, sample_size=100):
        """加载并准备测试数据"""
        print("\n" + "="*60)
        print("📊 加载测试数据")
        print("="*60)
        
        # 主要数据文件
        data_file = Path("output_results/A01_A40_cycles__analysis.csv")
        
        if not data_file.exists():
            print(f"❌ 主数据文件不存在: {data_file}")
            return False
        
        try:
            # 加载数据
            df = pd.read_csv(data_file)
            print(f"✅ 数据文件加载成功")
            print(f"📊 原始数据形状: {df.shape}")
            print(f"📋 数据列: {list(df.columns)}")
            
            # 检查必要的列
            required_columns = [
                'weight_difference',
                'silicon_thermal_energy_kwh', 
                'vice_total_energy_kwh',
                'feed_type',
                'start_weight',
                'end_weight',
                'end_temperature_celsius',
                'first_crystal_seeding_main_power_kw',
                'duration_hours'
            ]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"❌ 缺少必要列: {missing_columns}")
                return False
            
            # 数据清洗
            df_clean = df.dropna(subset=required_columns)
            print(f"📊 清洗后数据形状: {df_clean.shape}")
            
            # 过滤异常值
            df_filtered = self._filter_outliers(df_clean)
            print(f"📊 过滤异常值后: {df_filtered.shape}")
            
            # 随机采样
            if len(df_filtered) > sample_size:
                self.test_data = df_filtered.sample(n=sample_size, random_state=42)
                print(f"📊 随机采样: {sample_size} 条")
            else:
                self.test_data = df_filtered
                print(f"📊 使用全部数据: {len(df_filtered)} 条")
            
            # 计算数据统计
            self._calculate_data_stats()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _filter_outliers(self, df):
        """过滤异常值"""
        # 过滤明显异常的数据
        filtered = df[
            (df['weight_difference'] > 0) &
            (df['weight_difference'] < 1000) &
            (df['silicon_thermal_energy_kwh'] > 0) &
            (df['silicon_thermal_energy_kwh'] < 1000) &
            (df['vice_total_energy_kwh'] > 0) &
            (df['vice_total_energy_kwh'] < 2000) &
            (df['duration_hours'] > 0) &
            (df['duration_hours'] < 24)
        ]
        
        removed_count = len(df) - len(filtered)
        if removed_count > 0:
            print(f"  过滤掉 {removed_count} 条异常数据")
        
        return filtered
    
    def _calculate_data_stats(self):
        """计算数据统计信息"""
        if self.test_data is None:
            return
        
        self.data_stats = {
            'sample_count': len(self.test_data),
            'weight_difference': {
                'min': float(self.test_data['weight_difference'].min()),
                'max': float(self.test_data['weight_difference'].max()),
                'mean': float(self.test_data['weight_difference'].mean()),
                'std': float(self.test_data['weight_difference'].std())
            },
            'silicon_thermal_energy_kwh': {
                'min': float(self.test_data['silicon_thermal_energy_kwh'].min()),
                'max': float(self.test_data['silicon_thermal_energy_kwh'].max()),
                'mean': float(self.test_data['silicon_thermal_energy_kwh'].mean()),
                'std': float(self.test_data['silicon_thermal_energy_kwh'].std())
            },
            'vice_total_energy_kwh': {
                'min': float(self.test_data['vice_total_energy_kwh'].min()),
                'max': float(self.test_data['vice_total_energy_kwh'].max()),
                'mean': float(self.test_data['vice_total_energy_kwh'].mean()),
                'std': float(self.test_data['vice_total_energy_kwh'].std())
            },
            'feed_type_distribution': self.test_data['feed_type'].value_counts().to_dict()
        }
        
        print(f"\n📈 测试数据统计:")
        print(f"  样本数量: {self.data_stats['sample_count']}")
        print(f"  重量差异: {self.data_stats['weight_difference']['min']:.1f} - {self.data_stats['weight_difference']['max']:.1f} kg (均值: {self.data_stats['weight_difference']['mean']:.1f})")
        print(f"  硅热能: {self.data_stats['silicon_thermal_energy_kwh']['min']:.1f} - {self.data_stats['silicon_thermal_energy_kwh']['max']:.1f} kWh (均值: {self.data_stats['silicon_thermal_energy_kwh']['mean']:.1f})")
        print(f"  副功率: {self.data_stats['vice_total_energy_kwh']['min']:.1f} - {self.data_stats['vice_total_energy_kwh']['max']:.1f} kWh (均值: {self.data_stats['vice_total_energy_kwh']['mean']:.1f})")
        print(f"  工艺类型: {self.data_stats['feed_type_distribution']}")

class ModelTester:
    """模型测试器"""
    
    def __init__(self, test_data):
        self.test_data = test_data
        self.models = {}
        self.test_results = {}
    
    def discover_models(self):
        """发现所有可用模型"""
        print("\n" + "="*60)
        print("🔍 发现可用模型")
        print("="*60)
        
        # 1. SVR模型 (85.4%准确率)
        self._check_svr_model()
        
        # 2. 生产集成模型 (71.3%准确率)
        self._check_production_model()
        
        # 3. 实时集成模型
        self._check_realtime_model()
        
        print(f"\n📊 总共发现 {len(self.models)} 个可用模型")
        return len(self.models) > 0
    
    def _check_svr_model(self):
        """检查SVR模型"""
        model_path = Path("副功率预测_85.4%准确率_完整项目/models")
        
        required_files = {
            'model': model_path / "best_model_svr.joblib",
            'scaler': model_path / "scaler.joblib",
            'selector': model_path / "feature_selector.joblib",
            'results': model_path / "results.json"
        }
        
        if all(f.exists() for f in required_files.values()):
            # 读取性能信息
            performance_info = {}
            try:
                with open(required_files['results'], 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    performance_info = {
                        'reported_accuracy': results.get('best_accuracy', 85.4),
                        'model_type': results.get('best_model', 'svr')
                    }
            except:
                performance_info = {'reported_accuracy': 85.4, 'model_type': 'svr'}
            
            self.models['SVR_85.4%'] = {
                'type': 'svr',
                'files': required_files,
                'performance_info': performance_info,
                'description': 'SVR模型 (报告准确率: 85.4%)'
            }
            print(f"  ✅ 发现SVR模型: 85.4%准确率")
        else:
            missing = [name for name, file in required_files.items() if not file.exists()]
            print(f"  ❌ SVR模型文件缺失: {missing}")
    
    def _check_production_model(self):
        """检查生产模型"""
        model_path = Path("production_ready_models")
        
        model_file = model_path / "ensemble_model.joblib"
        version_file = model_path / "model_version.json"
        
        if model_file.exists():
            # 读取版本信息
            performance_info = {}
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    version_data = json.load(f)
                    performance_info = version_data.get('performance', {})
            except:
                performance_info = {'accuracy_10kwh': '71.3%'}
            
            self.models['Production_71.3%'] = {
                'type': 'production_ensemble',
                'files': {'model': model_file},
                'performance_info': performance_info,
                'description': f"生产集成模型 (报告准确率: {performance_info.get('accuracy_10kwh', '71.3%')})"
            }
            print(f"  ✅ 发现生产模型: {performance_info.get('accuracy_10kwh', '71.3%')}准确率")
        else:
            print(f"  ❌ 生产模型文件不存在")
    
    def _check_realtime_model(self):
        """检查实时模型"""
        model_path = Path("realtime_vice_power_models")
        
        required_files = {
            'model': model_path / "ensemble_model.joblib",
            'feature_engineer': model_path / "feature_engineer.joblib",
            'scaler': model_path / "scaler.joblib"
        }
        
        if all(f.exists() for f in required_files.values()):
            self.models['Realtime_71.3%'] = {
                'type': 'realtime_ensemble',
                'files': required_files,
                'performance_info': {'accuracy_10kwh': '71.3%'},
                'description': '实时集成模型 (报告准确率: 71.3%)'
            }
            print(f"  ✅ 发现实时模型: 71.3%准确率")
        else:
            missing = [name for name, file in required_files.items() if not file.exists()]
            print(f"  ❌ 实时模型文件缺失: {missing}")

def main():
    """主函数"""
    print("="*60)
    print("🧪 副功率预测模型详细准确率测试")
    print("="*60)
    print("使用output_results数据，随机选择100组测试")
    print("确保在lj_env_1环境下运行")
    
    # 1. 环境验证
    if not EnvironmentValidator.validate_lj_env_1():
        print("\n❌ 环境验证失败，测试终止")
        return
    
    # 2. 数据准备
    data_manager = TestDataManager()
    if not data_manager.load_and_prepare_data(sample_size=100):
        print("\n❌ 数据准备失败，测试终止")
        return
    
    # 3. 模型发现
    tester = ModelTester(data_manager.test_data)
    if not tester.discover_models():
        print("\n❌ 未发现可用模型，测试终止")
        return
    
    print(f"\n✅ 环境和数据准备完成，发现 {len(tester.models)} 个模型")
    print("📊 准备开始详细测试...")
    
    # 保存初始状态
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    initial_report = {
        'test_timestamp': timestamp,
        'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
        'test_data_stats': data_manager.data_stats,
        'discovered_models': {name: info['description'] for name, info in tester.models.items()},
        'test_status': 'initialized'
    }
    
    report_file = f"model_accuracy_test_report_{timestamp}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(initial_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 初始报告已保存: {report_file}")
    print("🚀 测试环境准备完成，可以开始详细的模型准确率测试")

if __name__ == "__main__":
    main()
