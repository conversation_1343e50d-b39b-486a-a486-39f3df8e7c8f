#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副功率预测模型性能测试脚本
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# 添加模型路径
sys.path.append(str(Path(__file__).parent / 'kongwen_power_control' / 'beta_version' / 'v6' / 'production_deployment' / 'src'))

try:
    from predict import VicePowerPredictor
    PREDICTOR_AVAILABLE = True
    print("✅ 副功率预测器导入成功")
except ImportError as e:
    print(f"❌ 副功率预测器导入失败: {e}")
    PREDICTOR_AVAILABLE = False

def test_model_performance():
    """测试模型性能"""
    
    if not PREDICTOR_AVAILABLE:
        print("预测器不可用，无法进行性能测试")
        return
    
    # 读取测试数据
    df = pd.read_csv('output_results/A01_A40_cycles__analysis.csv')
    
    print("=" * 60)
    print("副功率预测模型性能测试")
    print("=" * 60)
    
    # 初始化预测器
    try:
        models_dir = str(Path(__file__).parent / 'kongwen_power_control' / 'beta_version' / 'v6' / 'production_deployment' / 'models')
        predictor = VicePowerPredictor(models_dir=models_dir)
        print(f"✅ 预测器初始化成功")
    except Exception as e:
        print(f"❌ 预测器初始化失败: {e}")
        return
    
    # 准备测试数据（取前100个样本进行快速测试）
    test_df = df.head(100).copy()
    
    predictions = []
    actual_values = []
    errors = []
    
    print(f"\n开始测试 {len(test_df)} 个样本...")
    
    for idx, row in test_df.iterrows():
        try:
            # 提取输入特征
            weight_difference = row['weight_difference']
            silicon_thermal_energy_kwh = row['silicon_thermal_energy_kwh']
            process_type = row['feed_type']
            actual_vice_power = row['vice_total_energy_kwh']
            
            # 进行预测
            result = predictor.predict_single(
                weight_difference=weight_difference,
                silicon_thermal_energy_kwh=silicon_thermal_energy_kwh,
                process_type=process_type
            )
            
            if result['predicted_vice_power_kwh'] is not None:
                predicted_vice_power = result['predicted_vice_power_kwh']
                error = abs(predicted_vice_power - actual_vice_power)
                
                predictions.append(predicted_vice_power)
                actual_values.append(actual_vice_power)
                errors.append(error)
                
                if idx < 10:  # 显示前10个预测结果
                    print(f"样本 {idx+1}: 实际={actual_vice_power:.2f}, 预测={predicted_vice_power:.2f}, 误差={error:.2f}")
            else:
                print(f"样本 {idx+1}: 预测失败 - {result.get('error_message', '未知错误')}")
                
        except Exception as e:
            print(f"样本 {idx+1}: 处理失败 - {e}")
    
    if len(predictions) > 0:
        # 计算性能指标
        predictions = np.array(predictions)
        actual_values = np.array(actual_values)
        errors = np.array(errors)
        
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean((predictions - actual_values) ** 2))
        mape = np.mean(np.abs((actual_values - predictions) / actual_values)) * 100
        
        # 计算不同误差范围内的准确率
        accuracy_5 = np.mean(errors <= 5) * 100
        accuracy_7 = np.mean(errors <= 7) * 100
        accuracy_10 = np.mean(errors <= 10) * 100
        accuracy_15 = np.mean(errors <= 15) * 100
        
        # R²计算
        ss_res = np.sum((actual_values - predictions) ** 2)
        ss_tot = np.sum((actual_values - np.mean(actual_values)) ** 2)
        r2 = 1 - (ss_res / ss_tot)
        
        print(f"\n性能指标 (基于 {len(predictions)} 个成功预测):")
        print(f"MAE (平均绝对误差): {mae:.2f} kWh")
        print(f"RMSE (均方根误差): {rmse:.2f} kWh")
        print(f"MAPE (平均绝对百分比误差): {mape:.2f}%")
        print(f"R² (决定系数): {r2:.4f}")
        print()
        print("准确率分析:")
        print(f"±5 kWh 准确率: {accuracy_5:.1f}%")
        print(f"±7 kWh 准确率: {accuracy_7:.1f}%")
        print(f"±10 kWh 准确率: {accuracy_10:.1f}% (目标: 80%)")
        print(f"±15 kWh 准确率: {accuracy_15:.1f}%")
        
        # 按工艺类型分析
        print("\n按工艺类型分析:")
        for process_type in ['首投', '复投']:
            mask = test_df['feed_type'] == process_type
            if mask.sum() > 0:
                process_errors = errors[mask.values[:len(errors)]]
                process_accuracy_10 = np.mean(process_errors <= 10) * 100
                process_mae = np.mean(process_errors)
                print(f"{process_type}: ±10kWh准确率={process_accuracy_10:.1f}%, MAE={process_mae:.2f}kWh")
        
        # 性能差距分析
        print(f"\n性能差距分析:")
        print(f"当前±10kWh准确率: {accuracy_10:.1f}%")
        print(f"目标±10kWh准确率: 80.0%")
        print(f"差距: {80.0 - accuracy_10:.1f}%")
        
        if accuracy_10 < 80:
            print(f"⚠️ 未达到目标，需要优化")
        else:
            print(f"✅ 已达到目标")
    
    else:
        print("❌ 没有成功的预测结果")

if __name__ == "__main__":
    test_model_performance()
