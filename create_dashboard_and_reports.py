#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建模型性能仪表板和报告
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DashboardReportGenerator:
    """仪表板和报告生成器"""
    
    def __init__(self):
        """初始化"""
        self.target_dir = Path("production_ready_models")
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72', 
            'accent': '#F18F01',
            'success': '#C73E1D',
            'warning': '#F4A261',
            'info': '#264653'
        }
        
    def check_environment(self):
        """检查环境"""
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        if conda_env != 'lj_env_1':
            print(f"❌ 错误：必须在lj_env_1环境中运行")
            return False
        else:
            print("✅ 环境检查通过：lj_env_1")
            return True
    
    def create_performance_dashboard(self):
        """创建模型性能仪表板"""
        print(f"📊 创建模型性能仪表板...")
        
        # 模拟性能数据（基于之前的结果）
        performance_data = {
            'overall': {
                'mae': 7.79,
                'r2': 0.9972,
                'acc_5': 42.3,
                'acc_7': 55.9,
                'acc_10': 71.3,
                'acc_15': 85.3,
                'target_gap': 8.7
            },
            'by_process': {
                '首投': {
                    'sample_count': 25,
                    'mae': 9.63,
                    'acc_10': 64.0
                },
                '复投': {
                    'sample_count': 261,
                    'mae': 7.61,
                    'acc_10': 72.0
                }
            }
        }
        
        # 创建仪表板
        fig = plt.figure(figsize=(20, 12))
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # 1. 主要性能指标卡片
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.text(0.5, 0.7, '±10kWh准确率', ha='center', va='center', fontsize=16, fontweight='bold')
        ax1.text(0.5, 0.4, f"{performance_data['overall']['acc_10']:.1f}%", 
                ha='center', va='center', fontsize=32, fontweight='bold', 
                color=self.colors['primary'])
        ax1.text(0.5, 0.1, f"目标: 80% (差距: {performance_data['overall']['target_gap']:.1f}%)", 
                ha='center', va='center', fontsize=12, color='red')
        ax1.set_xlim(0, 1)
        ax1.set_ylim(0, 1)
        ax1.axis('off')
        ax1.add_patch(plt.Rectangle((0.05, 0.05), 0.9, 0.9, fill=False, edgecolor=self.colors['primary'], linewidth=3))
        
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.text(0.5, 0.7, 'MAE', ha='center', va='center', fontsize=16, fontweight='bold')
        ax2.text(0.5, 0.4, f"{performance_data['overall']['mae']:.2f}", 
                ha='center', va='center', fontsize=32, fontweight='bold', 
                color=self.colors['success'])
        ax2.text(0.5, 0.1, 'kWh', ha='center', va='center', fontsize=12)
        ax2.set_xlim(0, 1)
        ax2.set_ylim(0, 1)
        ax2.axis('off')
        ax2.add_patch(plt.Rectangle((0.05, 0.05), 0.9, 0.9, fill=False, edgecolor=self.colors['success'], linewidth=3))
        
        ax3 = fig.add_subplot(gs[0, 2])
        ax3.text(0.5, 0.7, 'R² 决定系数', ha='center', va='center', fontsize=16, fontweight='bold')
        ax3.text(0.5, 0.4, f"{performance_data['overall']['r2']:.4f}", 
                ha='center', va='center', fontsize=32, fontweight='bold', 
                color=self.colors['info'])
        ax3.text(0.5, 0.1, '拟合优度', ha='center', va='center', fontsize=12)
        ax3.set_xlim(0, 1)
        ax3.set_ylim(0, 1)
        ax3.axis('off')
        ax3.add_patch(plt.Rectangle((0.05, 0.05), 0.9, 0.9, fill=False, edgecolor=self.colors['info'], linewidth=3))
        
        ax4 = fig.add_subplot(gs[0, 3])
        ax4.text(0.5, 0.7, '数据泄露状态', ha='center', va='center', fontsize=16, fontweight='bold')
        ax4.text(0.5, 0.4, '✅ 无泄露', 
                ha='center', va='center', fontsize=24, fontweight='bold', 
                color='green')
        ax4.text(0.5, 0.1, '可信可用', ha='center', va='center', fontsize=12)
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')
        ax4.add_patch(plt.Rectangle((0.05, 0.05), 0.9, 0.9, fill=False, edgecolor='green', linewidth=3))
        
        # 2. 准确率进度条
        ax5 = fig.add_subplot(gs[1, :2])
        accuracies = [performance_data['overall']['acc_5'], performance_data['overall']['acc_7'], 
                     performance_data['overall']['acc_10'], performance_data['overall']['acc_15']]
        labels = ['±5kWh', '±7kWh', '±10kWh', '±15kWh']
        colors = [self.colors['warning'], self.colors['accent'], self.colors['primary'], self.colors['success']]
        
        y_pos = np.arange(len(labels))
        bars = ax5.barh(y_pos, accuracies, color=colors, alpha=0.8)
        ax5.set_yticks(y_pos)
        ax5.set_yticklabels(labels)
        ax5.set_xlabel('准确率 (%)')
        ax5.set_title('不同误差范围准确率', fontsize=16, fontweight='bold')
        ax5.axvline(x=80, color='red', linestyle='--', linewidth=2, label='目标线(80%)')
        ax5.legend()
        
        # 添加数值标签
        for bar, value in zip(bars, accuracies):
            ax5.text(value + 1, bar.get_y() + bar.get_height()/2,
                    f'{value:.1f}%', va='center', fontweight='bold')
        
        # 3. 工艺类型性能对比
        ax6 = fig.add_subplot(gs[1, 2:])
        process_types = ['首投', '复投']
        process_acc = [performance_data['by_process']['首投']['acc_10'], 
                      performance_data['by_process']['复投']['acc_10']]
        process_mae = [performance_data['by_process']['首投']['mae'], 
                      performance_data['by_process']['复投']['mae']]
        
        x = np.arange(len(process_types))
        width = 0.35
        
        bars1 = ax6.bar(x - width/2, process_acc, width, label='±10kWh准确率(%)', 
                       color=self.colors['primary'], alpha=0.8)
        
        ax6_twin = ax6.twinx()
        bars2 = ax6_twin.bar(x + width/2, process_mae, width, label='MAE(kWh)', 
                            color=self.colors['accent'], alpha=0.8)
        
        ax6.set_xlabel('工艺类型')
        ax6.set_ylabel('准确率 (%)', color=self.colors['primary'])
        ax6_twin.set_ylabel('MAE (kWh)', color=self.colors['accent'])
        ax6.set_title('工艺类型性能对比', fontsize=16, fontweight='bold')
        ax6.set_xticks(x)
        ax6.set_xticklabels(process_types)
        
        # 添加数值标签
        for bar, value in zip(bars1, process_acc):
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        for bar, value in zip(bars2, process_mae):
            ax6_twin.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,
                         f'{value:.2f}', ha='center', va='bottom', fontweight='bold')
        
        # 4. 目标达成情况
        ax7 = fig.add_subplot(gs[2, :])
        
        # 创建目标达成可视化
        current_acc = performance_data['overall']['acc_10']
        target_acc = 80.0
        gap = target_acc - current_acc
        
        # 进度条
        progress = current_acc / target_acc
        ax7.barh([0], [current_acc], height=0.3, color=self.colors['primary'], alpha=0.8, label='当前准确率')
        ax7.barh([0], [gap], left=[current_acc], height=0.3, color='lightgray', alpha=0.5, label='差距')
        
        ax7.set_xlim(0, 100)
        ax7.set_ylim(-0.5, 0.5)
        ax7.set_xlabel('准确率 (%)')
        ax7.set_title('±10kWh准确率目标达成情况', fontsize=16, fontweight='bold')
        ax7.axvline(x=target_acc, color='red', linestyle='--', linewidth=3, label='目标线(80%)')
        
        # 添加文本标注
        ax7.text(current_acc/2, 0, f'{current_acc:.1f}%\n已达成', 
                ha='center', va='center', fontweight='bold', fontsize=14)
        ax7.text(current_acc + gap/2, 0, f'{gap:.1f}%\n待提升', 
                ha='center', va='center', fontweight='bold', fontsize=12, color='red')
        
        ax7.legend(loc='upper right')
        ax7.set_yticks([])
        
        plt.suptitle('实时副功率预测模型性能仪表板', fontsize=24, fontweight='bold', y=0.98)
        
        plt.savefig(self.target_dir / 'model_performance_dashboard.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 保存 model_performance_dashboard.png")
        
    def create_html_report(self):
        """创建HTML可视化报告"""
        print(f"📄 创建HTML可视化报告...")
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时副功率预测模型可视化报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2E86AB;
            text-align: center;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #A23B72;
            border-left: 5px solid #A23B72;
            padding-left: 15px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .metric-card {{
            background: linear-gradient(135deg, #2E86AB, #A23B72);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }}
        .chart-container {{
            text-align: center;
            margin: 30px 0;
        }}
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }}
        .status-badge {{
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px;
        }}
        .status-success {{
            background-color: #28a745;
            color: white;
        }}
        .status-warning {{
            background-color: #ffc107;
            color: black;
        }}
        .status-info {{
            background-color: #17a2b8;
            color: white;
        }}
        .improvement-section {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }}
        th {{
            background-color: #2E86AB;
            color: white;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 实时副功率预测模型可视化报告</h1>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <div>±10kWh准确率</div>
                <div class="metric-value">71.3%</div>
                <div>目标: 80% (差距: 8.7%)</div>
            </div>
            <div class="metric-card">
                <div>平均绝对误差</div>
                <div class="metric-value">7.79</div>
                <div>kWh</div>
            </div>
            <div class="metric-card">
                <div>决定系数 R²</div>
                <div class="metric-value">0.9972</div>
                <div>拟合优度优秀</div>
            </div>
            <div class="metric-card">
                <div>数据泄露状态</div>
                <div class="metric-value">✅</div>
                <div>无泄露，可信可用</div>
            </div>
        </div>
        
        <h2>📊 模型状态概览</h2>
        <p>
            <span class="status-badge status-success">环境: lj_env_1</span>
            <span class="status-badge status-success">数据泄露: 已修正</span>
            <span class="status-badge status-info">特征数量: 26个</span>
            <span class="status-badge status-warning">准确率: 未达标</span>
            <span class="status-badge status-success">部署就绪: 是</span>
        </p>
        
        <h2>📈 性能对比分析</h2>
        <div class="chart-container">
            <img src="performance_comparison_charts.png" alt="性能对比图表">
        </div>
        
        <h2>🎯 预测效果分析</h2>
        <div class="chart-container">
            <img src="prediction_analysis_charts.png" alt="预测效果分析图">
        </div>
        
        <h2>🔍 特征重要性分析</h2>
        <div class="chart-container">
            <img src="feature_importance_charts.png" alt="特征重要性图表">
        </div>
        
        <h2>📊 性能仪表板</h2>
        <div class="chart-container">
            <img src="model_performance_dashboard.png" alt="模型性能仪表板">
        </div>
        
        <h2>📋 详细性能指标</h2>
        <table>
            <tr>
                <th>指标</th>
                <th>整体性能</th>
                <th>首投工艺</th>
                <th>复投工艺</th>
                <th>目标值</th>
            </tr>
            <tr>
                <td>±5kWh准确率</td>
                <td>42.3%</td>
                <td>24.0%</td>
                <td>44.1%</td>
                <td>-</td>
            </tr>
            <tr>
                <td>±10kWh准确率</td>
                <td><strong>71.3%</strong></td>
                <td>64.0%</td>
                <td>72.0%</td>
                <td><strong>80%</strong></td>
            </tr>
            <tr>
                <td>±15kWh准确率</td>
                <td>85.3%</td>
                <td>80.0%</td>
                <td>85.8%</td>
                <td>-</td>
            </tr>
            <tr>
                <td>MAE (kWh)</td>
                <td>7.79</td>
                <td>9.63</td>
                <td>7.61</td>
                <td>-</td>
            </tr>
            <tr>
                <td>R²</td>
                <td>0.9972</td>
                <td>0.9954</td>
                <td>0.9973</td>
                <td>-</td>
            </tr>
        </table>
        
        <div class="improvement-section">
            <h2>🚀 改进建议</h2>
            <h3>短期改进 (1-3个月)</h3>
            <ul>
                <li><strong>数据收集优化</strong>: 增加首投工艺样本数据，目标从139个增加到300+个</li>
                <li><strong>特征工程优化</strong>: 引入更多设备实时监测参数</li>
                <li><strong>模型调优</strong>: 优化集成学习权重和超参数</li>
            </ul>
            
            <h3>中期改进 (3-6个月)</h3>
            <ul>
                <li><strong>算法升级</strong>: 尝试XGBoost、LightGBM等先进算法</li>
                <li><strong>工艺特定模型</strong>: 为首投和复投分别训练专门模型</li>
                <li><strong>在线学习</strong>: 开发模型在线更新能力</li>
            </ul>
            
            <h3>长期改进 (6-12个月)</h3>
            <ul>
                <li><strong>深度学习</strong>: 探索神经网络和时间序列深度学习</li>
                <li><strong>多模态融合</strong>: 集成更多传感器数据</li>
                <li><strong>智能决策</strong>: 开发智能决策支持系统</li>
            </ul>
        </div>
        
        <h2>✅ 部署就绪确认</h2>
        <p>✅ <strong>技术可行性</strong>: 模型文件完整，可直接部署</p>
        <p>✅ <strong>业务可行性</strong>: 71.3%准确率在工业应用中可接受</p>
        <p>✅ <strong>数据安全性</strong>: 无数据泄露，预测结果可信</p>
        <p>⚠️ <strong>性能提升空间</strong>: 距离80%目标还有8.7%差距</p>
        
        <div class="footer">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>环境: lj_env_1 | 模型版本: v1.0 | 数据泄露: 已修正</p>
        </div>
    </div>
</body>
</html>
"""
        
        html_file = self.target_dir / 'model_visualization_report.html'
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 保存 model_visualization_report.html")

def main():
    """主函数"""
    print("="*60)
    print("创建模型性能仪表板和报告")
    print("="*60)
    
    generator = DashboardReportGenerator()
    
    # 环境检查
    if not generator.check_environment():
        return
    
    # 创建仪表板和报告
    generator.create_performance_dashboard()
    generator.create_html_report()
    
    print(f"\n🎉 仪表板和报告创建完成！")
    print(f"📁 文件保存在: {generator.target_dir.absolute()}")
    
    return generator

if __name__ == "__main__":
    generator = main()
