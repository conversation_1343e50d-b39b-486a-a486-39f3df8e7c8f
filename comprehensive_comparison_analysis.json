{"analysis_timestamp": "2025-07-24T08:43:18.855619", "model_consistency": {"training_model": {"type": "ensemble", "algorithms": ["RandomForest", "GradientBoosting"], "features": 26}, "current_model": {"type": "v7_realistic", "weight_coef": 5.205976, "energy_coef": -5.156788, "intercept": 50.237235}, "differences": ["模型类型不同: ensemble vs v7_realistic", "特征数量不同: 训练时26个 vs 当前2个"]}, "dataset_comparison": {"training_data": {"total_samples": 1430, "test_samples": 286, "file_source": "A01_A40_cycles__analysis.csv", "split_method": "time_series_80_20", "devices": ["analoga08", "analoga09", "analoga10"], "device_count": 3, "time_range": "2025-05-02 16:34:19 to 2025-07-05 12:03:22", "weight_stats": {"mean": 318.8473076923077, "std": 209.28411568652544, "min": 28.350000000000023, "max": 765.27}, "energy_stats": {"mean": 264.52911138141025, "std": 173.6997408434477, "min": 23.53750875000001, "max": 636.6457556666667}, "vice_power_stats": {"mean": 369.12310436091684, "std": 195.6359275692669, "min": 39.22222222222223, "max": 821.4888888888888}, "feed_type_dist": {"复投": 261, "首投": 25}}, "current_data": {"total_samples": 2119, "clean_samples": 2117, "test_samples": 300, "file_source": "all_folders_summary.csv", "split_method": "random_sampling", "devices": ["analoga01", "analoga01_12", "analoga02", "analoga02_12", "analoga03", "analoga03_12", "analoga04", "analoga04_12", "analoga05", "analoga05_12", "analoga06", "analoga06_12", "analoga07", "analoga07_12", "analoga08", "analoga08_12", "analoga09", "analoga09_12", "analoga10", "analoga10_12", "analoga11", "analoga11_12", "analoga12", "analoga12_12", "analoga13", "analoga13_12", "analoga14", "analoga14_12", "analoga15", "analoga15_12", "analoga16", "analoga16_12", "analoga17", "analoga17_12", "analoga18", "analoga18_12", "analoga19", "analoga19_12", "analoga20", "analoga20_12", "analoga21", "analoga21_1", "analoga22", "analoga22_1", "analoga23", "analoga23_1", "analoga24", "analoga24_1", "analoga25", "analoga25_1", "analoga26", "analoga26_1", "analoga27", "analoga27_1", "analoga28", "analoga28_1", "analoga29", "analoga29_1", "analoga30", "analoga30_1", "analoga31", "analoga31_1", "analoga32", "analoga32_1", "analoga33", "analoga33_1", "analoga34", "analoga34_1", "analoga35", "analoga35_1", "analoga36", "analoga36_1", "analoga37", "analoga37_1", "analoga38", "analoga38_1", "analoga39", "analoga39_1", "analoga40", "analoga40_1", "analoga41", "analoga42", "analoga43", "analoga44", "analoga45", "analoga46", "analoga47", "analoga48", "analoga49", "analoga50", "analoga51", "analoga52", "analoga53", "analoga54", "analoga55", "analoga56", "analoga57", "analoga58", "analoga59", "analoga60", "analoga61", "analoga62", "analoga63", "analoga64", "analoga65", "analoga66", "analoga67", "analoga68", "analoga69", "analoga70", "analoga71", "analoga72", "analoga73", "analoga74", "analoga75", "analoga76", "analoga77", "analoga79", "analoga80"], "device_count": 119, "time_range": "2025-01-01 00:05:29 to 2025-05-14 05:49:01", "weight_stats": {"mean": 460.0853000000001, "std": 170.47445110585576, "min": 20.529999999999973, "max": 757.49}, "energy_stats": {"mean": 381.9174391276852, "std": 141.55972133624857, "min": 17.426434277777755, "max": 630.3427712222223}, "vice_power_stats": {"mean": 467.82610875000006, "std": 162.90373539483966, "min": 34.89722222222223, "max": 750.4444444444446}, "feed_type_dist": {"复投": 239, "首投": 61}}, "differences": ["设备数量不同: 3 vs 119", "重量分布差异显著: 141.2 kg", "能量分布差异显著: 117.4 kWh"]}, "methodology_verification": {"training_method": {"split_type": "time_series", "accuracy_formula": "(errors <= 10).mean() * 100", "has_eval_function": true, "reported_acc_10": 71.32867132867133, "reported_mae": 7.789705970620979}, "current_method": {"split_type": "random_sampling", "accuracy_formula": "(error <= 10)", "error_formula": "abs(predicted - actual)", "measured_acc_10": 25.3, "measured_mae": 26.58}, "differences": ["数据分割方法不同: time_series vs random_sampling", "准确率计算公式不同: (errors <= 10).mean() * 100 vs (error <= 10)"]}, "difference_analysis": {"all_differences": ["模型类型不同: ensemble vs v7_realistic", "特征数量不同: 训练时26个 vs 当前2个", "设备数量不同: 3 vs 119", "重量分布差异显著: 141.2 kg", "能量分布差异显著: 117.4 kWh", "数据分割方法不同: time_series vs random_sampling", "准确率计算公式不同: (errors <= 10).mean() * 100 vs (error <= 10)"], "impact_analysis": {"critical": ["模型类型不同: ensemble vs v7_realistic", "特征数量不同: 训练时26个 vs 当前2个"], "major": ["设备数量不同: 3 vs 119", "重量分布差异显著: 141.2 kg", "能量分布差异显著: 117.4 kWh"], "minor": ["数据分割方法不同: time_series vs random_sampling", "准确率计算公式不同: (errors <= 10).mean() * 100 vs (error <= 10)"]}, "reasons": ["模型算法完全不同 (集成模型 vs 线性回归)", "数据覆盖范围不同 (设备数量差异)", "数据分布特征不同"], "reliability": {"training_result": "unreliable", "current_result": "reliable"}}}