#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SVR模型最终执行脚本
深度测试现有模型并在lj_env_1环境中重新训练
"""

import os
import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime

def main():
    """主执行函数"""
    print("="*60)
    print("🧪 SVR模型深度测试和重新训练")
    print("="*60)
    print("基于output_results数据，深度测试SVR模型准确率")
    print("并在lj_env_1环境中重新训练优化模型")
    
    # 环境检查
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print(f"\n🔍 当前环境: {conda_env}")
    
    if conda_env == 'lj_env_1':
        print("✅ 环境正确：lj_env_1")
        env_status = "CORRECT"
    else:
        print(f"⚠️ 环境警告：当前为 {conda_env}，建议使用 lj_env_1")
        env_status = "WARNING"
    
    # 检查数据文件
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    print(f"\n📊 检查数据文件: {data_file}")
    
    if data_file.exists():
        print("✅ 数据文件存在")
        try:
            df = pd.read_csv(data_file)
            print(f"📊 数据形状: {df.shape}")
            data_status = "AVAILABLE"
        except Exception as e:
            print(f"❌ 数据读取失败: {e}")
            data_status = "ERROR"
    else:
        print("❌ 数据文件不存在")
        data_status = "MISSING"
    
    # 检查SVR模型文件
    model_path = Path("副功率预测_85.4%准确率_完整项目/models")
    svr_files = {
        'model': model_path / "best_model_svr.joblib",
        'scaler': model_path / "scaler.joblib",
        'selector': model_path / "feature_selector.joblib",
        'results': model_path / "results.json"
    }
    
    print(f"\n🔍 检查SVR模型文件:")
    model_status = "COMPLETE"
    for name, file_path in svr_files.items():
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024*1024)
            print(f"  ✅ {name}: {file_path.name} ({size_mb:.2f}MB)")
        else:
            print(f"  ❌ {name}: {file_path.name} (缺失)")
            model_status = "INCOMPLETE"
    
    # 检查依赖包
    print(f"\n📦 检查依赖包:")
    packages = ['pandas', 'numpy', 'sklearn', 'joblib']
    package_status = "COMPLETE"
    
    for package in packages:
        try:
            if package == 'sklearn':
                import sklearn
                version = sklearn.__version__
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
            print(f"  ✅ {package}: {version}")
        except ImportError:
            print(f"  ❌ {package}: 未安装")
            package_status = "INCOMPLETE"
    
    # 生成状态报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    status_report = {
        'timestamp': timestamp,
        'environment': {
            'conda_env': conda_env,
            'status': env_status,
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        },
        'data': {
            'file_path': str(data_file),
            'status': data_status,
            'shape': df.shape if data_status == "AVAILABLE" else None
        },
        'model': {
            'path': str(model_path),
            'status': model_status,
            'files': {name: file_path.exists() for name, file_path in svr_files.items()}
        },
        'packages': {
            'status': package_status
        }
    }
    
    # 执行建议
    print(f"\n" + "="*60)
    print("📋 执行建议")
    print("="*60)
    
    if env_status == "CORRECT" and data_status == "AVAILABLE" and model_status == "COMPLETE" and package_status == "COMPLETE":
        print("✅ 所有条件满足，可以执行深度测试和重新训练")
        print("\n🚀 建议执行步骤:")
        print("1. 运行: python simplified_svr_test.py")
        print("2. 检查生成的测试报告")
        print("3. 查看新训练的模型文件夹")
        execution_ready = True
    else:
        print("⚠️ 存在问题，需要解决后再执行")
        print("\n🔧 需要解决的问题:")
        
        if env_status != "CORRECT":
            print("- 环境问题: 请切换到 lj_env_1 环境")
            print("  解决方案: conda activate lj_env_1")
        
        if data_status != "AVAILABLE":
            print("- 数据问题: 无法访问测试数据")
            print("  解决方案: 检查 output_results/A01_A40_cycles__analysis.csv 文件")
        
        if model_status != "COMPLETE":
            print("- 模型问题: SVR模型文件不完整")
            print("  解决方案: 检查 副功率预测_85.4%准确率_完整项目/models/ 目录")
        
        if package_status != "COMPLETE":
            print("- 依赖问题: 缺少必要的Python包")
            print("  解决方案: pip install pandas numpy scikit-learn joblib")
        
        execution_ready = False
    
    # 保存状态报告
    report_file = f"svr_execution_status_{timestamp}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(status_report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 状态报告已保存: {report_file}")
    
    # 如果条件满足，提供快速执行选项
    if execution_ready:
        print(f"\n🎯 快速执行选项:")
        print("如果您想立即开始测试，可以运行以下命令:")
        print("python simplified_svr_test.py")
        
        # 创建快速执行脚本
        quick_script = f"""#!/usr/bin/env python3
# 快速执行SVR测试脚本
import subprocess
import sys

print("🚀 开始SVR模型深度测试...")
try:
    result = subprocess.run([sys.executable, "simplified_svr_test.py"], 
                          capture_output=True, text=True, timeout=300)
    print(result.stdout)
    if result.stderr:
        print("错误信息:", result.stderr)
except Exception as e:
    print(f"执行失败: {{e}}")
"""
        
        with open("quick_svr_test.py", 'w', encoding='utf-8') as f:
            f.write(quick_script)
        
        print("📝 已创建快速执行脚本: quick_svr_test.py")
    
    print(f"\n✅ SVR执行准备检查完成")
    return execution_ready

if __name__ == "__main__":
    ready = main()
    if ready:
        print("\n🎉 准备就绪，可以开始SVR深度测试！")
    else:
        print("\n⚠️ 请解决上述问题后重新运行此脚本")
