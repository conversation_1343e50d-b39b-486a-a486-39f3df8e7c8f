#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的实时副功率预测模型 - 基于物理约束和领域知识
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class EnhancedRealtimeFeatureEngineer:
    """增强的实时特征工程器"""
    
    def __init__(self):
        """初始化"""
        self.scaler = StandardScaler()
        
    def create_physics_constrained_features(self, df):
        """创建基于物理约束的特征"""
        features_df = df.copy()
        
        print("创建基于物理约束的增强特征...")
        
        # 1. 基础物理特征
        features_df['weight_diff'] = features_df['weight_difference']
        features_df['silicon_energy'] = features_df['silicon_thermal_energy_kwh']
        features_df['is_first_cast'] = (features_df['feed_type'] == '首投').astype(int)
        
        # 2. 物理学衍生特征
        # 能量密度 (kWh/kg)
        features_df['energy_density'] = features_df['silicon_energy'] / (features_df['weight_diff'] + 1e-6)
        
        # 比能耗 (基于硅的物理特性)
        # 硅熔化潜热约1800 kJ/kg = 0.5 kWh/kg
        features_df['melting_energy_ratio'] = features_df['silicon_energy'] / (features_df['weight_diff'] * 0.5 + 1e-6)
        
        # 热效率预估 (基于经验公式)
        features_df['thermal_efficiency_est'] = np.clip(1.2 + 0.3 * features_df['is_first_cast'], 1.0, 2.5)
        
        # 3. 工艺特定特征
        # 首投工艺特殊处理
        features_df['first_cast_weight_factor'] = features_df['is_first_cast'] * features_df['weight_diff']
        features_df['first_cast_energy_factor'] = features_df['is_first_cast'] * features_df['silicon_energy']
        
        # 复投工艺特殊处理
        features_df['recast_weight_factor'] = (1 - features_df['is_first_cast']) * features_df['weight_diff']
        features_df['recast_energy_factor'] = (1 - features_df['is_first_cast']) * features_df['silicon_energy']
        
        # 4. 基于数据分析的经验特征
        # 重量范围特征（基于数据分析结果）
        features_df['weight_category'] = pd.cut(features_df['weight_diff'], 
                                               bins=[0, 100, 200, 350, 500, 1000], 
                                               labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 能量范围特征
        features_df['energy_category'] = pd.cut(features_df['silicon_energy'], 
                                               bins=[0, 150, 300, 450, 600, 1000], 
                                               labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 5. 交互特征
        # 重量-能量平衡
        features_df['weight_energy_balance'] = abs(features_df['weight_diff'] - features_df['silicon_energy'])
        
        # 工艺-重量-能量三元交互
        features_df['process_weight_energy'] = features_df['is_first_cast'] * features_df['weight_diff'] * features_df['silicon_energy'] / 10000
        
        # 6. 非线性变换
        # 对数变换（处理偏态分布）
        features_df['log_weight'] = np.log(features_df['weight_diff'] + 1)
        features_df['log_energy'] = np.log(features_df['silicon_energy'] + 1)
        
        # 平方根变换
        features_df['sqrt_weight'] = np.sqrt(features_df['weight_diff'])
        features_df['sqrt_energy'] = np.sqrt(features_df['silicon_energy'])
        
        # 7. 设备和时间特征
        if 'folder_name' in features_df.columns:
            device_mapping = {name: idx for idx, name in enumerate(features_df['folder_name'].unique())}
            features_df['device_id'] = features_df['folder_name'].map(device_mapping)
        
        if 'start_time' in features_df.columns:
            features_df['start_time'] = pd.to_datetime(features_df['start_time'])
            features_df['start_hour'] = features_df['start_time'].dt.hour
            features_df['start_day_of_week'] = features_df['start_time'].dt.dayofweek
            
            # 工作时间特征
            features_df['is_work_hours'] = ((features_df['start_hour'] >= 8) & (features_df['start_hour'] <= 18)).astype(int)
            features_df['is_weekend'] = (features_df['start_day_of_week'] >= 5).astype(int)
        
        # 8. 基于领域知识的特征
        # 副功率预估公式（基于经验）
        features_df['vice_power_estimate_v1'] = features_df['weight_diff'] * 0.8 + features_df['silicon_energy'] * 0.6
        features_df['vice_power_estimate_v2'] = features_df['weight_diff'] * features_df['thermal_efficiency_est'] * 0.7
        
        # 工艺难度评估
        features_df['process_difficulty'] = (
            features_df['is_first_cast'] * 1.5 +  # 首投更难
            (features_df['weight_diff'] > 400) * 1.2 +  # 大重量更难
            (features_df['energy_density'] > 2.0) * 1.1   # 高能量密度更难
        )
        
        return features_df
    
    def select_optimal_features(self, df, target_col='vice_total_energy_kwh'):
        """选择最优特征组合"""
        
        # 创建增强特征
        features_df = self.create_physics_constrained_features(df)
        
        # 候选特征列表
        candidate_features = [
            'weight_diff', 'silicon_energy', 'is_first_cast',
            'energy_density', 'melting_energy_ratio', 'thermal_efficiency_est',
            'first_cast_weight_factor', 'first_cast_energy_factor',
            'recast_weight_factor', 'recast_energy_factor',
            'weight_category', 'energy_category',
            'weight_energy_balance', 'process_weight_energy',
            'log_weight', 'log_energy', 'sqrt_weight', 'sqrt_energy',
            'vice_power_estimate_v1', 'vice_power_estimate_v2',
            'process_difficulty'
        ]
        
        # 添加设备和时间特征（如果可用）
        if 'device_id' in features_df.columns:
            candidate_features.extend(['device_id'])
        if 'start_hour' in features_df.columns:
            candidate_features.extend(['start_hour', 'start_day_of_week', 'is_work_hours', 'is_weekend'])
        
        # 过滤存在的特征
        available_features = [f for f in candidate_features if f in features_df.columns]
        
        print(f"可用特征数量: {len(available_features)}")
        
        # 准备特征矩阵
        X = features_df[available_features].fillna(0)
        y = features_df[target_col]
        
        return X, y, available_features

class EnhancedRealtimeModel:
    """增强的实时副功率预测模型"""
    
    def __init__(self):
        """初始化"""
        self.models = {}
        self.weights = {}
        self.scaler = StandardScaler()
        self.feature_names = None
        self.is_trained = False
        
    def create_ensemble_models(self):
        """创建集成模型"""
        models = {
            'rf_main': RandomForestRegressor(
                n_estimators=300,
                max_depth=12,
                min_samples_split=3,
                min_samples_leaf=2,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1
            ),
            'gbr_main': GradientBoostingRegressor(
                n_estimators=200,
                learning_rate=0.08,
                max_depth=8,
                min_samples_split=3,
                min_samples_leaf=2,
                subsample=0.8,
                random_state=42
            ),
            'ridge_main': Ridge(
                alpha=0.5,
                random_state=42
            )
        }
        return models
    
    def train_with_process_specific_optimization(self, X, y):
        """使用工艺特定优化训练模型"""
        print("\n训练增强的实时副功率预测模型...")
        
        # 数据预处理
        X_scaled = self.scaler.fit_transform(X)
        self.feature_names = X.columns.tolist()
        
        # 创建模型
        models = self.create_ensemble_models()
        
        # 使用时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=5)
        model_scores = {name: [] for name in models.keys()}
        
        print(f"\n时间序列交叉验证结果:")
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X_scaled)):
            X_train_fold, X_val_fold = X_scaled[train_idx], X_scaled[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
            
            fold_predictions = {}
            
            for name, model in models.items():
                # 训练模型
                model.fit(X_train_fold, y_train_fold)
                
                # 预测
                y_pred = model.predict(X_val_fold)
                
                # 评估
                mae = mean_absolute_error(y_val_fold, y_pred)
                r2 = r2_score(y_val_fold, y_pred)
                acc_10 = (np.abs(y_pred - y_val_fold) <= 10).mean() * 100
                
                model_scores[name].append({
                    'mae': mae,
                    'r2': r2,
                    'acc_10': acc_10
                })
                
                fold_predictions[name] = y_pred
            
            # 集成预测
            ensemble_pred = np.mean([fold_predictions[name] for name in models.keys()], axis=0)
            ensemble_mae = mean_absolute_error(y_val_fold, ensemble_pred)
            ensemble_acc_10 = (np.abs(ensemble_pred - y_val_fold) <= 10).mean() * 100
            
            print(f"  Fold {fold+1}: 集成MAE={ensemble_mae:.2f}, ±10kWh准确率={ensemble_acc_10:.1f}%")
        
        # 计算平均性能和权重
        avg_scores = {}
        for name in models.keys():
            scores = model_scores[name]
            avg_mae = np.mean([s['mae'] for s in scores])
            avg_r2 = np.mean([s['r2'] for s in scores])
            avg_acc_10 = np.mean([s['acc_10'] for s in scores])
            
            avg_scores[name] = {
                'mae': avg_mae,
                'r2': avg_r2,
                'acc_10': avg_acc_10
            }
            
            print(f"  {name}: 平均MAE={avg_mae:.2f}, R²={avg_r2:.4f}, ±10kWh准确率={avg_acc_10:.1f}%")
        
        # 基于性能计算权重
        total_score = sum([s['r2'] for s in avg_scores.values()])
        self.weights = {name: s['r2'] / total_score for name, s in avg_scores.items()}
        
        print(f"\n模型权重: {self.weights}")
        
        # 在全部数据上训练最终模型
        for name, model in models.items():
            model.fit(X_scaled, y)
            self.models[name] = model
        
        self.is_trained = True
        
        # 特征重要性分析（使用随机森林）
        rf_importance = pd.DataFrame({
            'feature': self.feature_names,
            'importance': self.models['rf_main'].feature_importances_
        }).sort_values('importance', ascending=False)
        
        print(f"\n特征重要性排序:")
        for idx, row in rf_importance.head(15).iterrows():
            print(f"  {row['feature']}: {row['importance']:.4f}")
        
        return avg_scores, rf_importance
    
    def predict(self, X):
        """集成预测"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        X_scaled = self.scaler.transform(X)
        
        # 获取各模型预测
        predictions = {}
        for name, model in self.models.items():
            predictions[name] = model.predict(X_scaled)
        
        # 加权集成
        ensemble_pred = np.zeros(len(X))
        for name, pred in predictions.items():
            ensemble_pred += pred * self.weights[name]
        
        return ensemble_pred
    
    def evaluate_final_performance(self, X, y):
        """最终性能评估"""
        print(f"\n最终性能评估（时间序列分割）:")
        
        # 使用最后20%作为测试集
        split_idx = int(len(X) * 0.8)
        X_test = X.iloc[split_idx:]
        y_test = y.iloc[split_idx:]
        
        # 预测
        y_pred = self.predict(X_test)
        
        # 计算指标
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        errors = np.abs(y_pred - y_test)
        acc_5 = (errors <= 5).mean() * 100
        acc_7 = (errors <= 7).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        print(f"  测试集大小: {len(y_test)} 样本")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  R²: {r2:.4f}")
        print(f"  ±5 kWh 准确率: {acc_5:.1f}%")
        print(f"  ±7 kWh 准确率: {acc_7:.1f}%")
        print(f"  ±10 kWh 准确率: {acc_10:.1f}% (目标: 80%)")
        print(f"  ±15 kWh 准确率: {acc_15:.1f}%")
        
        # 按工艺类型分析
        if 'is_first_cast' in X_test.columns:
            print(f"\n按工艺类型分析:")
            for process_type, process_name in [(1, '首投'), (0, '复投')]:
                mask = X_test['is_first_cast'] == process_type
                if mask.sum() > 0:
                    process_errors = errors[mask]
                    process_acc_10 = (process_errors <= 10).mean() * 100
                    process_mae = np.mean(process_errors)
                    print(f"  {process_name}: ±10kWh准确率={process_acc_10:.1f}%, MAE={process_mae:.2f}kWh")
        
        return {
            'mae': mae,
            'r2': r2,
            'acc_5': acc_5,
            'acc_7': acc_7,
            'acc_10': acc_10,
            'acc_15': acc_15
        }

def main():
    """主函数"""
    print("开始训练增强的实时副功率预测模型...")
    
    # 1. 加载数据
    df = pd.read_csv('output_results/A01_A40_cycles__analysis.csv')
    
    # 2. 增强特征工程
    engineer = EnhancedRealtimeFeatureEngineer()
    X, y, feature_names = engineer.select_optimal_features(df)
    
    print(f"\n数据概况:")
    print(f"  样本数: {len(X)}")
    print(f"  特征数: {len(feature_names)}")
    print(f"  首投样本: {X['is_first_cast'].sum()}")
    print(f"  复投样本: {len(X) - X['is_first_cast'].sum()}")
    
    # 3. 训练增强模型
    model = EnhancedRealtimeModel()
    avg_scores, feature_importance = model.train_with_process_specific_optimization(X, y)
    
    # 4. 最终性能评估
    final_results = model.evaluate_final_performance(X, y)
    
    print(f"\n" + "=" * 60)
    print("增强实时模型训练完成")
    print("=" * 60)
    print(f"实时约束下±10kWh准确率: {final_results['acc_10']:.1f}% (目标: 80%)")
    
    if final_results['acc_10'] >= 80:
        print("✅ 已达到目标准确率")
    else:
        print(f"⚠️ 未达到目标，差距: {80 - final_results['acc_10']:.1f}%")
    
    # 5. 性能改进建议
    print(f"\n性能改进建议:")
    if final_results['acc_10'] < 80:
        print("  1. 考虑收集更多首投工艺数据")
        print("  2. 引入更多设备实时监测参数")
        print("  3. 优化物理模型参数")
        print("  4. 考虑使用深度学习方法")
    
    return engineer, model, final_results

if __name__ == "__main__":
    engineer, model, results = main()
