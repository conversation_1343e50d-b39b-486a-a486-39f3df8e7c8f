# 模型使用指南

## 🎯 模型概述

本指南详细说明如何使用85.4%准确率的副功率预测模型。

## 📋 前置要求

### 环境依赖
```bash
pip install scikit-learn pandas numpy joblib
```

### 数据格式要求
输入数据必须包含以下列：
- start_weight: 起始重量
- end_weight: 结束重量  
- weight_difference: 重量差异
- silicon_thermal_energy_kwh: 硅热能耗
- main_total_energy_kwh: 主功率总能耗
- total_energy_kwh: 总能耗
- duration_hours: 持续时间
- 等其他特征...

## 🔧 使用步骤

### 1. 加载模型
```python
import joblib
import pandas as pd
import numpy as np

# 加载预训练模型和预处理器
model = joblib.load('models/best_model_svr.joblib')
scaler = joblib.load('models/scaler.joblib') 
selector = joblib.load('models/feature_selector.joblib')
```

### 2. 数据预处理
```python
# 加载新数据
new_data = pd.read_csv('your_new_data.csv')

# 特征工程（需要与训练时保持一致）
# 这里需要实现与训练时相同的特征工程步骤
# 包括物理特征、多项式特征、交互特征等

# 特征选择和标准化
X_scaled = scaler.transform(new_data)
X_selected = selector.transform(X_scaled)
```

### 3. 预测
```python
# 进行预测
predictions = model.predict(X_selected)

# 预测结果就是副功率能耗 (kWh)
print(f"预测的副功率能耗: {predictions}")
```

## ⚠️ 注意事项

1. **特征工程一致性**: 必须使用与训练时完全相同的特征工程步骤
2. **数据质量**: 确保输入数据质量与训练数据相当
3. **数值范围**: 输入数据应在训练数据的合理范围内
4. **缺失值处理**: 确保没有缺失值或按训练时方法处理

## 📊 预期性能

在类似的生产数据上，模型预期达到：
- ±10kWh准确率: 85.4%
- 平均绝对误差: ~8kWh
- 预测范围: 35-2500 kWh
