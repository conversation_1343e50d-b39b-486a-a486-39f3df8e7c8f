import os
import pandas as pd
import pymysql
from datetime import datetime, timedelta
import json

pd.set_option('display.max_columns', None)  # 显示所有列
class FurnaceControlData:
    def __init__(self, db_config, furnace_id, current_date=None, output_folder="output"):
        self.db_config = db_config
        self.furnace_id = furnace_id
        self.current_date = current_date or datetime.now()
        self.output_folder = output_folder

    def _get_start_and_end_dates(self):
        """
        获取查询的开始和结束日期
        """
        if isinstance(self.current_date, str):
            try:
                end_date = datetime.strptime(self.current_date, "%Y-%m-%d")
            except ValueError:
                raise ValueError("current_date 格式错误，应为 'YYYY-MM-DD'")
        else:
            end_date = self.current_date

        start_date = end_date - timedelta(days=50)
        print('开始日期和结束日期',start_date,end_date)
        return start_date, end_date

    def _connect_to_database(self):
        """
        连接数据库并返回连接对象
        使用 with 管理连接
        """
        try:
            connection = pymysql.connect(
                host=self.db_config["host"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                database=self.db_config["database"],
                port=self.db_config["port"]
            )
            return connection
        except pymysql.MySQLError as e:
            print(f"数据库连接失败: {e}")
            raise

    def export_and_sort_control_data(self):
        """
        导出某个炉台最近一个月的近7次控温数据，按周期保存为单独的 CSV 文件，并最终只保留最新的 7 份。
        """
        start_date, end_date = self._get_start_and_end_dates()
        table_sets = [
            ("Kongwen2ControlModel", "Kongwen2ControlModelPara"),
            ("Kongwen2ControlModelTest", "Kongwen2ControlModelParaTest")
        ]

        try:
            with self._connect_to_database() as connection:
                all_periods = []  # 存储所有控温周期

                for model_table, para_table in table_sets:
                    query = f"""
                    SELECT Model.*, Para.*
                    FROM {model_table} AS Model
                    JOIN {para_table} AS Para
                      ON Model.Id = Para.ControlID
                    WHERE Model.Furnace = %s
                      AND Model.CreateTime BETWEEN %s AND %s
                    ORDER BY Model.CreateTime ASC;
                    """
                    df = pd.read_sql(query, connection, params=(self.furnace_id, start_date, end_date))

                    if df.empty:
                        print(f"{model_table} 未找到符合条件的数据")
                        continue
                    print(df['CreateTime'].head(10))  # 查看前 10 行的 CreateTime 数据
                    df = df.loc[:, ~df.columns.duplicated()]  # 去掉重复列
                    df['CreateTime'] = pd.to_datetime(df['CreateTime'])  # 确保是 datetime 类型
                    df['TimeDiff'] = df['CreateTime'].diff().dt.total_seconds()
                    df['GroupID'] = (df['TimeDiff'] > 300).cumsum()

                    for period, sub_group in df.groupby('GroupID', group_keys=False):
                        all_periods.append(sub_group)

                # 处理每个控温周期
                processed_data = []
                for period in all_periods:
                    result = self.extract_info_from_control_period(period)
                    if result:
                        processed_data.append(result)

                # 按时间戳排序并保留最新的 7 条记录
                processed_data.sort(key=lambda x: x['控温开始时间'], reverse=True)
                processed_data = processed_data[:50]

                # 将最终结果保存
                result_df = pd.DataFrame(processed_data)
                return result_df

        except Exception as e:
            print(f"发生错误: {e}")
            return None

    def fetch_and_process_furnace_data(self,furnace_id):
        def _extract_vice_ratio(para_json):
            try:
                para = json.loads(para_json)
                jialiao = para.get("jialiao", 0)
                config = para.get('config', [])  # 正确的写法
                vice_ratios = config['vice_ratios']
                for w1, w2, r1, r2 in vice_ratios:
                    if w1 <= jialiao <= w2:
                        return (r1, r2)
                return None
            except json.JSONDecodeError:
                return None

        def _get_start_and_end_dates():
            end_date = pd.Timestamp.now()
            start_date = end_date - pd.DateOffset(months=1)
            return start_date, end_date

        start_date, end_date = _get_start_and_end_dates()
        table_sets = [
            "KongwenSetupQueryPara",
            "KongwenSetupQueryParaTest"
        ]

        try:
            with self._connect_to_database() as connection:
                all_results = []

                for table in table_sets:
                    query = f"""
                    SELECT Furnace, CreateTime, Para
                    FROM {table}
                    WHERE Furnace = %s
                      AND CreateTime BETWEEN %s AND %s
                    ORDER BY CreateTime ASC;
                    """
                    df = pd.read_sql(query, connection, params=(furnace_id, start_date, end_date))

                    if df.empty:
                        print(f"{table} 未找到符合条件的数据")
                        continue

                    df['CreateTime'] = pd.to_datetime(df['CreateTime'])
                    df['vice_close_ratio'] = df['Para'].apply(_extract_vice_ratio)

                    all_results.append(df[['Furnace', 'CreateTime', 'vice_close_ratio']])

                if all_results:
                    result_df = pd.concat(all_results).sort_values(by='CreateTime', ascending=False)
                    print(result_df)
                    return result_df
                else:
                    return pd.DataFrame(columns=['Furnace', 'CreateTime', 'vice_close_ratio'])

        except Exception as e:
            print(f"发生错误: {e}")
            return None

    def extract_info_from_control_period(self, df):
        """
        从单个控温周期的 DataFrame 中提取所需信息，并返回字典。
        """

        def get_time_diff_in_minutes(target_time, start_time):
            return (target_time - start_time).total_seconds() / 60 if pd.notna(target_time) else None

        # 找到副功率减半的时间
        half_power_time = df.loc[df['PushValue2'] <= df['PushValue2'].iloc[0] / 2, 'CreateTime'].min()
        half_power_interval = get_time_diff_in_minutes(half_power_time, df['CreateTime'].iloc[0])

        # 找到副功率置为 0 的时间
        zero_power_time = df.loc[df['PushValue2'] == 0, 'CreateTime'].min()
        zero_power_interval = get_time_diff_in_minutes(zero_power_time, df['CreateTime'].iloc[0])

        # 解析 ModelParas 列的 JSON 数据
        df['ModelParas'] = df['ModelParas'].apply(json.loads)

        # 副功率置 0 到溶液比 90 的时间
        ratio_90_time = df.loc[df['ModelParas'].apply(lambda x: x.get('ratio', 0) >= 90), 'CreateTime'].min()
        ratio_90_interval = get_time_diff_in_minutes(ratio_90_time, zero_power_time)

        # 副功率置 0 到全熔的时间
        full_melting_time = df.loc[df['ModelParas'].apply(lambda x: x.get('fullmelting', 0) == 1), 'CreateTime'].min()
        full_melting_interval = get_time_diff_in_minutes(full_melting_time, zero_power_time)

        # 副功率置 0 到溶液比第一次达到 98 的时间
        ratio_98_time = df.loc[df['ModelParas'].apply(lambda x: x.get('ratio', 0) >= 98), 'CreateTime'].min()
        ratio_98_interval = get_time_diff_in_minutes(ratio_98_time, zero_power_time)

        # **获取副功率减半时的溶液比**
        half_power_ratio = df.loc[df['CreateTime'] == half_power_time, 'ModelParas'].apply(
            lambda x: x.get('ratio', None)).values
        half_power_ratio = half_power_ratio[0] if len(half_power_ratio) > 0 else None

        # **获取副功率置 0 时的溶液比**
        zero_power_ratio = df.loc[df['CreateTime'] == zero_power_time, 'ModelParas'].apply(
            lambda x: x.get('ratio', None)).values
        zero_power_ratio = zero_power_ratio[0] if len(zero_power_ratio) > 0 else None

        # 提取加料数据
        last_jialiao_weight = df['ModelParas'].iloc[-1].get('last_jialiao_weight', None)
        last_jialiao_time = df['ModelParas'].iloc[-1].get('last_jialiao_time', None)
        last_Interval_time = df['ModelParas'].iloc[-1].get('last_Interval_time', None)

        last_but_one_jialiao_weight = df['ModelParas'].iloc[-1].get('last_but_one_jialiao_weight', None)
        last_but_one_jialiao_time = df['ModelParas'].iloc[-1].get('last_but_one_jialiao_time', None)
        last_but_one_Interval_time = df['ModelParas'].iloc[-1].get('last_but_one_Interval_time', None)

        sum_jialiao_time = df['ModelParas'].iloc[-1].get('sum_jialiao_time', None)
        barrelage = df['ModelParas'].iloc[-1].get('barrelage', None)

        furnace_id = df['Furnace'].iloc[0]

        return {
            '炉台号': furnace_id,
            '控温开始时间': df['CreateTime'].iloc[0],
            '控温开始到底加半关时间': half_power_interval,
            '控温开始到关底加时间': zero_power_interval,
            '关底加到溶液比90时间': ratio_90_interval,
            '关底加到全熔时间': full_melting_interval,
            '关底加到溶液比98时间': ratio_98_interval,
            '总加料时间（分钟）': sum_jialiao_time,
            '加料桶数': barrelage,
            '最后一桶加料重量': last_jialiao_weight,
            '最后一桶加料时长': last_jialiao_time / 60 if last_jialiao_time else None,
            '最后一桶加料间隔': last_Interval_time / 60 if last_Interval_time else None,
            '倒数第二桶加料重量': last_but_one_jialiao_weight,
            '倒数第二桶加料时长': last_but_one_jialiao_time / 60 if last_but_one_jialiao_time else None,
            '倒数第二桶加料间隔': last_but_one_Interval_time / 60 if last_but_one_Interval_time else None,
            '副功率减半时溶液比': half_power_ratio,
            '副功率置 0 时溶液比': zero_power_ratio
        }


    def fetch_furnace_data(self):
        """
        获取炉台数据
        """
        # 确保 current_date 是 datetime 类型
        if isinstance(self.current_date, str):
            self.current_date = datetime.strptime(self.current_date, "%Y-%m-%d")

        start_date = self.current_date - timedelta(days=50)

        try:
            with self._connect_to_database() as conn:
                sql = """
                SELECT 
                    Furnace, KongwenStartTime, TempAfterFixedCruciblePosition
                FROM KongwenControlReport
                WHERE Furnace = %s
                AND KongwenStartTime >= %s
                ORDER BY KongwenStartTime DESC
                LIMIT 50;
                """
                df = pd.read_sql(sql, conn, params=(self.furnace_id, start_date))

            df['KongwenStartTime'] = pd.to_datetime(df['KongwenStartTime'])

            # 更换列名
            column_mapping = {
                "Furnace": "炉台号", "KongwenStartTime": "控温开始时间",
                "TempAfterFixedCruciblePosition": "定完埚位的CCD温度（10分钟）"
            }
            df = df.rename(columns=column_mapping)
            # 仅返回需要的三列
            data_array = df[['炉台号', '控温开始时间', '定完埚位的CCD温度（10分钟）']].values.tolist()
            return pd.DataFrame(data_array, columns=df.columns.tolist())

        except Exception as e:
            print(f"获取炉台数据时发生错误: {e}")
            return None

    def merge_dataframes(self, processed_data, df, df_extra):
        """
        合并三个 DataFrame：
        - processed_data 和 df 按 "控温开始时间" 进行匹配。
        - df_extra 先将 "CreateTime" 重命名为 "控温开始时间"，再进行匹配。
        """

        # 确保时间列格式正确
        processed_data['控温开始时间'] = pd.to_datetime(processed_data['控温开始时间'])
        df['控温开始时间'] = pd.to_datetime(df['控温开始时间'])
        df_extra['CreateTime'] = pd.to_datetime(df_extra['CreateTime'])

        # 先将 df_extra 的 "CreateTime" 改为 "控温开始时间"
        df_extra = df_extra.rename(columns={'CreateTime': '控温开始时间'})
        df_extra = df_extra.rename(columns={'Furnace': '炉台号'})

        # 按时间排序
        processed_data = processed_data.sort_values('控温开始时间')
        df = df.sort_values('控温开始时间')
        df_extra = df_extra.sort_values('控温开始时间')

        # 先合并 processed_data 和 df（按 "控温开始时间" 匹配）
        merged_data = pd.merge_asof(processed_data, df, on='控温开始时间', by='炉台号',
                                    tolerance=pd.Timedelta('3min'), direction='nearest')

        # 再合并 df_extra（按 "控温开始时间" 匹配）
        merged_data = pd.merge_asof(merged_data, df_extra, on='控温开始时间', by='炉台号',
                                    tolerance=pd.Timedelta('3min'), direction='nearest')

        # 删除包含 NaN 的行
        merged_data = merged_data.dropna()

        # 计算超温和低温比率（基于 df）
        total_records = len(df)
        if total_records == 0:
            return merged_data, 0, 0  # 避免除零错误

        over_temp_count = (df['定完埚位的CCD温度（10分钟）'] > 1448.5).sum()
        under_temp_count = (df['定完埚位的CCD温度（10分钟）'] < 1446).sum()

        over_temp_ratio = over_temp_count / total_records
        under_temp_ratio = under_temp_count / total_records

        return merged_data, over_temp_ratio, under_temp_ratio

    def calculate_boundary_times(self, df, target_bucket_count):
        try:
            required_columns = {'加料桶数', '控温开始到底加半关时间', '控温开始到关底加时间', '定完埚位的CCD温度（10分钟）',
                                '关底加到全熔时间'}
            if not required_columns.issubset(df.columns):
                return None, None

            df = df[abs(df['加料桶数'] - target_bucket_count) <= 0]
            if df.empty:
                return None, None

            df = df[abs(df['控温开始到底加半关时间'] - df['控温开始到关底加时间']) <= 15]
            print('数据筛选',df)
            if df.empty:
                return None, None

            over_temp = df[df['定完埚位的CCD温度（10分钟）'] > 1450]['控温开始到关底加时间'].sort_values()
            under_temp = df[df['定完埚位的CCD温度（10分钟）'] < 1446]['控温开始到关底加时间'].sort_values(ascending=False)

            upper_limit = over_temp.iloc[:len(over_temp)+1 // 2].mean() if not over_temp.empty else None
            lower_limit = under_temp.iloc[:len(under_temp)+1 // 2].mean() if not under_temp.empty else None
            print(upper_limit,lower_limit)
            if lower_limit > upper_limit:
                return upper_limit, lower_limit
            elif lower_limit < upper_limit:
                return lower_limit, upper_limit
            else:
                return None, None
        except Exception as e:
            return None, None

    def match_data(self,processed_df, current_jialiao_tongshu, current_last_but_one_jialiao_weight):
        """
        根据加料桶数和倒数第二桶加料重量进行匹配，并提取相关字段。

        参数:
        processed_df: 处理后的 DataFrame，包含加料桶数、倒数第二桶加料重量等信息。
        current_jialiao_tongshu: 当前加料桶数
        current_last_but_one_jialiao_weight: 当前倒数第二桶加料重量

        返回:
        匹配到的相关字段值，若未匹配到则返回 None。
        """
        # 步骤1: 根据加料桶数筛选数据
        filtered_df = processed_df[processed_df['加料桶数'] == current_jialiao_tongshu]

        if filtered_df.empty:
            print(f"未找到加料桶数为 {current_jialiao_tongshu} 的数据")
            return None

        # 步骤2: 找到倒数第二桶加料重量最接近的记录
        # 计算绝对差值
        filtered_df['倒数第二桶加料重量差值'] = (
                    filtered_df['倒数第二桶加料重量'] - current_last_but_one_jialiao_weight).abs()

        # 找到最小差值的记录
        closest_match = filtered_df.loc[filtered_df['倒数第二桶加料重量差值'].idxmin()]

        # 提取匹配记录的相关字段
        matched_data = {
            '最后一桶加料间隔': closest_match['最后一桶加料间隔'],
            '最后一桶加料重量': closest_match['最后一桶加料重量'],
            '最后一桶加料时长': closest_match['最后一桶加料时长'],
            '倒数第二桶加料间隔': closest_match['倒数第二桶加料间隔'],
            '倒数第二桶加料重量': closest_match['倒数第二桶加料重量'],
            '倒数第二桶加料时长': closest_match['倒数第二桶加料时长'],
            '控温开始到关底加时间': closest_match['控温开始到底加半关时间'],
            '关底加到溶液比98时间': closest_match['关底加到溶液比98时间'],
            '关底加到全熔时间': closest_match['关底加到全熔时间'],
            '定埚位10min后CCD温度': closest_match['定完埚位的CCD温度（10分钟）'],
            'vice_close_ratio': closest_match['vice_close_ratio']
        }

        return matched_data

    def generate_feature_data(self,values):
        # 原始的 weight 和 direction 数据
        feature_data_template = [
            {'name': '最后一桶加料间隔', 'weight': 0.12072893, 'direction': -1},
            {'name': '最后一桶加料重量', 'weight': 0.00911162, 'direction': 1},
            {'name': '最后一桶加料时长', 'weight': 0.11845103, 'direction': 1},
            {'name': '倒数第二桶加料间隔', 'weight': 0.27790433, 'direction': -1},
            {'name': '倒数第二桶加料重量', 'weight': 0.15261959, 'direction': 1},
            {'name': '倒数第二桶加料时长', 'weight': 0.32118451, 'direction': 1},
        ]

        # 将传入的 values 拼接到新的数据结构中
        for i, value in enumerate(values):
            feature_data_template[i]['value'] = value  # 将 value 加入到字典中

        return feature_data_template

    def calculate_weighted_difference_from_array(self,match_data, feature_data):
        """
        计算带方向和权重的加权差异
        :param match_data: 字典，包含匹配数据的各个特征值
        :param feature_data: 列表，每个元素是一个字典，包含特征的 'name'、'value'、'weight'、'direction'
        :return: 总加权差异
        """
        total_difference = 0

        for feature in feature_data:
            # 获取特征名称、方向、权重
            feature_name = feature['name']
            direction = feature['direction']
            weight = feature['weight']
            current_value = feature['value']  # 当前数据值

            # 获取该特征的匹配值
            match_value = match_data.get(feature_name)

            if match_value is not None and current_value is not None:
                difference = (current_value - match_value) * direction * weight
                total_difference += difference

        return total_difference

# --------------------------- 主程序 ---------------------------

def calculate(furnace_id,current_jialiao_tongshu,current_last_but_one_jialiao_weight,values):

    try:
        with open("DBUtil/config.json", 'r') as file:
            json_string = file.read()
        config = json.loads(json_string)
        selected_name = config.get("data", None)
        host = selected_name['host']
        user = selected_name['user']
        port = selected_name['port']
        password = selected_name['password']
        database = selected_name['database']
        print(host)
        db_config = {
            'host': host,
            'user': user,
            'password': password,
            'database': database,
            'port': port
        }

        furnace_control = FurnaceControlData(db_config, furnace_id, current_date="2025-04-01")

        # 导出并处理控温数据
        processed_data = furnace_control.export_and_sort_control_data()

        # 获取炉台数据
        df = furnace_control.fetch_furnace_data()

        df_exa = furnace_control.fetch_and_process_furnace_data(furnace_id)

        # 合并数据
        merged_df,over_temp_ratio,under_temp_ratio = furnace_control.merge_dataframes(processed_data, df, df_exa)

        vice_lower, vice_upper = furnace_control.calculate_boundary_times(merged_df, current_jialiao_tongshu)

        # 获取定完埚位的CCD温度大于1450的最大时间
        lower_bound_time = merged_df[merged_df['定完埚位的CCD温度（10分钟）'] > 1450]['关底加到溶液比98时间'].max()

        # 获取定完埚位的CCD温度小于1446的最小时间
        upper_bound_time = merged_df[merged_df['定完埚位的CCD温度（10分钟）'] < 1446]['关底加到溶液比98时间'].min()

        # 如果上限时间小于下限时间，则返回空
        lower_bound_time = None if pd.isna(lower_bound_time) else lower_bound_time
        upper_bound_time = None if pd.isna(upper_bound_time) else upper_bound_time

        if (upper_bound_time and lower_bound_time) and upper_bound_time < lower_bound_time:
            lower_bound_time = None
            upper_bound_time = None
        # 获取定完埚位的CCD温度在 1446 到 1450 之间的最大时间
        middle_max_time = merged_df[
            (merged_df['定完埚位的CCD温度（10分钟）'] >= 1446) &
            (merged_df['定完埚位的CCD温度（10分钟）'] <= 1450)
            ]['关底加到溶液比98时间'].max()

        # 获取定完埚位的CCD温度在 1446 到 1450 之间的最小时间
        middle_min_time = merged_df[
            (merged_df['定完埚位的CCD温度（10分钟）'] >= 1446) &
            (merged_df['定完埚位的CCD温度（10分钟）'] <= 1450)
            ]['关底加到溶液比98时间'].min()

        # 如果没有符合条件的数据，则将其置为 None

        middle_max_time = None if pd.isna(middle_max_time) else middle_max_time
        middle_min_time = None if pd.isna(middle_min_time) else middle_min_time
        time_range = [middle_min_time, middle_max_time]
        limit_range = [lower_bound_time, upper_bound_time]

        # 当前的加料桶数和倒数第二桶加料量
        match_data = furnace_control.match_data(merged_df,current_jialiao_tongshu,current_last_but_one_jialiao_weight)
        if match_data:
            #print(match_data)
            # 特征数据数组，包含每个特征的名称、值、方向和权重
            feature_data = furnace_control.generate_feature_data(values)
            total_difference = furnace_control.calculate_weighted_difference_from_array(match_data, feature_data)
            if total_difference < 0 and match_data['定埚位10min后CCD温度'] >= 1446:
                risk = 1
            elif total_difference > 0 and match_data['定埚位10min后CCD温度'] <= 1444:
                risk = -1
            else:
                risk = 0
        else:
            return 0, None, None, time_range, limit_range, merged_df['vice_close_ratio'].iloc[
                -1], over_temp_ratio, under_temp_ratio,vice_lower, vice_upper

        return risk,match_data['控温开始到关底加时间'], match_data['关底加到全熔时间'],  time_range,limit_range,merged_df['vice_close_ratio'].iloc[-1], over_temp_ratio, under_temp_ratio,vice_lower, vice_upper

    except Exception as e:
        return None,None,None,None,None,None,None,None,None,None

# if __name__ == "__main__":
#     print(calculate(furnace_id = 'A17',current_jialiao_tongshu = 7,current_last_but_one_jialiao_weight = 50.0,values = [77.36666666666666, 29.5, 19.11, 81, 39.7, 36.7]))

