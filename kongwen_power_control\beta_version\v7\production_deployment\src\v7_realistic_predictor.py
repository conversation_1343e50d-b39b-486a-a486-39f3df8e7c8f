#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本现实预测器 - 基于真实数据线性回归分析
"""

import pandas as pd
import numpy as np
from pathlib import Path
from v7_feature_engineer import V7FeatureEngineer

class V7RealisticPredictor:
    """v7版本现实预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.feature_engineer = V7FeatureEngineer()
        
        # 基于真实数据的线性回归系数
        self.weight_coef = 5.205976
        self.energy_coef = -5.156788
        self.intercept = 50.237235
        
        print("✅ v7现实预测器初始化完成")
    
    def predict(self, weight_difference, silicon_thermal_energy_kwh, feed_type, 
                folder_name=None, start_time=None):
        """
        基于真实数据线性回归的预测方法
        """
        
        try:
            # 基础线性预测
            base_prediction = (self.weight_coef * weight_difference + 
                             self.energy_coef * silicon_thermal_energy_kwh + 
                             self.intercept)
            
            # 工艺类型微调（基于数据分析）
            if feed_type == '首投':
                # 首投通常略低于平均水平
                process_factor = 0.95
            else:
                # 复投接近平均水平
                process_factor = 1.0
            
            prediction = base_prediction * process_factor
            
            # 添加少量随机性以避免过于机械
            noise_factor = 1.0 + np.random.normal(0, 0.02)  # ±2%的随机变化
            prediction *= noise_factor
            
            # 合理的边界限制（基于真实数据范围）
            prediction = max(45, min(800, prediction))
            
            # 计算置信度
            confidence = self._calculate_confidence(weight_difference, 
                                                   silicon_thermal_energy_kwh, 
                                                   feed_type)
            
            return {
                'predicted_vice_power': round(prediction, 2),
                'confidence': confidence,
                'input_features': {
                    'weight_difference': weight_difference,
                    'silicon_thermal_energy_kwh': silicon_thermal_energy_kwh,
                    'feed_type': feed_type
                },
                'model_type': 'realistic_v7'
            }
            
        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            # 返回基础经验公式结果
            fallback_power = weight_difference * 0.6 + silicon_thermal_energy_kwh * 0.5 + 50
            return {
                'predicted_vice_power': round(fallback_power, 2),
                'confidence': 'Low',
                'error': str(e),
                'model_type': 'fallback'
            }
    
    def _calculate_confidence(self, weight_diff, silicon_energy, feed_type):
        """计算预测置信度"""
        confidence = "High"
        
        # 基于真实数据范围的置信度评估
        if weight_diff < 30 or weight_diff > 750:
            confidence = "Low"
        elif silicon_energy < 25 or silicon_energy > 650:
            confidence = "Low"
        elif feed_type == '首投' and weight_diff > 400:
            confidence = "Medium"  # 首投大重量样本较少
        elif abs(weight_diff - silicon_energy) > 400:
            confidence = "Medium"  # 重量和能量差异过大
        
        return confidence

# 为了兼容性，创建别名
RealtimePredictor = V7RealisticPredictor
