#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型文件检查器 - 不依赖特定环境
直接检查文件内容和元数据
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

def check_current_environment():
    """检查当前环境基本信息"""
    print("="*60)
    print("🔍 当前环境基本信息")
    print("="*60)
    
    current_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    python_path = sys.executable
    
    print(f"当前Conda环境: {current_env}")
    print(f"Python版本: {python_version}")
    print(f"Python路径: {python_path}")
    
    return {
        'conda_env': current_env,
        'python_version': python_version,
        'python_path': python_path
    }

def find_model_files():
    """查找所有模型相关文件"""
    print("\n" + "="*60)
    print("📁 查找模型相关文件")
    print("="*60)
    
    model_files = []
    json_files = []
    
    # 查找模型文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            file_path = Path(root) / file
            
            # 模型文件
            if file.endswith(('.joblib', '.pkl', '.pickle')):
                model_files.append(file_path)
            
            # JSON配置文件
            elif file.endswith('.json') and any(keyword in file.lower() for keyword in 
                ['model', 'result', 'performance', 'version', 'environment']):
                json_files.append(file_path)
    
    print(f"找到 {len(model_files)} 个模型文件:")
    for i, file_path in enumerate(model_files[:10]):
        size_mb = file_path.stat().st_size / (1024*1024)
        mod_time = datetime.fromtimestamp(file_path.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
        print(f"  {i+1}. {file_path} ({size_mb:.2f}MB, {mod_time})")
    
    print(f"\n找到 {len(json_files)} 个JSON配置文件:")
    for i, file_path in enumerate(json_files):
        print(f"  {i+1}. {file_path}")
    
    return model_files, json_files

def analyze_json_files(json_files):
    """分析JSON文件中的环境信息"""
    print("\n" + "="*60)
    print("📋 分析JSON文件中的环境信息")
    print("="*60)
    
    environment_info = {}
    
    for json_file in json_files:
        try:
            print(f"\n🔍 检查: {json_file}")
            
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 递归查找环境相关信息
            env_data = {}
            
            def extract_env_info(obj, path=''):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        
                        # 检查是否是环境相关的键
                        if any(keyword in key.lower() for keyword in 
                            ['env', 'environment', 'version', 'python', 'sklearn', 'numpy', 'pandas']):
                            env_data[current_path] = value
                            print(f"  ✅ {current_path}: {value}")
                        
                        # 递归检查嵌套对象
                        elif isinstance(value, (dict, list)):
                            extract_env_info(value, current_path)
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        if isinstance(item, (dict, list)):
                            extract_env_info(item, f"{path}[{i}]")
            
            extract_env_info(data)
            
            if env_data:
                environment_info[str(json_file)] = env_data
            else:
                print("  ❌ 未找到环境信息")
                
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
    
    return environment_info

def check_file_timestamps(model_files):
    """检查文件时间戳分析训练时间"""
    print("\n" + "="*60)
    print("⏰ 文件时间戳分析")
    print("="*60)
    
    timestamp_analysis = {}
    
    for model_file in model_files:
        try:
            stat = model_file.stat()
            
            timestamp_analysis[str(model_file)] = {
                'creation_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modification_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'size_bytes': stat.st_size,
                'size_mb': stat.st_size / (1024*1024)
            }
            
            print(f"📄 {model_file.name}:")
            print(f"  创建时间: {datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"  修改时间: {datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"  文件大小: {stat.st_size / (1024*1024):.2f} MB")
            
        except Exception as e:
            print(f"  ❌ 检查 {model_file} 失败: {e}")
    
    return timestamp_analysis

def analyze_environment_consistency(current_env_info, environment_info):
    """分析环境一致性"""
    print("\n" + "="*60)
    print("🔄 环境一致性分析")
    print("="*60)
    
    current_env = current_env_info['conda_env']
    
    # 从JSON文件中提取的环境信息
    extracted_environments = []
    
    for file_path, env_data in environment_info.items():
        for key, value in env_data.items():
            if 'environment' in key.lower() and isinstance(value, str):
                extracted_environments.append({
                    'source_file': file_path,
                    'key': key,
                    'environment': value
                })
                print(f"📋 从 {Path(file_path).name} 提取到环境: {value}")
    
    # 一致性检查
    consistency_results = {
        'current_environment': current_env,
        'extracted_environments': extracted_environments,
        'is_consistent': False,
        'inconsistencies': []
    }
    
    if extracted_environments:
        # 检查是否所有提取的环境都与当前环境一致
        all_consistent = True
        
        for env_info in extracted_environments:
            extracted_env = env_info['environment']
            
            if extracted_env != current_env:
                all_consistent = False
                consistency_results['inconsistencies'].append({
                    'source': env_info['source_file'],
                    'expected': extracted_env,
                    'current': current_env
                })
                print(f"⚠️  环境不一致:")
                print(f"   文件: {Path(env_info['source_file']).name}")
                print(f"   期望环境: {extracted_env}")
                print(f"   当前环境: {current_env}")
        
        consistency_results['is_consistent'] = all_consistent
        
        if all_consistent:
            print(f"✅ 环境一致: 所有模型都要求 {current_env} 环境")
        else:
            print(f"❌ 环境不一致: 发现 {len(consistency_results['inconsistencies'])} 个不匹配")
    else:
        print("⚠️  未找到环境要求信息")
    
    return consistency_results

def generate_final_report(current_env_info, environment_info, consistency_results, timestamp_analysis):
    """生成最终报告"""
    print("\n" + "="*60)
    print("📊 最终分析报告")
    print("="*60)
    
    report = {
        'analysis_timestamp': datetime.now().isoformat(),
        'current_environment': current_env_info,
        'extracted_environment_info': environment_info,
        'consistency_analysis': consistency_results,
        'file_timestamp_analysis': timestamp_analysis,
        'conclusions': {}
    }
    
    # 生成结论
    conclusions = {}
    
    # 环境一致性结论
    if consistency_results['is_consistent']:
        conclusions['environment_status'] = 'CONSISTENT'
        conclusions['required_environment'] = consistency_results['extracted_environments'][0]['environment'] if consistency_results['extracted_environments'] else 'UNKNOWN'
    else:
        conclusions['environment_status'] = 'INCONSISTENT'
        conclusions['required_environment'] = 'MULTIPLE_OR_UNKNOWN'
    
    # 操作建议
    recommendations = []
    
    if not consistency_results['is_consistent'] and consistency_results['inconsistencies']:
        for inconsistency in consistency_results['inconsistencies']:
            recommendations.append(f"切换到 {inconsistency['expected']} 环境")
    
    conclusions['recommendations'] = list(set(recommendations))  # 去重
    
    report['conclusions'] = conclusions
    
    # 输出结论
    print(f"环境状态: {conclusions['environment_status']}")
    print(f"要求的环境: {conclusions['required_environment']}")
    print(f"当前环境: {current_env_info['conda_env']}")
    
    if conclusions['recommendations']:
        print("\n🔧 建议操作:")
        for rec in conclusions['recommendations']:
            print(f"  - {rec}")
    else:
        print("\n✅ 环境配置正确")
    
    # 保存报告
    report_file = 'model_environment_analysis_report.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    return report

def main():
    """主函数"""
    print("🔍 模型文件环境检查器")
    print("不依赖特定环境，直接分析文件内容")
    
    # 1. 检查当前环境
    current_env_info = check_current_environment()
    
    # 2. 查找模型文件
    model_files, json_files = find_model_files()
    
    # 3. 分析JSON文件
    environment_info = analyze_json_files(json_files)
    
    # 4. 检查文件时间戳
    timestamp_analysis = check_file_timestamps(model_files)
    
    # 5. 分析环境一致性
    consistency_results = analyze_environment_consistency(current_env_info, environment_info)
    
    # 6. 生成最终报告
    final_report = generate_final_report(current_env_info, environment_info, consistency_results, timestamp_analysis)
    
    print("\n" + "="*60)
    print("✅ 分析完成")
    print("="*60)
    
    return final_report

if __name__ == "__main__":
    main()
