{"version": "v7.0", "creation_time": "2025-07-23T16:19:47.260674", "base_version": "v6", "model_source": "production_ready_models", "architecture": "single_model", "model_type": "ensemble_learning", "algorithms": ["RandomForest", "GradientBoosting", "Ridge"], "features": 26, "data_leakage_free": true, "performance": {"accuracy_10kwh": "71.3%", "mae": "7.79 kWh", "r2_score": "0.9972"}, "changes_from_v6": ["替换双模型架构为单模型架构", "集成实时训练的ensemble模型", "使用26个无数据泄露特征", "保持相同的控制逻辑和接口"]}