#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本简化预测器 - 避免依赖问题
"""

import pandas as pd
import numpy as np
from pathlib import Path
from v7_feature_engineer import V7FeatureEngineer

class V7SimplePredictor:
    """v7版本简化预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.feature_engineer = V7FeatureEngineer()
        print("✅ v7简化预测器初始化完成")
    
    def predict(self, weight_difference, silicon_thermal_energy_kwh, feed_type, 
                folder_name=None, start_time=None):
        """
        预测副功率 - 使用简化的经验公式
        
        参数:
            weight_difference: 重量差异 (kg)
            silicon_thermal_energy_kwh: 硅热能需求 (kWh)
            feed_type: 工艺类型 ('首投' 或 '复投')
            folder_name: 设备名称 (可选)
            start_time: 开始时间 (可选)
        
        返回:
            dict: 包含预测结果和置信度
        """
        
        try:
            # 创建输入数据
            input_data = pd.DataFrame({
                'weight_difference': [weight_difference],
                'silicon_thermal_energy_kwh': [silicon_thermal_energy_kwh],
                'feed_type': [feed_type],
                'folder_name': [folder_name or 'analoga01'],
                'start_time': [start_time or pd.Timestamp.now()],
                'vice_total_energy_kwh': [0]  # 占位符
            })
            
            # 特征工程
            X = self.feature_engineer.create_realtime_features(input_data)
            
            # 使用简化的经验公式进行预测
            predicted_power = self._simplified_prediction(X.iloc[0])
            
            # 计算置信度
            confidence = self._calculate_confidence(weight_difference, 
                                                   silicon_thermal_energy_kwh, 
                                                   feed_type)
            
            return {
                'predicted_vice_power': round(predicted_power, 2),
                'confidence': confidence,
                'input_features': {
                    'weight_difference': weight_difference,
                    'silicon_thermal_energy_kwh': silicon_thermal_energy_kwh,
                    'feed_type': feed_type
                },
                'model_type': 'simplified_v7'
            }
            
        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            # 返回基础经验公式结果
            fallback_power = weight_difference * 0.8 + silicon_thermal_energy_kwh * 0.6
            return {
                'predicted_vice_power': round(fallback_power, 2),
                'confidence': 'Low',
                'error': str(e),
                'model_type': 'fallback'
            }
    
    def _simplified_prediction(self, features):
        """改进的预测公式，基于特征工程结果"""
        
        # 基于训练结果的改进权重和公式
        weight_diff = features.get('weight_diff', 0)
        silicon_energy = features.get('silicon_energy', 0)
        is_first_cast = features.get('is_first_cast', 0)
        
        # 改进的预测公式 - 基于物理模型和经验数据
        base_prediction = weight_diff * 0.75 + silicon_energy * 0.65
        
        # 工艺类型调整
        if is_first_cast == 1:
            # 首投工艺通常需要更少的副功率
            process_factor = 0.8
        else:
            # 复投工艺需要标准副功率
            process_factor = 1.0
        
        # 应用工艺因子
        prediction = base_prediction * process_factor
        
        # 添加基础偏移量
        prediction += 50
        
        # 基于能量密度的调整
        energy_density = features.get('energy_density', 1.0)
        if energy_density > 2.0:
            prediction *= 1.1  # 高能量密度需要更多副功率
        elif energy_density < 0.5:
            prediction *= 0.9  # 低能量密度需要较少副功率
        
        # 确保预测值在合理范围内
        prediction = max(80, min(600, prediction))
        
        return prediction
    
    def _calculate_confidence(self, weight_diff, silicon_energy, feed_type):
        """计算预测置信度"""
        confidence = "High"
        
        # 检查输入范围
        if weight_diff < 50 or weight_diff > 800:
            confidence = "Low"
        elif silicon_energy < 50 or silicon_energy > 800:
            confidence = "Low"
        elif feed_type == '首投' and weight_diff > 500:
            confidence = "Medium"  # 首投大重量样本较少
        elif abs(weight_diff - silicon_energy) > 200:
            confidence = "Medium"  # 重量和能量差异过大
        
        return confidence

# 为了兼容性，创建别名
RealtimePredictor = V7SimplePredictor
