#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量分析脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def analyze_training_data():
    """分析训练数据质量"""
    
    # 读取训练数据
    df = pd.read_csv('output_results/A01_A40_cycles__analysis.csv')
    
    print("=" * 60)
    print("副功率预测模型训练数据质量分析")
    print("=" * 60)
    
    # 基本信息
    print("\n1. 数据基本信息:")
    print(f"总样本数: {len(df)}")
    print(f"首投样本数: {len(df[df['feed_type'] == '首投'])}")
    print(f"复投样本数: {len(df[df['feed_type'] == '复投'])}")
    
    # 关键字段统计
    print("\n2. 副功率分布统计:")
    print(df['vice_total_energy_kwh'].describe())
    
    print("\n3. 重量差异分布:")
    print(df['weight_difference'].describe())
    
    print("\n4. 硅热能分布:")
    print(df['silicon_thermal_energy_kwh'].describe())
    
    # 异常值检查
    print("\n5. 异常值检查:")
    print(f"副功率异常值(>1000kWh): {len(df[df['vice_total_energy_kwh'] > 1000])}")
    print(f"副功率异常值(<0kWh): {len(df[df['vice_total_energy_kwh'] < 0])}")
    print(f"重量差异异常值(<0): {len(df[df['weight_difference'] < 0])}")
    print(f"硅热能异常值(<0): {len(df[df['silicon_thermal_energy_kwh'] < 0])}")
    
    # 按工艺类型分析
    print("\n6. 按工艺类型分析:")
    for feed_type in ['首投', '复投']:
        subset = df[df['feed_type'] == feed_type]
        print(f"\n{feed_type}工艺:")
        print(f"  样本数: {len(subset)}")
        print(f"  副功率均值: {subset['vice_total_energy_kwh'].mean():.2f} kWh")
        print(f"  副功率标准差: {subset['vice_total_energy_kwh'].std():.2f} kWh")
        print(f"  重量差异均值: {subset['weight_difference'].mean():.2f} kg")
        print(f"  硅热能均值: {subset['silicon_thermal_energy_kwh'].mean():.2f} kWh")
    
    # 数据质量问题
    print("\n7. 数据质量问题:")
    missing_values = df.isnull().sum()
    if missing_values.sum() > 0:
        print("缺失值:")
        print(missing_values[missing_values > 0])
    else:
        print("无缺失值")
    
    # 相关性分析
    print("\n8. 关键特征相关性:")
    correlation = df[['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']].corr()
    print(correlation)
    
    return df

if __name__ == "__main__":
    df = analyze_training_data()
