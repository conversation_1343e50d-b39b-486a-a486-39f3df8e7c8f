#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的SVR模型测试和训练脚本
"""

import os
import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print("="*60)
    print("🔍 环境检查")
    print("="*60)
    print(f"当前环境: {conda_env}")
    
    if conda_env == 'lj_env_1':
        print("✅ 环境正确：lj_env_1")
        return True
    else:
        print(f"⚠️ 环境警告：当前为 {conda_env}，建议使用 lj_env_1")
        return False

def load_and_test_existing_svr():
    """加载并测试现有SVR模型"""
    print("\n" + "="*60)
    print("📥 测试现有SVR模型")
    print("="*60)
    
    try:
        import joblib
        
        # 模型文件路径
        model_path = Path("副功率预测_85.4%准确率_完整项目/models")
        model_file = model_path / "best_model_svr.joblib"
        scaler_file = model_path / "scaler.joblib"
        selector_file = model_path / "feature_selector.joblib"
        results_file = model_path / "results.json"
        
        # 检查文件存在性
        if not all(f.exists() for f in [model_file, scaler_file, selector_file]):
            print("❌ SVR模型文件不完整")
            return None
        
        # 加载模型
        model = joblib.load(model_file)
        scaler = joblib.load(scaler_file)
        selector = joblib.load(selector_file)
        
        print(f"✅ SVR模型加载成功")
        print(f"📊 模型类型: {type(model)}")
        
        # 读取性能信息
        if results_file.exists():
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
                print(f"📊 报告准确率: {results.get('best_accuracy', 'unknown')}%")
        
        return {'model': model, 'scaler': scaler, 'selector': selector}
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def load_test_data():
    """加载测试数据"""
    print("\n" + "="*60)
    print("📊 加载测试数据")
    print("="*60)
    
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return None
    
    try:
        df = pd.read_csv(data_file)
        print(f"✅ 数据加载成功: {df.shape}")
        
        # 数据清洗
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        df_clean = df.dropna(subset=required_cols)
        
        # 过滤异常值
        df_filtered = df_clean[
            (df_clean['weight_difference'] > 0) &
            (df_clean['weight_difference'] < 1000) &
            (df_clean['silicon_thermal_energy_kwh'] > 0) &
            (df_clean['silicon_thermal_energy_kwh'] < 1000) &
            (df_clean['vice_total_energy_kwh'] > 0) &
            (df_clean['vice_total_energy_kwh'] < 2000)
        ]
        
        print(f"📊 清洗后数据: {df_filtered.shape}")
        
        # 随机采样100条
        if len(df_filtered) > 100:
            test_data = df_filtered.sample(n=100, random_state=42)
        else:
            test_data = df_filtered
        
        print(f"📊 测试样本: {len(test_data)} 条")
        
        return test_data
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def prepare_features_for_svr(df):
    """为SVR准备特征"""
    print("\n🔧 准备SVR特征...")
    
    features_list = []
    
    for _, row in df.iterrows():
        # 基础特征
        base_features = [
            row.get('start_weight', 500),
            row.get('end_weight', 700),
            row.get('weight_difference', 200),
            row.get('end_temperature_celsius', 1450),
            row.get('first_crystal_seeding_main_power_kw', 60),
            row.get('feed_number_1_records', 0),
            row.get('silicon_thermal_energy_kwh', 150),
            row.get('energy_efficiency_percent', 75),
            row.get('record_count', 3000),
            row.get('duration_hours', 3)
        ]
        
        # 工程特征
        weight_diff = row.get('weight_difference', 200)
        silicon_energy = row.get('silicon_thermal_energy_kwh', 150)
        duration = max(row.get('duration_hours', 3), 0.1)
        
        engineered_features = [
            weight_diff ** 2,
            np.sqrt(abs(weight_diff)),
            np.log1p(abs(weight_diff)),
            silicon_energy ** 2,
            np.sqrt(abs(silicon_energy)),
            np.log1p(abs(silicon_energy)),
            duration ** 2,
            np.sqrt(abs(duration)),
            np.log1p(abs(duration)),
            weight_diff * silicon_energy,
            weight_diff * duration,
            weight_diff / duration,
            silicon_energy * duration,
            silicon_energy / duration,
            1 if row.get('feed_type') == '首投' else 0,
            # 额外特征
            row.get('start_weight', 500) / duration,
            row.get('end_temperature_celsius', 1450) / 1000,
            row.get('first_crystal_seeding_main_power_kw', 60) * duration,
            row.get('energy_efficiency_percent', 75) / 100,
            row.get('record_count', 3000) / 1000
        ]
        
        all_features = base_features + engineered_features
        features_list.append(all_features)
    
    feature_matrix = np.array(features_list)
    print(f"✅ 特征准备完成: {feature_matrix.shape}")
    
    return feature_matrix

def test_svr_accuracy(model_components, test_data):
    """测试SVR准确率"""
    print("\n" + "="*60)
    print("🧪 测试SVR模型准确率")
    print("="*60)
    
    if model_components is None:
        print("❌ 模型组件为空")
        return None
    
    model = model_components['model']
    scaler = model_components['scaler']
    selector = model_components['selector']
    
    # 准备特征
    X_test = prepare_features_for_svr(test_data)
    y_test = test_data['vice_total_energy_kwh'].values
    
    predictions = []
    actual_values = []
    failed_count = 0
    
    print(f"📊 开始预测 {len(test_data)} 个样本...")
    
    for i in range(len(X_test)):
        try:
            # 特征选择和标准化
            features_selected = selector.transform([X_test[i]])
            features_scaled = scaler.transform(features_selected)
            
            # 预测
            pred = model.predict(features_scaled)[0]
            predictions.append(pred)
            actual_values.append(y_test[i])
            
        except Exception as e:
            failed_count += 1
            continue
    
    if len(predictions) == 0:
        print("❌ 所有预测都失败了")
        return None
    
    # 计算评估指标
    predictions = np.array(predictions)
    actual_values = np.array(actual_values)
    
    errors = np.abs(predictions - actual_values)
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean((predictions - actual_values) ** 2))
    
    # 准确率指标
    acc_5 = (errors <= 5).mean() * 100
    acc_10 = (errors <= 10).mean() * 100
    acc_15 = (errors <= 15).mean() * 100
    
    results = {
        'successful_predictions': len(predictions),
        'failed_predictions': failed_count,
        'mae': float(mae),
        'rmse': float(rmse),
        'accuracy_5kwh': float(acc_5),
        'accuracy_10kwh': float(acc_10),
        'accuracy_15kwh': float(acc_15),
        'prediction_range': {
            'min': float(np.min(predictions)),
            'max': float(np.max(predictions)),
            'mean': float(np.mean(predictions))
        },
        'actual_range': {
            'min': float(np.min(actual_values)),
            'max': float(np.max(actual_values)),
            'mean': float(np.mean(actual_values))
        }
    }
    
    print(f"\n📊 SVR模型测试结果:")
    print(f"  成功预测: {results['successful_predictions']} / {len(test_data)}")
    print(f"  失败预测: {results['failed_predictions']}")
    print(f"  ±5kWh准确率: {results['accuracy_5kwh']:.2f}%")
    print(f"  ±10kWh准确率: {results['accuracy_10kwh']:.2f}%")
    print(f"  ±15kWh准确率: {results['accuracy_15kwh']:.2f}%")
    print(f"  MAE: {results['mae']:.2f} kWh")
    print(f"  RMSE: {results['rmse']:.2f} kWh")
    
    return results

def train_new_svr_model(df):
    """训练新的SVR模型"""
    print("\n" + "="*60)
    print("🚀 训练新的SVR模型")
    print("="*60)
    
    try:
        from sklearn.svm import SVR
        from sklearn.preprocessing import StandardScaler
        from sklearn.feature_selection import SelectKBest, f_regression
        from sklearn.model_selection import train_test_split
        import joblib
        
        # 准备数据
        X = prepare_features_for_svr(df)
        y = df['vice_total_energy_kwh'].values
        
        print(f"📊 训练数据: {X.shape}")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 特征选择
        selector = SelectKBest(score_func=f_regression, k=20)
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        # 训练SVR模型
        print("🔧 训练SVR模型...")
        svr = SVR(kernel='rbf', C=100, gamma='scale', epsilon=0.1)
        svr.fit(X_train_scaled, y_train)
        
        # 预测和评估
        y_pred = svr.predict(X_test_scaled)
        
        errors = np.abs(y_pred - y_test)
        mae = np.mean(errors)
        acc_10 = (errors <= 10).mean() * 100
        
        print(f"✅ 新模型训练完成")
        print(f"📊 ±10kWh准确率: {acc_10:.2f}%")
        print(f"📊 MAE: {mae:.2f} kWh")
        
        # 保存新模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path(f"retrained_svr_lj_env_1_{timestamp}")
        model_dir.mkdir(exist_ok=True)
        
        joblib.dump(svr, model_dir / "best_model_svr.joblib")
        joblib.dump(scaler, model_dir / "scaler.joblib")
        joblib.dump(selector, model_dir / "feature_selector.joblib")
        
        # 保存结果
        results = {
            'timestamp': timestamp,
            'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
            'accuracy_10kwh': float(acc_10),
            'mae': float(mae),
            'training_samples': len(X_train),
            'test_samples': len(X_test)
        }
        
        with open(model_dir / "results.json", 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 新模型已保存到: {model_dir}")
        
        return results, model_dir
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return None, None

def main():
    """主函数"""
    print("="*60)
    print("🧪 SVR模型深度测试和重新训练")
    print("="*60)
    
    # 1. 环境检查
    env_ok = check_environment()
    
    # 2. 加载测试数据
    test_data = load_test_data()
    if test_data is None:
        print("\n❌ 测试数据加载失败")
        return
    
    # 3. 测试现有模型
    model_components = load_and_test_existing_svr()
    if model_components:
        existing_results = test_svr_accuracy(model_components, test_data)
    else:
        existing_results = None
    
    # 4. 训练新模型
    print("\n🚀 开始训练新的SVR模型...")
    new_results, model_dir = train_new_svr_model(test_data)
    
    # 5. 结果对比
    print("\n" + "="*60)
    print("📊 最终结果")
    print("="*60)
    
    print(f"环境状态: {'✅ lj_env_1' if env_ok else '⚠️ 非lj_env_1'}")
    print(f"测试数据: {len(test_data)} 条样本")
    
    if existing_results:
        print(f"\n现有SVR模型:")
        print(f"  ±10kWh准确率: {existing_results['accuracy_10kwh']:.2f}%")
        print(f"  MAE: {existing_results['mae']:.2f} kWh")
        print(f"  成功预测: {existing_results['successful_predictions']} / {len(test_data)}")
    
    if new_results:
        print(f"\n新训练SVR模型:")
        print(f"  ±10kWh准确率: {new_results['accuracy_10kwh']:.2f}%")
        print(f"  MAE: {new_results['mae']:.2f} kWh")
        print(f"  保存位置: {model_dir}")
    
    # 保存测试报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        'test_timestamp': timestamp,
        'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
        'environment_ok': env_ok,
        'test_data_size': len(test_data),
        'existing_model_results': existing_results,
        'new_model_results': new_results,
        'new_model_directory': str(model_dir) if model_dir else None
    }
    
    report_file = f"svr_deep_test_report_{timestamp}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存: {report_file}")
    print("✅ SVR深度测试完成")

if __name__ == "__main__":
    main()
