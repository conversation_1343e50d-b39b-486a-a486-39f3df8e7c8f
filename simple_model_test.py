#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的副功率预测模型测试脚本
"""

import os
import sys
import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print(f"当前环境: {conda_env}")
    
    if conda_env != 'lj_env_1':
        print(f"⚠️ 警告：当前环境为 {conda_env}，建议使用 lj_env_1 环境")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def load_test_data():
    """加载测试数据"""
    print("\n📊 加载测试数据...")
    
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return None
    
    try:
        df = pd.read_csv(data_file)
        print(f"✅ 数据加载成功: {df.shape}")
        
        # 检查必要列
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh', 'feed_type']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"❌ 缺少列: {missing_cols}")
            return None
        
        # 清洗数据
        df_clean = df.dropna(subset=required_cols)
        print(f"📊 清洗后数据: {df_clean.shape}")
        
        # 随机采样100条
        if len(df_clean) > 100:
            df_sample = df_clean.sample(n=100, random_state=42)
        else:
            df_sample = df_clean
        
        print(f"📊 测试样本: {len(df_sample)} 条")
        return df_sample
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def find_models():
    """查找可用模型"""
    print("\n🔍 查找可用模型...")
    
    models = {}
    
    # 1. SVR模型 (85.4%准确率)
    svr_path = Path("副功率预测_85.4%准确率_完整项目/models")
    if (svr_path / "best_model_svr.joblib").exists():
        models['SVR_85.4%'] = {
            'path': svr_path,
            'type': 'svr',
            'description': 'SVR模型 (85.4%准确率)'
        }
        print(f"  ✅ 发现SVR模型")
    
    # 2. 生产集成模型
    prod_path = Path("production_ready_models")
    if (prod_path / "ensemble_model.joblib").exists():
        models['Production'] = {
            'path': prod_path,
            'type': 'ensemble',
            'description': '生产集成模型'
        }
        print(f"  ✅ 发现生产模型")
    
    # 3. 实时模型
    realtime_path = Path("realtime_vice_power_models")
    if (realtime_path / "ensemble_model.joblib").exists():
        models['Realtime'] = {
            'path': realtime_path,
            'type': 'realtime',
            'description': '实时集成模型'
        }
        print(f"  ✅ 发现实时模型")
    
    print(f"📊 总共发现 {len(models)} 个模型")
    return models

def test_model_loading():
    """测试模型加载"""
    print("\n🧪 测试模型加载...")
    
    models = find_models()
    results = {}
    
    for name, info in models.items():
        print(f"\n🔬 测试 {name}...")
        try:
            # 尝试导入joblib
            import joblib
            
            if info['type'] == 'svr':
                # SVR模型需要三个文件
                model_file = info['path'] / "best_model_svr.joblib"
                scaler_file = info['path'] / "scaler.joblib"
                selector_file = info['path'] / "feature_selector.joblib"
                
                if all(f.exists() for f in [model_file, scaler_file, selector_file]):
                    model = joblib.load(model_file)
                    scaler = joblib.load(scaler_file)
                    selector = joblib.load(selector_file)
                    
                    results[name] = {
                        'status': 'loaded_successfully',
                        'model_type': str(type(model)),
                        'files': ['model', 'scaler', 'selector']
                    }
                    print(f"  ✅ {name} 加载成功")
                else:
                    results[name] = {'status': 'missing_files'}
                    print(f"  ❌ {name} 文件缺失")
            
            else:
                # 集成模型
                model_file = info['path'] / "ensemble_model.joblib"
                
                if model_file.exists():
                    model = joblib.load(model_file)
                    results[name] = {
                        'status': 'loaded_successfully',
                        'model_type': str(type(model))
                    }
                    print(f"  ✅ {name} 加载成功")
                else:
                    results[name] = {'status': 'file_not_found'}
                    print(f"  ❌ {name} 文件不存在")
                    
        except ImportError:
            results[name] = {'status': 'joblib_not_available'}
            print(f"  ❌ {name} joblib不可用")
        except Exception as e:
            results[name] = {'status': 'error', 'error': str(e)}
            print(f"  ❌ {name} 加载失败: {e}")
    
    return results

def simple_prediction_test():
    """简单预测测试"""
    print("\n🎯 简单预测测试...")
    
    try:
        import joblib
        
        # 测试SVR模型
        svr_path = Path("副功率预测_85.4%准确率_完整项目/models")
        if (svr_path / "best_model_svr.joblib").exists():
            print("🔬 测试SVR模型预测...")
            
            model = joblib.load(svr_path / "best_model_svr.joblib")
            scaler = joblib.load(svr_path / "scaler.joblib")
            selector = joblib.load(svr_path / "feature_selector.joblib")
            
            # 创建测试特征（30个特征）
            test_features = np.array([[
                500, 700, 200, 1450, 60, 0, 150, 75, 3000, 3,  # 基础特征
                40000, 14.14, 5.3, 22500, 12.25, 5.01, 9, 3, 1.73,  # 工程特征1
                30000, 600, 66.67, 450, 50, 1  # 工程特征2，补齐到25个
            ] + [0] * 5])  # 补齐到30个特征
            
            # 特征选择和标准化
            features_selected = selector.transform(test_features)
            features_scaled = scaler.transform(features_selected)
            
            # 预测
            prediction = model.predict(features_scaled)[0]
            print(f"  ✅ SVR预测结果: {prediction:.2f} kWh")
            
            return {'SVR': prediction}
            
    except Exception as e:
        print(f"  ❌ 预测测试失败: {e}")
        return {}

def analyze_data_distribution():
    """分析数据分布"""
    print("\n📈 分析数据分布...")
    
    test_data = load_test_data()
    if test_data is None:
        return
    
    print(f"📊 数据统计:")
    print(f"  样本数量: {len(test_data)}")
    print(f"  重量差异: {test_data['weight_difference'].min():.1f} - {test_data['weight_difference'].max():.1f} kg")
    print(f"  硅热能: {test_data['silicon_thermal_energy_kwh'].min():.1f} - {test_data['silicon_thermal_energy_kwh'].max():.1f} kWh")
    print(f"  副功率: {test_data['vice_total_energy_kwh'].min():.1f} - {test_data['vice_total_energy_kwh'].max():.1f} kWh")
    
    feed_type_counts = test_data['feed_type'].value_counts()
    print(f"  工艺类型: {feed_type_counts.to_dict()}")

def main():
    """主函数"""
    print("="*60)
    print("🧪 副功率预测模型简化测试")
    print("="*60)
    
    # 1. 环境检查
    env_ok = check_environment()
    
    # 2. 数据分析
    analyze_data_distribution()
    
    # 3. 模型发现
    models = find_models()
    
    # 4. 模型加载测试
    loading_results = test_model_loading()
    
    # 5. 简单预测测试
    prediction_results = simple_prediction_test()
    
    # 6. 结果汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    print(f"环境状态: {'✅ lj_env_1' if env_ok else '⚠️ 非lj_env_1'}")
    print(f"发现模型: {len(models)} 个")
    
    print(f"\n模型加载结果:")
    for name, result in loading_results.items():
        status = result['status']
        if status == 'loaded_successfully':
            print(f"  ✅ {name}: 加载成功")
        else:
            print(f"  ❌ {name}: {status}")
    
    if prediction_results:
        print(f"\n预测测试结果:")
        for name, pred in prediction_results.items():
            print(f"  🎯 {name}: {pred:.2f} kWh")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results = {
        'timestamp': timestamp,
        'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
        'environment_ok': env_ok,
        'models_found': len(models),
        'loading_results': loading_results,
        'prediction_results': prediction_results
    }
    
    results_file = f"simple_test_results_{timestamp}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 结果已保存到: {results_file}")
    print("✅ 测试完成")

if __name__ == "__main__":
    main()
