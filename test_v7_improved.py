#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试v7改进版本的性能
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def load_test_sample():
    """加载测试样本"""
    print("📊 加载测试样本...")
    
    # 从最新的验证结果中获取一些样本
    result_dirs = list(Path(".").glob("v7_real_validation_*"))
    if not result_dirs:
        print("❌ 未找到验证结果目录")
        return None
    
    latest_dir = max(result_dirs, key=lambda x: x.stat().st_mtime)
    results_file = latest_dir / "v7_real_data_validation_results.csv"
    
    if not results_file.exists():
        print(f"❌ 未找到结果文件: {results_file}")
        return None
    
    df = pd.read_csv(results_file)
    
    # 选择一些代表性样本
    sample_df = df.sample(n=min(20, len(df)), random_state=42)
    print(f"✅ 选择了 {len(sample_df)} 个测试样本")
    
    return sample_df

def test_improved_v7():
    """测试改进的v7模型"""
    print("\n🧪 测试改进的v7模型...")
    
    try:
        v7_path = Path("kongwen_power_control/beta_version/v7")
        sys.path.insert(0, str(v7_path))
        
        # 清除之前的导入
        modules_to_remove = [m for m in sys.modules.keys() if 'model' in m]
        for module in modules_to_remove:
            if module != '__main__':
                del sys.modules[module]
        
        from model import VicePowerControlModel
        v7_model = VicePowerControlModel()
        
        print("✅ v7改进模型加载成功")
        return v7_model
        
    except Exception as e:
        print(f"❌ v7改进模型加载失败: {e}")
        return None

def run_comparison_test(sample_df, v7_model):
    """运行对比测试"""
    print("\n🔄 运行对比测试...")
    
    results = []
    
    for idx, row in sample_df.iterrows():
        try:
            # 重置模型状态
            if hasattr(v7_model, 'reset_vice_power_state'):
                v7_model.reset_vice_power_state()
            
            # 准备输入参数
            params = {
                't': 0,
                'ratio': 1.0,
                'ccd': 1400,
                'ccd3': 1400,
                'fullmelting': True,
                'sum_jialiao_time': 3600,
                'last_jialiao_weight': row['weight_difference'],
                'last_Interval_time': 600,
                'barrelage': row['weight_difference'],
                'time_interval': 600,
                'cumulative_feed_weight': row['weight_difference'] * (1 if row.get('feed_type') == '首投' else 2)
            }
            
            # 执行预测
            main_power, vice_power, vice_info = v7_model.predict(**params)
            
            # 记录结果
            actual_value = row['actual_vice_power']
            old_predicted = row['predicted_vice_power']
            new_predicted = vice_info.get('predicted_total', 0)
            
            old_error = abs(old_predicted - actual_value)
            new_error = abs(new_predicted - actual_value)
            
            results.append({
                'actual': actual_value,
                'old_predicted': old_predicted,
                'new_predicted': new_predicted,
                'old_error': old_error,
                'new_error': new_error,
                'improvement': old_error - new_error,
                'old_within_10': old_error <= 10,
                'new_within_10': new_error <= 10
            })
            
        except Exception as e:
            print(f"⚠️ 样本 {idx} 测试失败: {e}")
            continue
    
    return pd.DataFrame(results)

def analyze_improvement(results_df):
    """分析改进效果"""
    print("\n📈 分析改进效果...")
    
    if len(results_df) == 0:
        print("❌ 没有有效的测试结果")
        return
    
    # 计算性能指标
    old_mae = results_df['old_error'].mean()
    new_mae = results_df['new_error'].mean()
    mae_improvement = old_mae - new_mae
    
    old_acc_10 = (results_df['old_within_10'].sum() / len(results_df)) * 100
    new_acc_10 = (results_df['new_within_10'].sum() / len(results_df)) * 100
    acc_improvement = new_acc_10 - old_acc_10
    
    print(f"性能对比结果:")
    print(f"  样本数量: {len(results_df)}")
    print(f"  ")
    print(f"  MAE对比:")
    print(f"    改进前: {old_mae:.2f} kWh")
    print(f"    改进后: {new_mae:.2f} kWh")
    print(f"    改进幅度: {mae_improvement:.2f} kWh ({mae_improvement/old_mae*100:.1f}%)")
    print(f"  ")
    print(f"  ±10kWh准确率对比:")
    print(f"    改进前: {old_acc_10:.1f}%")
    print(f"    改进后: {new_acc_10:.1f}%")
    print(f"    改进幅度: {acc_improvement:.1f}%")
    
    # 显示一些具体例子
    print(f"\n📋 具体改进例子:")
    print(f"{'实际值':<8} {'改进前':<8} {'改进后':<8} {'改进前误差':<10} {'改进后误差':<10} {'改进效果':<8}")
    print("-" * 70)
    
    # 选择改进最明显的几个例子
    top_improvements = results_df.nlargest(5, 'improvement')
    for _, row in top_improvements.iterrows():
        print(f"{row['actual']:<8.1f} {row['old_predicted']:<8.1f} {row['new_predicted']:<8.1f} "
              f"{row['old_error']:<10.1f} {row['new_error']:<10.1f} "
              f"{'✅' if row['improvement'] > 0 else '❌':<8}")
    
    # 评估改进效果
    if new_acc_10 > old_acc_10:
        print(f"\n🎉 改进成功！±10kWh准确率提升了 {acc_improvement:.1f}%")
        if new_acc_10 >= 70:
            print("✅ 性能已达到可接受水平")
        else:
            print("⚠️ 性能仍需进一步优化")
    else:
        print(f"\n⚠️ 改进效果不明显，准确率变化: {acc_improvement:.1f}%")
    
    return {
        'old_mae': old_mae,
        'new_mae': new_mae,
        'old_acc_10': old_acc_10,
        'new_acc_10': new_acc_10,
        'improvement': acc_improvement
    }

def main():
    """主函数"""
    print("="*60)
    print("测试v7改进版本性能")
    print("="*60)
    
    # 1. 环境检查
    if not check_environment():
        return False
    
    # 2. 加载测试样本
    sample_df = load_test_sample()
    if sample_df is None:
        return False
    
    # 3. 测试改进的v7模型
    v7_model = test_improved_v7()
    if v7_model is None:
        return False
    
    # 4. 运行对比测试
    results_df = run_comparison_test(sample_df, v7_model)
    if len(results_df) == 0:
        print("❌ 对比测试失败")
        return False
    
    # 5. 分析改进效果
    improvement_stats = analyze_improvement(results_df)
    
    # 6. 给出建议
    if improvement_stats and improvement_stats['new_acc_10'] > improvement_stats['old_acc_10']:
        print(f"\n💡 建议:")
        print("  1. 改进版本性能更好，建议使用")
        if improvement_stats['new_acc_10'] >= 70:
            print("  2. 可以进行完整的真实数据验证")
        else:
            print("  2. 需要进一步优化预测公式")
        print("  3. 考虑使用原始训练的集成模型")
    else:
        print(f"\n💡 建议:")
        print("  1. 改进效果有限，需要更深入的优化")
        print("  2. 考虑重新训练模型")
        print("  3. 分析真实数据的特征分布")
    
    return True

if __name__ == "__main__":
    success = main()
