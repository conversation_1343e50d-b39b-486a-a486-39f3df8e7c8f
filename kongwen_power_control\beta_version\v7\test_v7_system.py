#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本副功率预测系统测试脚本
"""

import sys
from pathlib import Path

# 添加v7路径
v7_path = Path(__file__).parent
sys.path.append(str(v7_path))

from model import VicePowerControlModel

def test_v7_system():
    """测试v7系统"""
    print("="*60)
    print("v7版本副功率预测系统测试")
    print("="*60)
    
    # 初始化模型
    model = VicePowerControlModel()
    
    # 显示模型信息
    model_info = model.get_model_info()
    print(f"\n模型信息:")
    print(f"  版本: {model_info['version']}")
    print(f"  架构: {model_info['architecture']}")
    print(f"  模型类型: {model_info['model_type']}")
    print(f"  特征数量: {model_info['features']}")
    print(f"  数据泄露: {'无' if model_info['data_leakage_free'] else '有'}")
    
    # 测试案例
    test_cases = [
        {
            'name': '复投工艺测试',
            'barrelage': 320,
            'last_jialiao_weight': 300,
            'cumulative_feed_weight': 500,
            'expected_process': '复投'
        },
        {
            'name': '首投工艺测试', 
            'barrelage': 150,
            'last_jialiao_weight': 140,
            'cumulative_feed_weight': 50,
            'expected_process': '首投'
        },
        {
            'name': '大重量复投测试',
            'barrelage': 450,
            'last_jialiao_weight': 420,
            'cumulative_feed_weight': 800,
            'expected_process': '复投'
        }
    ]
    
    print(f"\n执行 {len(test_cases)} 个测试案例:")
    print("-" * 60)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}: {case['name']}")
        
        try:
            # 调用预测方法
            main_power, vice_power, vice_info = model.predict(
                t=0,
                ratio=1.0,
                ccd=1400,
                ccd3=1400,
                fullmelting=True,
                sum_jialiao_time=3600,
                last_jialiao_weight=case['last_jialiao_weight'],
                last_Interval_time=600,
                barrelage=case['barrelage'],
                time_interval=600,
                cumulative_feed_weight=case['cumulative_feed_weight']
            )
            
            print(f"  输入参数:")
            print(f"    桶重: {case['barrelage']}kg")
            print(f"    加料重量: {case['last_jialiao_weight']}kg")
            print(f"    累积重量: {case['cumulative_feed_weight']}kg")
            print(f"  预测结果:")
            print(f"    主功率: {main_power}")
            print(f"    副功率: {vice_power}kW")
            print(f"    预测总量: {vice_info.get('predicted_total', 0):.2f}kWh")
            print(f"    累积输出: {vice_info.get('cumulative_output', 0):.2f}kWh")
            print(f"    关闭状态: {vice_info.get('shutdown_status', False)}")
            print(f"    模型版本: {vice_info.get('model_version', 'unknown')}")
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    print(f"\n✅ v7系统测试完成")

if __name__ == "__main__":
    test_v7_system()
