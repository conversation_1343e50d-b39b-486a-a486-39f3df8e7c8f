#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行SVR模型深度测试和重新训练
检查环境、测试现有模型、重新训练并保存结果
"""

import os
import sys
import pandas as pd
import numpy as np
import json
import joblib
from pathlib import Path
from datetime import datetime
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查运行环境"""
    print("="*60)
    print("🔍 环境检查")
    print("="*60)
    
    # 基本环境信息
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    print(f"当前Conda环境: {conda_env}")
    print(f"Python版本: {python_version}")
    
    # 检查scikit-learn版本
    try:
        import sklearn
        sklearn_version = sklearn.__version__
        print(f"scikit-learn版本: {sklearn_version}")
        
        if sklearn_version == "1.0.2":
            print("✅ scikit-learn版本正确: 1.0.2")
            sklearn_status = "CORRECT"
        else:
            print(f"⚠️ scikit-learn版本不是1.0.2，当前为: {sklearn_version}")
            sklearn_status = "DIFFERENT"
    except ImportError:
        print("❌ scikit-learn未安装")
        sklearn_status = "MISSING"
    
    # 检查其他依赖
    packages = {'pandas': None, 'numpy': None, 'joblib': None}
    for package in packages:
        try:
            module = __import__(package)
            packages[package] = getattr(module, '__version__', 'unknown')
            print(f"{package}版本: {packages[package]}")
        except ImportError:
            packages[package] = "NOT_INSTALLED"
            print(f"❌ {package}未安装")
    
    return {
        'conda_env': conda_env,
        'python_version': python_version,
        'sklearn_version': sklearn_version if 'sklearn_version' in locals() else 'unknown',
        'sklearn_status': sklearn_status if 'sklearn_status' in locals() else 'unknown',
        'packages': packages
    }

def load_test_data():
    """加载测试数据"""
    print("\n" + "="*60)
    print("📊 加载测试数据")
    print("="*60)
    
    data_file = Path("output_results/A01_A40_cycles__analysis.csv")
    
    if not data_file.exists():
        print(f"❌ 数据文件不存在: {data_file}")
        return None
    
    try:
        df = pd.read_csv(data_file)
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        
        # 数据清洗
        required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        df_clean = df.dropna(subset=required_cols)
        
        # 过滤异常值
        df_filtered = df_clean[
            (df_clean['weight_difference'] > 0) &
            (df_clean['weight_difference'] < 1000) &
            (df_clean['silicon_thermal_energy_kwh'] > 0) &
            (df_clean['silicon_thermal_energy_kwh'] < 1000) &
            (df_clean['vice_total_energy_kwh'] > 0) &
            (df_clean['vice_total_energy_kwh'] < 2000)
        ]
        
        print(f"📊 清洗后数据: {df_filtered.shape}")
        
        # 随机采样100条
        if len(df_filtered) > 100:
            test_data = df_filtered.sample(n=100, random_state=42)
        else:
            test_data = df_filtered
        
        print(f"📊 测试样本: {len(test_data)} 条")
        
        # 数据统计
        print(f"\n📈 数据统计:")
        print(f"  重量差异: {test_data['weight_difference'].min():.1f} - {test_data['weight_difference'].max():.1f} kg")
        print(f"  硅热能: {test_data['silicon_thermal_energy_kwh'].min():.1f} - {test_data['silicon_thermal_energy_kwh'].max():.1f} kWh")
        print(f"  副功率: {test_data['vice_total_energy_kwh'].min():.1f} - {test_data['vice_total_energy_kwh'].max():.1f} kWh")
        
        return test_data, df_filtered
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def prepare_features(df):
    """准备特征"""
    print("\n🔧 准备特征...")
    
    features_list = []
    
    for _, row in df.iterrows():
        # 基础特征
        base_features = [
            row.get('start_weight', 500),
            row.get('end_weight', 700),
            row.get('weight_difference', 200),
            row.get('end_temperature_celsius', 1450),
            row.get('first_crystal_seeding_main_power_kw', 60),
            row.get('feed_number_1_records', 0),
            row.get('silicon_thermal_energy_kwh', 150),
            row.get('energy_efficiency_percent', 75),
            row.get('record_count', 3000),
            row.get('duration_hours', 3)
        ]
        
        # 工程特征
        weight_diff = row.get('weight_difference', 200)
        silicon_energy = row.get('silicon_thermal_energy_kwh', 150)
        duration = max(row.get('duration_hours', 3), 0.1)
        
        engineered_features = [
            weight_diff ** 2,
            np.sqrt(abs(weight_diff)),
            np.log1p(abs(weight_diff)),
            silicon_energy ** 2,
            np.sqrt(abs(silicon_energy)),
            np.log1p(abs(silicon_energy)),
            duration ** 2,
            np.sqrt(abs(duration)),
            np.log1p(abs(duration)),
            weight_diff * silicon_energy,
            weight_diff * duration,
            weight_diff / duration,
            silicon_energy * duration,
            silicon_energy / duration,
            1 if row.get('feed_type') == '首投' else 0,
            # 额外特征
            row.get('start_weight', 500) / duration,
            row.get('end_temperature_celsius', 1450) / 1000,
            row.get('first_crystal_seeding_main_power_kw', 60) * duration,
            row.get('energy_efficiency_percent', 75) / 100,
            row.get('record_count', 3000) / 1000
        ]
        
        all_features = base_features + engineered_features
        features_list.append(all_features)
    
    feature_matrix = np.array(features_list)
    print(f"✅ 特征准备完成: {feature_matrix.shape}")
    
    return feature_matrix

def test_existing_svr(test_data):
    """测试现有SVR模型"""
    print("\n" + "="*60)
    print("🧪 测试现有SVR模型")
    print("="*60)
    
    # 检查模型文件
    model_path = Path("副功率预测_85.4%准确率_完整项目/models")
    model_files = {
        'model': model_path / "best_model_svr.joblib",
        'scaler': model_path / "scaler.joblib",
        'selector': model_path / "feature_selector.joblib",
        'results': model_path / "results.json"
    }
    
    missing_files = [name for name, file in model_files.items() if not file.exists()]
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return None
    
    try:
        # 加载模型
        model = joblib.load(model_files['model'])
        scaler = joblib.load(model_files['scaler'])
        selector = joblib.load(model_files['selector'])
        
        print(f"✅ SVR模型加载成功")
        print(f"📊 模型类型: {type(model)}")
        
        # 读取性能信息
        with open(model_files['results'], 'r', encoding='utf-8') as f:
            results = json.load(f)
            print(f"📊 报告准确率: {results.get('best_accuracy', 'unknown')}%")
        
        # 准备特征并测试
        X_test = prepare_features(test_data)
        y_test = test_data['vice_total_energy_kwh'].values
        
        predictions = []
        actual_values = []
        failed_count = 0
        
        print(f"📊 开始预测 {len(test_data)} 个样本...")
        
        for i in range(len(X_test)):
            try:
                # 特征选择和标准化
                features_selected = selector.transform([X_test[i]])
                features_scaled = scaler.transform(features_selected)
                
                # 预测
                pred = model.predict(features_scaled)[0]
                predictions.append(pred)
                actual_values.append(y_test[i])
                
            except Exception as e:
                failed_count += 1
                continue
        
        if len(predictions) == 0:
            print("❌ 所有预测都失败了")
            return None
        
        # 计算评估指标
        predictions = np.array(predictions)
        actual_values = np.array(actual_values)
        
        errors = np.abs(predictions - actual_values)
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean((predictions - actual_values) ** 2))
        
        # 准确率指标
        acc_5 = (errors <= 5).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        test_results = {
            'successful_predictions': len(predictions),
            'failed_predictions': failed_count,
            'mae': float(mae),
            'rmse': float(rmse),
            'accuracy_5kwh': float(acc_5),
            'accuracy_10kwh': float(acc_10),
            'accuracy_15kwh': float(acc_15),
            'predictions': predictions.tolist(),
            'actual_values': actual_values.tolist(),
            'errors': errors.tolist()
        }
        
        print(f"\n📊 现有SVR模型测试结果:")
        print(f"  成功预测: {test_results['successful_predictions']} / {len(test_data)}")
        print(f"  失败预测: {test_results['failed_predictions']}")
        print(f"  ±5kWh准确率: {test_results['accuracy_5kwh']:.2f}%")
        print(f"  ±10kWh准确率: {test_results['accuracy_10kwh']:.2f}%")
        print(f"  ±15kWh准确率: {test_results['accuracy_15kwh']:.2f}%")
        print(f"  MAE: {test_results['mae']:.2f} kWh")
        print(f"  RMSE: {test_results['rmse']:.2f} kWh")
        
        return test_results
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return None

def train_new_svr(df):
    """训练新的SVR模型"""
    print("\n" + "="*60)
    print("🚀 训练新的SVR模型")
    print("="*60)
    
    try:
        # 准备数据
        X = prepare_features(df)
        y = df['vice_total_energy_kwh'].values
        
        print(f"📊 训练数据: {X.shape}")
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        print(f"📊 训练集: {X_train.shape}")
        print(f"📊 测试集: {X_test.shape}")
        
        # 特征选择
        print("🔧 特征选择...")
        selector = SelectKBest(score_func=f_regression, k=20)
        X_train_selected = selector.fit_transform(X_train, y_train)
        X_test_selected = selector.transform(X_test)
        
        # 标准化
        print("🔧 数据标准化...")
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        # 训练SVR模型
        print("🔧 训练SVR模型...")
        svr = SVR(kernel='rbf', C=100, gamma='scale', epsilon=0.1)
        svr.fit(X_train_scaled, y_train)
        
        # 预测和评估
        y_pred = svr.predict(X_test_scaled)
        
        errors = np.abs(y_pred - y_test)
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean((y_pred - y_test) ** 2))
        r2 = r2_score(y_test, y_pred)
        acc_5 = (errors <= 5).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        training_results = {
            'mae': float(mae),
            'rmse': float(rmse),
            'r2_score': float(r2),
            'accuracy_5kwh': float(acc_5),
            'accuracy_10kwh': float(acc_10),
            'accuracy_15kwh': float(acc_15),
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'selected_features': int(X_train_selected.shape[1]),
            'predictions': y_pred.tolist(),
            'actual_values': y_test.tolist(),
            'errors': errors.tolist()
        }
        
        print(f"\n📊 新SVR模型训练结果:")
        print(f"  ±5kWh准确率: {training_results['accuracy_5kwh']:.2f}%")
        print(f"  ±10kWh准确率: {training_results['accuracy_10kwh']:.2f}%")
        print(f"  ±15kWh准确率: {training_results['accuracy_15kwh']:.2f}%")
        print(f"  MAE: {training_results['mae']:.2f} kWh")
        print(f"  RMSE: {training_results['rmse']:.2f} kWh")
        print(f"  R²: {training_results['r2_score']:.4f}")
        
        # 保存新模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = Path(f"retrained_svr_model_{timestamp}")
        model_dir.mkdir(exist_ok=True)
        
        joblib.dump(svr, model_dir / "best_model_svr.joblib")
        joblib.dump(scaler, model_dir / "scaler.joblib")
        joblib.dump(selector, model_dir / "feature_selector.joblib")
        
        # 保存训练信息
        model_info = {
            'timestamp': timestamp,
            'environment': os.environ.get('CONDA_DEFAULT_ENV', 'unknown'),
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'sklearn_version': __import__('sklearn').__version__,
            'model_type': 'SVR',
            'training_results': training_results
        }
        
        with open(model_dir / "model_info.json", 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        print(f"💾 新模型已保存到: {model_dir}")
        
        return training_results, model_dir
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return None, None

def main():
    """主函数"""
    print("="*60)
    print("🧪 SVR模型深度测试和重新训练执行")
    print("="*60)
    
    # 1. 环境检查
    env_info = check_environment()
    
    # 2. 加载数据
    data_result = load_test_data()
    if data_result is None:
        print("\n❌ 数据加载失败，程序终止")
        return
    
    test_data, full_data = data_result
    
    # 3. 测试现有模型
    existing_results = test_existing_svr(test_data)
    
    # 4. 训练新模型
    new_results, model_dir = train_new_svr(full_data)
    
    # 5. 生成最终报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    final_report = {
        'execution_timestamp': timestamp,
        'environment_info': env_info,
        'test_data_info': {
            'test_samples': len(test_data),
            'total_samples': len(full_data),
            'data_file': 'output_results/A01_A40_cycles__analysis.csv'
        },
        'existing_model_results': existing_results,
        'new_model_results': new_results,
        'new_model_directory': str(model_dir) if model_dir else None,
        'comparison': {}
    }
    
    # 性能对比
    if existing_results and new_results:
        final_report['comparison'] = {
            'accuracy_10kwh_improvement': new_results['accuracy_10kwh'] - existing_results['accuracy_10kwh'],
            'mae_improvement': existing_results['mae'] - new_results['mae'],
            'existing_accuracy_10kwh': existing_results['accuracy_10kwh'],
            'new_accuracy_10kwh': new_results['accuracy_10kwh']
        }
    
    # 保存最终报告
    report_file = f"svr_execution_report_{timestamp}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(final_report, f, indent=2, ensure_ascii=False)
    
    # 保存测试数据
    test_data.to_csv(f"test_data_{timestamp}.csv", index=False, encoding='utf-8')
    
    print("\n" + "="*60)
    print("📊 执行完成")
    print("="*60)
    
    print(f"环境信息: {env_info['conda_env']} (Python {env_info['python_version']})")
    print(f"scikit-learn版本: {env_info['sklearn_version']} ({'✅ 1.0.2' if env_info['sklearn_status'] == 'CORRECT' else '⚠️ 非1.0.2'})")
    
    if existing_results:
        print(f"\n现有模型测试结果:")
        print(f"  ±10kWh准确率: {existing_results['accuracy_10kwh']:.2f}%")
        print(f"  MAE: {existing_results['mae']:.2f} kWh")
    
    if new_results:
        print(f"\n新模型训练结果:")
        print(f"  ±10kWh准确率: {new_results['accuracy_10kwh']:.2f}%")
        print(f"  MAE: {new_results['mae']:.2f} kWh")
        print(f"  模型保存位置: {model_dir}")
    
    if existing_results and new_results:
        improvement = new_results['accuracy_10kwh'] - existing_results['accuracy_10kwh']
        print(f"\n📈 性能改进: {improvement:+.2f}%")
    
    print(f"\n📄 详细报告: {report_file}")
    print(f"📊 测试数据: test_data_{timestamp}.csv")
    print("✅ SVR深度测试执行完成！")

if __name__ == "__main__":
    main()
