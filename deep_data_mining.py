#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度数据挖掘分析脚本 - 提高副功率预测准确率
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.feature_selection import mutual_info_regression
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False

class DeepDataMiner:
    """深度数据挖掘分析器"""
    
    def __init__(self, data_path):
        """初始化"""
        self.df = pd.read_csv(data_path)
        self.prepare_features()
        
    def prepare_features(self):
        """准备特征数据"""
        print("准备特征数据...")
        
        # 基础特征
        self.df['energy_density'] = self.df['silicon_thermal_energy_kwh'] / (self.df['weight_difference'] + 1e-6)
        self.df['energy_weight_ratio'] = self.df['silicon_thermal_energy_kwh'] / (self.df['weight_difference'] + 1e-6)
        self.df['weight_energy_product'] = self.df['weight_difference'] * self.df['silicon_thermal_energy_kwh']
        self.df['efficiency_ratio'] = self.df['vice_total_energy_kwh'] / (self.df['silicon_thermal_energy_kwh'] + 1e-6)
        
        # 时间特征
        self.df['start_time'] = pd.to_datetime(self.df['start_time'])
        self.df['hour'] = self.df['start_time'].dt.hour
        self.df['day_of_week'] = self.df['start_time'].dt.dayofweek
        self.df['month'] = self.df['start_time'].dt.month
        
        # 温度相关特征
        self.df['temp_deviation'] = abs(self.df['end_temperature_celsius'] - 1448)
        self.df['temp_category'] = pd.cut(self.df['end_temperature_celsius'], 
                                         bins=[0, 1440, 1450, 1460, 2000], 
                                         labels=['低温', '正常', '高温', '超高温'])
        
        # 功率相关特征
        self.df['power_efficiency'] = self.df['vice_total_energy_kwh'] / (self.df['first_crystal_seeding_main_power_kw'] * self.df['duration_hours'] + 1e-6)
        self.df['power_density'] = self.df['first_crystal_seeding_main_power_kw'] / (self.df['weight_difference'] + 1e-6)
        
        print(f"特征准备完成，数据形状: {self.df.shape}")
    
    def analyze_data_distribution(self):
        """分析数据分布"""
        print("\n" + "="*60)
        print("1. 数据分布分析")
        print("="*60)
        
        # 按工艺类型分析
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 副功率分布
        for i, feed_type in enumerate(['首投', '复投']):
            subset = self.df[self.df['feed_type'] == feed_type]
            axes[i, 0].hist(subset['vice_total_energy_kwh'], bins=30, alpha=0.7, label=feed_type)
            axes[i, 0].set_title(f'{feed_type} - 副功率分布')
            axes[i, 0].set_xlabel('副功率 (kWh)')
            axes[i, 0].set_ylabel('频次')
            
            # 重量差异分布
            axes[i, 1].hist(subset['weight_difference'], bins=30, alpha=0.7, label=feed_type)
            axes[i, 1].set_title(f'{feed_type} - 重量差异分布')
            axes[i, 1].set_xlabel('重量差异 (kg)')
            axes[i, 1].set_ylabel('频次')
            
            # 效率比分布
            axes[i, 2].hist(subset['efficiency_ratio'], bins=30, alpha=0.7, label=feed_type)
            axes[i, 2].set_title(f'{feed_type} - 效率比分布')
            axes[i, 2].set_xlabel('效率比')
            axes[i, 2].set_ylabel('频次')
        
        plt.tight_layout()
        plt.savefig('data_distribution_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 统计分析
        print("\n按工艺类型统计分析:")
        for feed_type in ['首投', '复投']:
            subset = self.df[self.df['feed_type'] == feed_type]
            print(f"\n{feed_type}工艺 (样本数: {len(subset)}):")
            print(f"  副功率: 均值={subset['vice_total_energy_kwh'].mean():.2f}, 标准差={subset['vice_total_energy_kwh'].std():.2f}")
            print(f"  重量差异: 均值={subset['weight_difference'].mean():.2f}, 标准差={subset['weight_difference'].std():.2f}")
            print(f"  效率比: 均值={subset['efficiency_ratio'].mean():.2f}, 标准差={subset['efficiency_ratio'].std():.2f}")
            print(f"  持续时间: 均值={subset['duration_hours'].mean():.2f}, 标准差={subset['duration_hours'].std():.2f}")
    
    def analyze_feature_importance(self):
        """分析特征重要性"""
        print("\n" + "="*60)
        print("2. 特征重要性分析")
        print("="*60)
        
        # 选择数值特征
        numeric_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'end_temperature_celsius',
                           'first_crystal_seeding_main_power_kw', 'duration_hours', 'energy_density',
                           'energy_weight_ratio', 'weight_energy_product', 'efficiency_ratio',
                           'temp_deviation', 'power_efficiency', 'power_density', 'hour', 'day_of_week', 'month']
        
        X = self.df[numeric_features].fillna(0)
        y = self.df['vice_total_energy_kwh']
        
        # 随机森林特征重要性
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X, y)
        
        feature_importance = pd.DataFrame({
            'feature': numeric_features,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\n随机森林特征重要性排序:")
        for idx, row in feature_importance.head(10).iterrows():
            print(f"{row['feature']}: {row['importance']:.4f}")
        
        # 互信息特征重要性
        mi_scores = mutual_info_regression(X, y, random_state=42)
        mi_importance = pd.DataFrame({
            'feature': numeric_features,
            'mi_score': mi_scores
        }).sort_values('mi_score', ascending=False)
        
        print("\n互信息特征重要性排序:")
        for idx, row in mi_importance.head(10).iterrows():
            print(f"{row['feature']}: {row['mi_score']:.4f}")
        
        return feature_importance, mi_importance
    
    def analyze_correlations(self):
        """分析特征相关性"""
        print("\n" + "="*60)
        print("3. 特征相关性分析")
        print("="*60)
        
        # 选择关键特征
        key_features = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh',
                       'end_temperature_celsius', 'first_crystal_seeding_main_power_kw', 'duration_hours',
                       'energy_density', 'efficiency_ratio', 'power_efficiency']
        
        corr_matrix = self.df[key_features].corr()
        
        # 绘制相关性热图
        plt.figure(figsize=(12, 10))
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                   square=True, linewidths=0.5)
        plt.title('特征相关性热图')
        plt.tight_layout()
        plt.savefig('correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 找出高相关性特征对
        print("\n高相关性特征对 (|r| > 0.8):")
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_val = corr_matrix.iloc[i, j]
                if abs(corr_val) > 0.8:
                    print(f"{corr_matrix.columns[i]} - {corr_matrix.columns[j]}: {corr_val:.3f}")
        
        return corr_matrix
    
    def identify_outliers(self):
        """识别异常值"""
        print("\n" + "="*60)
        print("4. 异常值识别")
        print("="*60)
        
        # 使用IQR方法识别异常值
        outliers_info = {}
        
        for feature in ['vice_total_energy_kwh', 'weight_difference', 'silicon_thermal_energy_kwh', 'efficiency_ratio']:
            Q1 = self.df[feature].quantile(0.25)
            Q3 = self.df[feature].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = self.df[(self.df[feature] < lower_bound) | (self.df[feature] > upper_bound)]
            outliers_info[feature] = {
                'count': len(outliers),
                'percentage': len(outliers) / len(self.df) * 100,
                'lower_bound': lower_bound,
                'upper_bound': upper_bound
            }
            
            print(f"\n{feature}:")
            print(f"  异常值数量: {len(outliers)} ({len(outliers)/len(self.df)*100:.1f}%)")
            print(f"  正常范围: [{lower_bound:.2f}, {upper_bound:.2f}]")
        
        return outliers_info
    
    def cluster_analysis(self):
        """聚类分析"""
        print("\n" + "="*60)
        print("5. 聚类分析")
        print("="*60)
        
        # 选择特征进行聚类
        features_for_clustering = ['weight_difference', 'silicon_thermal_energy_kwh', 'efficiency_ratio', 'duration_hours']
        X = self.df[features_for_clustering].fillna(0)
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=4, random_state=42)
        clusters = kmeans.fit_predict(X_scaled)
        self.df['cluster'] = clusters
        
        # 分析每个聚类的特征
        print("\n聚类分析结果:")
        for cluster_id in range(4):
            cluster_data = self.df[self.df['cluster'] == cluster_id]
            print(f"\n聚类 {cluster_id} (样本数: {len(cluster_data)}):")
            print(f"  副功率均值: {cluster_data['vice_total_energy_kwh'].mean():.2f} kWh")
            print(f"  重量差异均值: {cluster_data['weight_difference'].mean():.2f} kg")
            print(f"  效率比均值: {cluster_data['efficiency_ratio'].mean():.2f}")
            print(f"  首投比例: {(cluster_data['feed_type'] == '首投').mean()*100:.1f}%")
        
        return clusters
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("开始深度数据挖掘分析...")
        
        # 1. 数据分布分析
        self.analyze_data_distribution()
        
        # 2. 特征重要性分析
        rf_importance, mi_importance = self.analyze_feature_importance()
        
        # 3. 相关性分析
        corr_matrix = self.analyze_correlations()
        
        # 4. 异常值识别
        outliers_info = self.identify_outliers()
        
        # 5. 聚类分析
        clusters = self.cluster_analysis()
        
        print("\n" + "="*60)
        print("数据挖掘分析完成！")
        print("="*60)
        
        return {
            'rf_importance': rf_importance,
            'mi_importance': mi_importance,
            'corr_matrix': corr_matrix,
            'outliers_info': outliers_info,
            'clusters': clusters
        }

if __name__ == "__main__":
    # 运行深度数据挖掘分析
    miner = DeepDataMiner('output_results/A01_A40_cycles__analysis.csv')
    results = miner.run_complete_analysis()
