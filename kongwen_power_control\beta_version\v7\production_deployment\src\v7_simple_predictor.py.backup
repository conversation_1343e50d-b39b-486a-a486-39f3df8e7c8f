#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v7版本简化预测器 - 避免依赖问题
"""

import pandas as pd
import numpy as np
from pathlib import Path
from v7_feature_engineer import V7FeatureEngineer

class V7SimplePredictor:
    """v7版本简化预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.feature_engineer = V7FeatureEngineer()
        print("✅ v7简化预测器初始化完成")
    
    def predict(self, weight_difference, silicon_thermal_energy_kwh, feed_type, 
                folder_name=None, start_time=None):
        """
        预测副功率 - 使用简化的经验公式
        
        参数:
            weight_difference: 重量差异 (kg)
            silicon_thermal_energy_kwh: 硅热能需求 (kWh)
            feed_type: 工艺类型 ('首投' 或 '复投')
            folder_name: 设备名称 (可选)
            start_time: 开始时间 (可选)
        
        返回:
            dict: 包含预测结果和置信度
        """
        
        try:
            # 创建输入数据
            input_data = pd.DataFrame({
                'weight_difference': [weight_difference],
                'silicon_thermal_energy_kwh': [silicon_thermal_energy_kwh],
                'feed_type': [feed_type],
                'folder_name': [folder_name or 'analoga01'],
                'start_time': [start_time or pd.Timestamp.now()],
                'vice_total_energy_kwh': [0]  # 占位符
            })
            
            # 特征工程
            X = self.feature_engineer.create_realtime_features(input_data)
            
            # 使用简化的经验公式进行预测
            predicted_power = self._simplified_prediction(X.iloc[0])
            
            # 计算置信度
            confidence = self._calculate_confidence(weight_difference, 
                                                   silicon_thermal_energy_kwh, 
                                                   feed_type)
            
            return {
                'predicted_vice_power': round(predicted_power, 2),
                'confidence': confidence,
                'input_features': {
                    'weight_difference': weight_difference,
                    'silicon_thermal_energy_kwh': silicon_thermal_energy_kwh,
                    'feed_type': feed_type
                },
                'model_type': 'simplified_v7'
            }
            
        except Exception as e:
            print(f"⚠️ 预测失败: {e}")
            # 返回基础经验公式结果
            fallback_power = weight_difference * 0.8 + silicon_thermal_energy_kwh * 0.6
            return {
                'predicted_vice_power': round(fallback_power, 2),
                'confidence': 'Low',
                'error': str(e),
                'model_type': 'fallback'
            }
    
    def _simplified_prediction(self, features):
        """简化的预测公式，基于特征工程结果"""
        
        # 基于训练结果的经验权重
        weights = {
            'log_weight': 0.133,
            'sqrt_weight': 0.125,
            'vice_power_estimate_v1': 0.117,
            'log_energy': 0.111,
            'sqrt_energy': 0.111,
            'weight_diff': 0.089,
            'silicon_energy': 0.087,
            'energy_density': 0.065,
            'melting_energy_ratio': 0.054,
            'thermal_efficiency_est': 0.048
        }
        
        # 计算加权预测
        prediction = 0
        total_weight = 0
        
        for feature, weight in weights.items():
            if feature in features.index:
                prediction += features[feature] * weight
                total_weight += weight
        
        # 标准化
        if total_weight > 0:
            prediction = prediction / total_weight * 300  # 缩放到合理范围
        
        # 工艺类型调整
        if features.get('is_first_cast', 0) == 1:
            prediction *= 0.85  # 首投通常功率较低
        
        # 确保预测值在合理范围内
        prediction = max(50, min(800, prediction))
        
        return prediction
    
    def _calculate_confidence(self, weight_diff, silicon_energy, feed_type):
        """计算预测置信度"""
        confidence = "High"
        
        # 检查输入范围
        if weight_diff < 50 or weight_diff > 800:
            confidence = "Low"
        elif silicon_energy < 50 or silicon_energy > 800:
            confidence = "Low"
        elif feed_type == '首投' and weight_diff > 500:
            confidence = "Medium"  # 首投大重量样本较少
        elif abs(weight_diff - silicon_energy) > 200:
            confidence = "Medium"  # 重量和能量差异过大
        
        return confidence

# 为了兼容性，创建别名
RealtimePredictor = V7SimplePredictor
