#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
v6和v7版本副功率预测系统对比测试
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def load_v6_model():
    """加载v6模型"""
    try:
        v6_path = Path("kongwen_power_control/beta_version/v6")
        sys.path.insert(0, str(v6_path))
        
        from model import KongwenGonglvCorrectionModel as V6Model
        v6_model = V6Model()
        print("✅ v6模型加载成功")
        return v6_model
    except Exception as e:
        print(f"❌ v6模型加载失败: {e}")
        return None

def load_v7_model():
    """加载v7模型"""
    try:
        v7_path = Path("kongwen_power_control/beta_version/v7")
        sys.path.insert(0, str(v7_path))
        
        # 清除之前的导入
        if 'model' in sys.modules:
            del sys.modules['model']
        
        from model import VicePowerControlModel as V7Model
        v7_model = V7Model()
        print("✅ v7模型加载成功")
        return v7_model
    except Exception as e:
        print(f"❌ v7模型加载失败: {e}")
        return None

def compare_models():
    """对比v6和v7模型"""
    print("="*60)
    print("v6 vs v7 副功率预测系统对比测试")
    print("="*60)
    
    # 环境检查
    if not check_environment():
        return
    
    # 加载模型
    print(f"\n加载模型...")
    v6_model = load_v6_model()
    v7_model = load_v7_model()
    
    if v6_model is None or v7_model is None:
        print("❌ 模型加载失败，无法进行对比")
        return
    
    # 测试案例
    test_cases = [
        {
            'name': '中等重量复投',
            'params': {
                't': 0,
                'ratio': 1.0,
                'ccd': 1400,
                'ccd3': 1400,
                'fullmelting': True,
                'sum_jialiao_time': 3600,
                'last_jialiao_time': 1800,
                'last_jialiao_weight': 300,
                'last_Interval_time': 600,
                'barrelage': 320,
                'last_but_one_jialiao_weight': 280,
                'last_but_one_jialiao_time': 1200,
                'last_but_one_jialiao_interval_time': 600,
                'film_ratio': 5.0,
                'turnover_ratio': 10.0,
                'time_interval': 600,
                'cumulative_feed_weight': 500
            },
            'v7_params': {
                't': 0,
                'ratio': 1.0,
                'ccd': 1400,
                'ccd3': 1400,
                'fullmelting': True,
                'sum_jialiao_time': 3600,
                'last_jialiao_weight': 300,
                'last_Interval_time': 600,
                'barrelage': 320,
                'time_interval': 600,
                'cumulative_feed_weight': 500
            }
        },
        {
            'name': '轻量首投',
            'params': {
                't': 0,
                'ratio': 1.0,
                'ccd': 1400,
                'ccd3': 1400,
                'fullmelting': True,
                'sum_jialiao_time': 3600,
                'last_jialiao_time': 1800,
                'last_jialiao_weight': 140,
                'last_Interval_time': 600,
                'barrelage': 150,
                'last_but_one_jialiao_weight': 0,
                'last_but_one_jialiao_time': 0,
                'last_but_one_jialiao_interval_time': 0,
                'film_ratio': 5.0,
                'turnover_ratio': 10.0,
                'time_interval': 600,
                'cumulative_feed_weight': 50
            },
            'v7_params': {
                't': 0,
                'ratio': 1.0,
                'ccd': 1400,
                'ccd3': 1400,
                'fullmelting': True,
                'sum_jialiao_time': 3600,
                'last_jialiao_weight': 140,
                'last_Interval_time': 600,
                'barrelage': 150,
                'time_interval': 600,
                'cumulative_feed_weight': 50
            }
        },
        {
            'name': '大重量复投',
            'params': {
                't': 0,
                'ratio': 1.0,
                'ccd': 1400,
                'ccd3': 1400,
                'fullmelting': True,
                'sum_jialiao_time': 3600,
                'last_jialiao_time': 1800,
                'last_jialiao_weight': 420,
                'last_Interval_time': 600,
                'barrelage': 450,
                'last_but_one_jialiao_weight': 400,
                'last_but_one_jialiao_time': 1200,
                'last_but_one_jialiao_interval_time': 600,
                'film_ratio': 5.0,
                'turnover_ratio': 10.0,
                'time_interval': 600,
                'cumulative_feed_weight': 800
            },
            'v7_params': {
                't': 0,
                'ratio': 1.0,
                'ccd': 1400,
                'ccd3': 1400,
                'fullmelting': True,
                'sum_jialiao_time': 3600,
                'last_jialiao_weight': 420,
                'last_Interval_time': 600,
                'barrelage': 450,
                'time_interval': 600,
                'cumulative_feed_weight': 800
            }
        }
    ]
    
    # 对比结果
    comparison_results = []
    
    print(f"\n执行 {len(test_cases)} 个对比测试:")
    print("="*60)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}: {case['name']}")
        print("-" * 40)
        
        # 重置模型状态
        v6_model.reset_vice_power_state() if hasattr(v6_model, 'reset_vice_power_state') else None
        v7_model.reset_vice_power_state() if hasattr(v7_model, 'reset_vice_power_state') else None
        
        try:
            # v6预测
            v6_main, v6_vice, v6_info = v6_model.predict(**case['params'])

            # v7预测 (使用简化参数)
            v7_params = case.get('v7_params', case['params'])
            v7_main, v7_vice, v7_info = v7_model.predict(**v7_params)
            
            # 显示结果
            print(f"输入参数:")
            print(f"  桶重: {case['params']['barrelage']}kg")
            print(f"  加料重量: {case['params']['last_jialiao_weight']}kg")
            print(f"  累积重量: {case['params']['cumulative_feed_weight']}kg")
            
            print(f"\nv6结果:")
            print(f"  主功率: {v6_main}")
            print(f"  副功率: {v6_vice}kW")
            print(f"  预测总量: {v6_info.get('predicted_total', 'N/A')}")
            print(f"  模型版本: {v6_info.get('model_version', 'v6.x')}")
            
            print(f"\nv7结果:")
            print(f"  主功率: {v7_main}")
            print(f"  副功率: {v7_vice}kW")
            print(f"  预测总量: {v7_info.get('predicted_total', 'N/A')}")
            print(f"  模型版本: {v7_info.get('model_version', 'v7.0')}")
            
            # 计算差异
            vice_diff = abs(v7_vice - v6_vice) if v6_vice != 'N/A' and v7_vice != 'N/A' else 'N/A'
            total_diff = 'N/A'
            
            if (v6_info.get('predicted_total') is not None and 
                v7_info.get('predicted_total') is not None):
                try:
                    v6_total = float(v6_info['predicted_total'])
                    v7_total = float(v7_info['predicted_total'])
                    total_diff = abs(v7_total - v6_total)
                except:
                    total_diff = 'N/A'
            
            print(f"\n差异分析:")
            print(f"  副功率差异: {vice_diff}")
            print(f"  预测总量差异: {total_diff}")
            
            # 记录结果
            comparison_results.append({
                'case': case['name'],
                'v6_vice': v6_vice,
                'v7_vice': v7_vice,
                'v6_total': v6_info.get('predicted_total', 'N/A'),
                'v7_total': v7_info.get('predicted_total', 'N/A'),
                'vice_diff': vice_diff,
                'total_diff': total_diff
            })
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            comparison_results.append({
                'case': case['name'],
                'error': str(e)
            })
    
    # 生成对比总结
    print(f"\n" + "="*60)
    print("对比总结")
    print("="*60)
    
    # 创建对比表格
    print(f"\n{'测试案例':<15} {'v6副功率':<10} {'v7副功率':<10} {'副功率差异':<12} {'v6总量':<12} {'v7总量':<12}")
    print("-" * 80)
    
    for result in comparison_results:
        if 'error' not in result:
            print(f"{result['case']:<15} {str(result['v6_vice']):<10} {str(result['v7_vice']):<10} "
                  f"{str(result['vice_diff']):<12} {str(result['v6_total'])[:10]:<12} {str(result['v7_total'])[:10]:<12}")
    
    # 架构对比
    print(f"\n架构对比:")
    print(f"v6架构: 双模型系统（标准模型 + 高功率模型）")
    print(f"v7架构: 单模型系统（集成学习模型）")
    print(f"v6特征: 47个特征（可能存在数据泄露）")
    print(f"v7特征: 26个实时特征（无数据泄露）")
    print(f"v6性能: 理论100%准确率（虚假）")
    print(f"v7性能: 71.3%准确率（真实）")
    
    # 保存对比结果
    results_df = pd.DataFrame(comparison_results)
    results_file = f"v6_v7_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    results_df.to_csv(results_file, index=False, encoding='utf-8')
    print(f"\n✅ 对比结果已保存到: {results_file}")
    
    print(f"\n🎉 v6 vs v7 对比测试完成！")

if __name__ == "__main__":
    compare_models()
