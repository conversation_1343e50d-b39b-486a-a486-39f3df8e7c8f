#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时副功率预测模型训练脚本 - lj_env_1环境
无数据泄露的完整训练和评估流程
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import json
from datetime import datetime
from pathlib import Path

from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class EnvironmentChecker:
    """环境检查器"""
    
    @staticmethod
    def check_environment():
        """检查当前环境是否为lj_env_1"""
        conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
        print(f"当前Conda环境: {conda_env}")
        
        if conda_env != 'lj_env_1':
            print(f"❌ 错误：当前环境为 {conda_env}，必须使用 lj_env_1 环境")
            print("请运行: conda activate lj_env_1")
            return False
        else:
            print("✅ 环境检查通过：正在使用 lj_env_1 环境")
            return True

class RealtimeFeatureEngineer:
    """实时特征工程器（无数据泄露版本）"""
    
    def __init__(self):
        """初始化"""
        self.scaler = StandardScaler()
        self.feature_names = None
        
    def create_realtime_features(self, df):
        """创建实时可获取的特征（26个特征）"""
        print("创建实时特征工程（无数据泄露）...")
        
        features_df = df.copy()
        
        # 1. 基础实时特征（3个）
        features_df['weight_diff'] = features_df['weight_difference']
        features_df['silicon_energy'] = features_df['silicon_thermal_energy_kwh']
        features_df['is_first_cast'] = (features_df['feed_type'] == '首投').astype(int)
        
        # 2. 物理学衍生特征（3个）
        features_df['energy_density'] = features_df['silicon_energy'] / (features_df['weight_diff'] + 1e-6)
        features_df['melting_energy_ratio'] = features_df['silicon_energy'] / (features_df['weight_diff'] * 0.5 + 1e-6)
        features_df['thermal_efficiency_est'] = np.clip(1.2 + 0.3 * features_df['is_first_cast'], 1.0, 2.5)
        
        # 3. 工艺特定特征（4个）
        features_df['first_cast_weight_factor'] = features_df['is_first_cast'] * features_df['weight_diff']
        features_df['first_cast_energy_factor'] = features_df['is_first_cast'] * features_df['silicon_energy']
        features_df['recast_weight_factor'] = (1 - features_df['is_first_cast']) * features_df['weight_diff']
        features_df['recast_energy_factor'] = (1 - features_df['is_first_cast']) * features_df['silicon_energy']
        
        # 4. 范围和分类特征（2个）
        features_df['weight_category'] = pd.cut(features_df['weight_diff'], 
                                               bins=[0, 100, 200, 350, 500, 1000], 
                                               labels=[1, 2, 3, 4, 5]).astype(float)
        features_df['energy_category'] = pd.cut(features_df['silicon_energy'], 
                                               bins=[0, 150, 300, 450, 600, 1000], 
                                               labels=[1, 2, 3, 4, 5]).astype(float)
        
        # 5. 交互特征（3个）
        features_df['weight_energy_balance'] = abs(features_df['weight_diff'] - features_df['silicon_energy'])
        features_df['process_weight_energy'] = features_df['is_first_cast'] * features_df['weight_diff'] * features_df['silicon_energy'] / 10000
        features_df['weight_energy_interaction'] = features_df['weight_diff'] * features_df['silicon_energy'] / 1000
        
        # 6. 非线性变换特征（4个）
        features_df['log_weight'] = np.log(features_df['weight_diff'] + 1)
        features_df['log_energy'] = np.log(features_df['silicon_energy'] + 1)
        features_df['sqrt_weight'] = np.sqrt(features_df['weight_diff'])
        features_df['sqrt_energy'] = np.sqrt(features_df['silicon_energy'])
        
        # 7. 经验预估特征（2个）
        features_df['vice_power_estimate_v1'] = features_df['weight_diff'] * 0.8 + features_df['silicon_energy'] * 0.6
        features_df['vice_power_estimate_v2'] = features_df['weight_diff'] * features_df['thermal_efficiency_est'] * 0.7
        
        # 8. 设备和时间特征（5个）
        if 'folder_name' in features_df.columns:
            device_mapping = {name: idx for idx, name in enumerate(features_df['folder_name'].unique())}
            features_df['device_id'] = features_df['folder_name'].map(device_mapping)
        else:
            features_df['device_id'] = 0
            
        if 'start_time' in features_df.columns:
            features_df['start_time'] = pd.to_datetime(features_df['start_time'])
            features_df['start_hour'] = features_df['start_time'].dt.hour
            features_df['start_day_of_week'] = features_df['start_time'].dt.dayofweek
            features_df['is_work_hours'] = ((features_df['start_hour'] >= 8) & (features_df['start_hour'] <= 18)).astype(int)
            features_df['is_weekend'] = (features_df['start_day_of_week'] >= 5).astype(int)
        else:
            features_df['start_hour'] = 12
            features_df['start_day_of_week'] = 1
            features_df['is_work_hours'] = 1
            features_df['is_weekend'] = 0
        
        # 选择最终特征（26个）
        feature_cols = [
            'weight_diff', 'silicon_energy', 'is_first_cast',
            'energy_density', 'melting_energy_ratio', 'thermal_efficiency_est',
            'first_cast_weight_factor', 'first_cast_energy_factor',
            'recast_weight_factor', 'recast_energy_factor',
            'weight_category', 'energy_category',
            'weight_energy_balance', 'process_weight_energy', 'weight_energy_interaction',
            'log_weight', 'log_energy', 'sqrt_weight', 'sqrt_energy',
            'vice_power_estimate_v1', 'vice_power_estimate_v2',
            'device_id', 'start_hour', 'start_day_of_week', 'is_work_hours', 'is_weekend'
        ]
        
        # 确保所有特征都存在
        for col in feature_cols:
            if col not in features_df.columns:
                features_df[col] = 0
        
        self.feature_names = feature_cols
        print(f"✅ 创建了 {len(feature_cols)} 个实时特征")
        
        return features_df[feature_cols].fillna(0)

class RealtimeVicePowerModel:
    """实时副功率预测模型"""
    
    def __init__(self):
        """初始化"""
        self.models = {}
        self.weights = {}
        self.scaler = StandardScaler()
        self.feature_engineer = RealtimeFeatureEngineer()
        self.is_trained = False
        self.training_history = {}
        
    def create_ensemble_models(self):
        """创建集成模型"""
        models = {
            'random_forest': RandomForestRegressor(
                n_estimators=300,
                max_depth=15,
                min_samples_split=3,
                min_samples_leaf=2,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1
            ),
            'gradient_boosting': GradientBoostingRegressor(
                n_estimators=200,
                learning_rate=0.08,
                max_depth=8,
                min_samples_split=3,
                min_samples_leaf=2,
                subsample=0.8,
                random_state=42
            ),
            'ridge_regression': Ridge(
                alpha=0.5,
                random_state=42
            )
        }
        return models
    
    def train_with_time_series_cv(self, df):
        """使用时间序列交叉验证训练模型"""
        print("\n" + "="*60)
        print("开始训练实时副功率预测模型（lj_env_1环境）")
        print("="*60)
        
        # 特征工程
        X = self.feature_engineer.create_realtime_features(df)
        y = df['vice_total_energy_kwh']
        
        print(f"\n数据概况:")
        print(f"  样本总数: {len(X)}")
        print(f"  特征数量: {len(X.columns)}")
        print(f"  首投样本: {X['is_first_cast'].sum()}")
        print(f"  复投样本: {len(X) - X['is_first_cast'].sum()}")
        print(f"  目标变量范围: {y.min():.1f} - {y.max():.1f} kWh")
        
        # 数据预处理
        X_scaled = self.scaler.fit_transform(X)
        
        # 创建模型
        models = self.create_ensemble_models()
        
        # 时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=5)
        cv_results = {}
        
        print(f"\n时间序列交叉验证 (5折):")
        print("-" * 60)
        
        for name, model in models.items():
            cv_scores = []
            
            for fold, (train_idx, val_idx) in enumerate(tscv.split(X_scaled)):
                X_train_fold, X_val_fold = X_scaled[train_idx], X_scaled[val_idx]
                y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]
                
                # 训练
                model.fit(X_train_fold, y_train_fold)
                
                # 预测
                y_pred = model.predict(X_val_fold)
                
                # 评估
                mae = mean_absolute_error(y_val_fold, y_pred)
                rmse = np.sqrt(mean_squared_error(y_val_fold, y_pred))
                r2 = r2_score(y_val_fold, y_pred)
                
                errors = np.abs(y_pred - y_val_fold)
                acc_5 = (errors <= 5).mean() * 100
                acc_10 = (errors <= 10).mean() * 100
                acc_15 = (errors <= 15).mean() * 100
                
                cv_scores.append({
                    'fold': fold + 1,
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'acc_5': acc_5,
                    'acc_10': acc_10,
                    'acc_15': acc_15
                })
            
            # 计算平均性能
            avg_mae = np.mean([s['mae'] for s in cv_scores])
            avg_rmse = np.mean([s['rmse'] for s in cv_scores])
            avg_r2 = np.mean([s['r2'] for s in cv_scores])
            avg_acc_5 = np.mean([s['acc_5'] for s in cv_scores])
            avg_acc_10 = np.mean([s['acc_10'] for s in cv_scores])
            avg_acc_15 = np.mean([s['acc_15'] for s in cv_scores])
            
            cv_results[name] = {
                'avg_mae': avg_mae,
                'avg_rmse': avg_rmse,
                'avg_r2': avg_r2,
                'avg_acc_5': avg_acc_5,
                'avg_acc_10': avg_acc_10,
                'avg_acc_15': avg_acc_15,
                'fold_scores': cv_scores
            }
            
            print(f"{name}:")
            print(f"  平均MAE: {avg_mae:.2f} kWh")
            print(f"  平均RMSE: {avg_rmse:.2f} kWh")
            print(f"  平均R²: {avg_r2:.4f}")
            print(f"  平均±5kWh准确率: {avg_acc_5:.1f}%")
            print(f"  平均±10kWh准确率: {avg_acc_10:.1f}%")
            print(f"  平均±15kWh准确率: {avg_acc_15:.1f}%")
            print()
        
        # 计算集成权重（基于R²性能）
        total_r2 = sum([results['avg_r2'] for results in cv_results.values()])
        self.weights = {name: results['avg_r2'] / total_r2 for name, results in cv_results.items()}
        
        print(f"集成模型权重:")
        for name, weight in self.weights.items():
            print(f"  {name}: {weight:.3f}")
        
        # 在全部数据上训练最终模型
        print(f"\n在全部数据上训练最终模型...")
        for name, model in models.items():
            model.fit(X_scaled, y)
            self.models[name] = model
        
        self.is_trained = True
        self.training_history = cv_results
        
        print("✅ 模型训练完成")
        return cv_results
    
    def predict(self, X):
        """集成预测"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        X_scaled = self.scaler.transform(X)
        
        # 获取各模型预测
        predictions = {}
        for name, model in self.models.items():
            predictions[name] = model.predict(X_scaled)
        
        # 加权集成
        ensemble_pred = np.zeros(len(X))
        for name, pred in predictions.items():
            ensemble_pred += pred * self.weights[name]
        
        return ensemble_pred
    
    def evaluate_final_performance(self, df):
        """最终性能评估（时间序列分割）"""
        print(f"\n" + "="*60)
        print("最终性能评估（时间序列分割，无数据泄露）")
        print("="*60)
        
        # 特征工程
        X = self.feature_engineer.create_realtime_features(df)
        y = df['vice_total_energy_kwh']
        
        # 时间序列分割：前80%训练，后20%测试
        split_idx = int(len(X) * 0.8)
        X_test = X.iloc[split_idx:]
        y_test = y.iloc[split_idx:]
        
        print(f"测试集大小: {len(y_test)} 样本")
        print(f"测试集时间范围: {split_idx} - {len(X)}")
        
        # 预测
        y_pred = self.predict(X_test)
        
        # 整体性能评估
        mae = mean_absolute_error(y_test, y_pred)
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        r2 = r2_score(y_test, y_pred)
        
        errors = np.abs(y_pred - y_test)
        acc_5 = (errors <= 5).mean() * 100
        acc_7 = (errors <= 7).mean() * 100
        acc_10 = (errors <= 10).mean() * 100
        acc_15 = (errors <= 15).mean() * 100
        
        print(f"\n整体性能指标:")
        print(f"  MAE: {mae:.2f} kWh")
        print(f"  RMSE: {rmse:.2f} kWh")
        print(f"  R²: {r2:.4f}")
        print(f"  ±5 kWh 准确率: {acc_5:.1f}%")
        print(f"  ±7 kWh 准确率: {acc_7:.1f}%")
        print(f"  ±10 kWh 准确率: {acc_10:.1f}% (目标: 80%)")
        print(f"  ±15 kWh 准确率: {acc_15:.1f}%")
        
        # 按工艺类型分析
        print(f"\n按工艺类型性能分析:")
        process_results = {}
        
        for process_type, process_name in [(1, '首投'), (0, '复投')]:
            mask = X_test['is_first_cast'] == process_type
            if mask.sum() > 0:
                process_errors = errors[mask]
                process_y_test = y_test[mask]
                process_y_pred = y_pred[mask]
                
                process_mae = np.mean(process_errors)
                process_rmse = np.sqrt(mean_squared_error(process_y_test, process_y_pred))
                process_r2 = r2_score(process_y_test, process_y_pred)
                process_acc_5 = (process_errors <= 5).mean() * 100
                process_acc_10 = (process_errors <= 10).mean() * 100
                process_acc_15 = (process_errors <= 15).mean() * 100
                
                process_results[process_name] = {
                    'sample_count': mask.sum(),
                    'mae': process_mae,
                    'rmse': process_rmse,
                    'r2': process_r2,
                    'acc_5': process_acc_5,
                    'acc_10': process_acc_10,
                    'acc_15': process_acc_15
                }
                
                print(f"  {process_name} (样本数: {mask.sum()}):")
                print(f"    MAE: {process_mae:.2f} kWh")
                print(f"    RMSE: {process_rmse:.2f} kWh")
                print(f"    R²: {process_r2:.4f}")
                print(f"    ±5kWh准确率: {process_acc_5:.1f}%")
                print(f"    ±10kWh准确率: {process_acc_10:.1f}%")
                print(f"    ±15kWh准确率: {process_acc_15:.1f}%")
        
        # 目标达成分析
        print(f"\n目标达成分析:")
        target_gap = 80.0 - acc_10
        if acc_10 >= 80:
            print(f"  ✅ 已达到±10kWh准确率80%目标")
        else:
            print(f"  ⚠️ 未达到目标，差距: {target_gap:.1f}%")
        
        return {
            'overall': {
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'acc_5': acc_5,
                'acc_7': acc_7,
                'acc_10': acc_10,
                'acc_15': acc_15,
                'target_gap': target_gap
            },
            'by_process': process_results,
            'test_size': len(y_test)
        }

def main():
    """主函数"""
    # 1. 环境检查
    if not EnvironmentChecker.check_environment():
        sys.exit(1)
    
    print(f"\n开始实时副功率预测模型训练...")
    print(f"训练时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 2. 加载数据
    print(f"\n加载训练数据...")
    df = pd.read_csv('output_results/A01_A40_cycles__analysis.csv')
    print(f"✅ 数据加载完成: {df.shape}")
    
    # 3. 创建并训练模型
    model = RealtimeVicePowerModel()
    cv_results = model.train_with_time_series_cv(df)
    
    # 4. 最终性能评估
    final_results = model.evaluate_final_performance(df)
    
    return model, cv_results, final_results

if __name__ == "__main__":
    model, cv_results, final_results = main()
