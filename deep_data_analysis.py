#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入分析训练数据vs随机抽取数据的具体差异
找出71.3%准确率的"特定数据条件"
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def check_environment():
    """检查环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    if conda_env != 'lj_env_1':
        print(f"❌ 错误：必须在lj_env_1环境中运行")
        return False
    else:
        print("✅ 环境检查通过：lj_env_1")
        return True

def load_training_data():
    """加载训练数据"""
    print("📊 加载训练数据...")
    
    # 查找训练数据的可能位置
    possible_paths = [
        "realtime_vice_power_models/processed_data.csv",
        "realtime_vice_power_models/training_data.csv",
        "processed_data.csv",
        "training_data.csv"
    ]
    
    # 查找所有CSV文件
    all_csv_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.csv'):
                all_csv_files.append(Path(root) / file)
    
    print(f"找到的所有CSV文件:")
    for i, file in enumerate(all_csv_files[:20]):  # 只显示前20个
        print(f"  {i+1}. {file} ({os.path.getsize(file) / 1024:.1f} KB)")
    
    # 尝试找到最可能的训练数据
    training_candidates = []
    for file in all_csv_files:
        try:
            df = pd.read_csv(file, nrows=5)  # 只读前5行检查结构
            required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
            if all(col in df.columns for col in required_cols):
                file_size = os.path.getsize(file)
                training_candidates.append((file, file_size, len(df.columns)))
        except:
            continue
    
    print(f"\n可能的训练数据文件:")
    for file, size, cols in training_candidates:
        print(f"  📄 {file} (大小: {size/1024:.1f}KB, 列数: {cols})")
    
    return training_candidates

def analyze_specific_training_data():
    """分析具体的训练数据来源"""
    print("\n🔍 分析训练数据的具体来源...")
    
    # 检查训练脚本中的数据加载过程
    training_script = Path("realtime_model_training.py")
    if training_script.exists():
        print(f"分析训练脚本: {training_script}")
        
        with open(training_script, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找数据加载相关代码
        lines = content.split('\n')
        data_loading_lines = []
        
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in ['read_csv', 'load_data', 'glob', '*.csv']):
                data_loading_lines.append((i+1, line.strip()))
        
        print(f"数据加载相关代码:")
        for line_num, line in data_loading_lines:
            print(f"  第{line_num}行: {line}")
        
        # 查找数据筛选和处理逻辑
        filtering_keywords = ['filter', 'dropna', 'drop', 'query', 'loc', 'iloc', '>', '<', '==']
        filtering_lines = []
        
        for i, line in enumerate(lines):
            if any(keyword in line for keyword in filtering_keywords) and 'df' in line:
                filtering_lines.append((i+1, line.strip()))
        
        print(f"\n数据筛选相关代码:")
        for line_num, line in filtering_lines[:10]:  # 只显示前10个
            print(f"  第{line_num}行: {line}")

def load_and_compare_datasets():
    """加载并对比数据集"""
    print("\n📈 加载并对比数据集...")
    
    datasets = {}
    
    # 1. 尝试加载训练数据
    training_files = [
        "realtime_vice_power_models/processed_data.csv",
        "processed_data.csv"
    ]
    
    for file_path in training_files:
        if Path(file_path).exists():
            try:
                df = pd.read_csv(file_path)
                datasets['training'] = df
                print(f"✅ 加载训练数据: {file_path} ({len(df)}行)")
                break
            except Exception as e:
                print(f"❌ 加载失败 {file_path}: {e}")
    
    # 2. 加载随机抽取的验证数据
    validation_dirs = list(Path(".").glob("v7_real_validation_*"))
    if validation_dirs:
        latest_dir = max(validation_dirs, key=lambda x: x.stat().st_mtime)
        validation_file = latest_dir / "v7_real_data_validation_results.csv"
        
        if validation_file.exists():
            try:
                df = pd.read_csv(validation_file)
                # 重构原始数据
                validation_data = pd.DataFrame({
                    'weight_difference': df['weight_difference'],
                    'silicon_thermal_energy_kwh': df['silicon_thermal_energy_kwh'],
                    'vice_total_energy_kwh': df['actual_vice_power'],
                    'feed_type': df['feed_type']
                })
                datasets['validation'] = validation_data
                print(f"✅ 加载验证数据: {validation_file} ({len(validation_data)}行)")
            except Exception as e:
                print(f"❌ 加载验证数据失败: {e}")
    
    # 3. 尝试加载原始大数据集
    output_dirs = []
    for root, dirs, files in os.walk("."):
        for dir_name in dirs:
            if "output_results" in dir_name.lower():
                output_dirs.append(Path(root) / dir_name)
    
    if output_dirs:
        for output_dir in output_dirs[:1]:  # 只处理第一个
            csv_files = list(output_dir.glob("*.csv"))
            if csv_files:
                try:
                    df = pd.read_csv(csv_files[0])
                    # 检查列名并映射
                    column_mapping = {
                        'Weight_Difference': 'weight_difference',
                        'Silicon_Thermal_Energy_kWh': 'silicon_thermal_energy_kwh',
                        'Vice_Total_Energy_kWh': 'vice_total_energy_kwh',
                        'Feed_Type': 'feed_type'
                    }
                    
                    for old_name, new_name in column_mapping.items():
                        if old_name in df.columns:
                            df[new_name] = df[old_name]
                    
                    required_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
                    if all(col in df.columns for col in required_cols):
                        datasets['original'] = df
                        print(f"✅ 加载原始数据: {csv_files[0]} ({len(df)}行)")
                    break
                except Exception as e:
                    print(f"❌ 加载原始数据失败: {e}")
    
    return datasets

def detailed_statistical_comparison(datasets):
    """详细统计对比"""
    print("\n📊 详细统计对比...")
    
    if len(datasets) < 2:
        print("❌ 数据集不足，无法对比")
        return
    
    # 创建对比表格
    comparison_data = []
    
    for name, df in datasets.items():
        stats = {
            'dataset': name,
            'sample_count': len(df),
            'weight_mean': df['weight_difference'].mean(),
            'weight_std': df['weight_difference'].std(),
            'weight_min': df['weight_difference'].min(),
            'weight_max': df['weight_difference'].max(),
            'energy_mean': df['silicon_thermal_energy_kwh'].mean(),
            'energy_std': df['silicon_thermal_energy_kwh'].std(),
            'energy_min': df['silicon_thermal_energy_kwh'].min(),
            'energy_max': df['silicon_thermal_energy_kwh'].max(),
            'vice_mean': df['vice_total_energy_kwh'].mean(),
            'vice_std': df['vice_total_energy_kwh'].std(),
            'vice_min': df['vice_total_energy_kwh'].min(),
            'vice_max': df['vice_total_energy_kwh'].max()
        }
        
        # 工艺类型分布
        if 'feed_type' in df.columns:
            feed_counts = df['feed_type'].value_counts()
            stats['first_cast_ratio'] = feed_counts.get('首投', 0) / len(df) * 100
            stats['recast_ratio'] = feed_counts.get('复投', 0) / len(df) * 100
        
        comparison_data.append(stats)
    
    comparison_df = pd.DataFrame(comparison_data)
    
    print("\n数据集统计对比:")
    print("="*100)
    print(f"{'数据集':<12} {'样本数':<8} {'重量均值':<10} {'重量标准差':<12} {'能量均值':<10} {'能量标准差':<12} {'副功率均值':<12} {'副功率标准差':<12}")
    print("-"*100)
    
    for _, row in comparison_df.iterrows():
        print(f"{row['dataset']:<12} {row['sample_count']:<8} {row['weight_mean']:<10.1f} {row['weight_std']:<12.1f} "
              f"{row['energy_mean']:<10.1f} {row['energy_std']:<12.1f} {row['vice_mean']:<12.1f} {row['vice_std']:<12.1f}")
    
    return comparison_df

def analyze_data_quality_differences(datasets):
    """分析数据质量差异"""
    print("\n🔍 分析数据质量差异...")
    
    for name, df in datasets.items():
        print(f"\n{name}数据集质量分析:")
        
        # 缺失值分析
        missing_count = df.isnull().sum().sum()
        print(f"  缺失值: {missing_count}")
        
        # 异常值分析（使用IQR方法）
        numeric_cols = ['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']
        outlier_counts = {}
        
        for col in numeric_cols:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
                outlier_counts[col] = len(outliers)
                print(f"  {col}异常值: {len(outliers)} ({len(outliers)/len(df)*100:.1f}%)")
        
        # 数据分布特征
        print(f"  数据分布特征:")
        for col in numeric_cols:
            if col in df.columns:
                skewness = df[col].skew()
                kurtosis = df[col].kurtosis()
                print(f"    {col}: 偏度={skewness:.2f}, 峰度={kurtosis:.2f}")

def create_distribution_comparison_plots(datasets):
    """创建分布对比图"""
    print("\n📊 创建分布对比图...")
    
    if len(datasets) < 2:
        return
    
    fig, axes = plt.subplots(3, 3, figsize=(18, 15))
    
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    
    # 重量分布对比
    for i, (name, df) in enumerate(datasets.items()):
        axes[0, 0].hist(df['weight_difference'], bins=30, alpha=0.6, 
                       color=colors[i], label=f'{name} (n={len(df)})')
    axes[0, 0].set_title('重量分布对比')
    axes[0, 0].set_xlabel('重量 (kg)')
    axes[0, 0].legend()
    
    # 能量分布对比
    for i, (name, df) in enumerate(datasets.items()):
        axes[0, 1].hist(df['silicon_thermal_energy_kwh'], bins=30, alpha=0.6, 
                       color=colors[i], label=f'{name} (n={len(df)})')
    axes[0, 1].set_title('能量分布对比')
    axes[0, 1].set_xlabel('硅热能 (kWh)')
    axes[0, 1].legend()
    
    # 副功率分布对比
    for i, (name, df) in enumerate(datasets.items()):
        axes[0, 2].hist(df['vice_total_energy_kwh'], bins=30, alpha=0.6, 
                       color=colors[i], label=f'{name} (n={len(df)})')
    axes[0, 2].set_title('副功率分布对比')
    axes[0, 2].set_xlabel('副功率 (kWh)')
    axes[0, 2].legend()
    
    # 箱线图对比
    dataset_names = list(datasets.keys())
    weight_data = [datasets[name]['weight_difference'] for name in dataset_names]
    energy_data = [datasets[name]['silicon_thermal_energy_kwh'] for name in dataset_names]
    vice_data = [datasets[name]['vice_total_energy_kwh'] for name in dataset_names]
    
    axes[1, 0].boxplot(weight_data, labels=dataset_names)
    axes[1, 0].set_title('重量分布箱线图')
    axes[1, 0].set_ylabel('重量 (kg)')
    
    axes[1, 1].boxplot(energy_data, labels=dataset_names)
    axes[1, 1].set_title('能量分布箱线图')
    axes[1, 1].set_ylabel('硅热能 (kWh)')
    
    axes[1, 2].boxplot(vice_data, labels=dataset_names)
    axes[1, 2].set_title('副功率分布箱线图')
    axes[1, 2].set_ylabel('副功率 (kWh)')
    
    # 散点图对比
    for i, (name, df) in enumerate(datasets.items()):
        sample_df = df.sample(n=min(500, len(df)), random_state=42)
        axes[2, 0].scatter(sample_df['weight_difference'], sample_df['vice_total_energy_kwh'], 
                          alpha=0.6, color=colors[i], label=name, s=20)
    axes[2, 0].set_title('重量 vs 副功率')
    axes[2, 0].set_xlabel('重量 (kg)')
    axes[2, 0].set_ylabel('副功率 (kWh)')
    axes[2, 0].legend()
    
    for i, (name, df) in enumerate(datasets.items()):
        sample_df = df.sample(n=min(500, len(df)), random_state=42)
        axes[2, 1].scatter(sample_df['silicon_thermal_energy_kwh'], sample_df['vice_total_energy_kwh'], 
                          alpha=0.6, color=colors[i], label=name, s=20)
    axes[2, 1].set_title('能量 vs 副功率')
    axes[2, 1].set_xlabel('硅热能 (kWh)')
    axes[2, 1].set_ylabel('副功率 (kWh)')
    axes[2, 1].legend()
    
    # 相关性热图
    if 'training' in datasets:
        corr_data = datasets['training'][['weight_difference', 'silicon_thermal_energy_kwh', 'vice_total_energy_kwh']].corr()
        im = axes[2, 2].imshow(corr_data, cmap='coolwarm', aspect='auto')
        axes[2, 2].set_title('训练数据相关性')
        axes[2, 2].set_xticks(range(len(corr_data.columns)))
        axes[2, 2].set_yticks(range(len(corr_data.columns)))
        axes[2, 2].set_xticklabels(['重量', '能量', '副功率'], rotation=45)
        axes[2, 2].set_yticklabels(['重量', '能量', '副功率'])
        
        # 添加数值标注
        for i in range(len(corr_data.columns)):
            for j in range(len(corr_data.columns)):
                axes[2, 2].text(j, i, f'{corr_data.iloc[i, j]:.2f}', 
                               ha='center', va='center', color='black')
    
    plt.tight_layout()
    plt.savefig('detailed_data_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 分布对比图已保存: detailed_data_comparison.png")

def main():
    """主函数"""
    print("="*60)
    print("深入分析训练数据的特定条件")
    print("="*60)
    
    # 1. 环境检查
    if not check_environment():
        return False
    
    # 2. 加载训练数据候选
    training_candidates = load_training_data()
    
    # 3. 分析训练数据来源
    analyze_specific_training_data()
    
    # 4. 加载并对比数据集
    datasets = load_and_compare_datasets()
    
    if len(datasets) == 0:
        print("❌ 未找到可对比的数据集")
        return False
    
    print(f"\n找到 {len(datasets)} 个数据集:")
    for name, df in datasets.items():
        print(f"  {name}: {len(df)} 样本")
    
    # 5. 详细统计对比
    comparison_df = detailed_statistical_comparison(datasets)
    
    # 6. 分析数据质量差异
    analyze_data_quality_differences(datasets)
    
    # 7. 创建可视化对比
    create_distribution_comparison_plots(datasets)
    
    # 8. 总结分析
    print(f"\n🎯 关键发现:")
    if len(datasets) >= 2:
        print("数据集之间存在显著差异，这解释了性能差异的原因")
        
        if 'training' in datasets and 'validation' in datasets:
            train_df = datasets['training']
            val_df = datasets['validation']
            
            print(f"\n训练数据 vs 验证数据对比:")
            print(f"  样本数: {len(train_df)} vs {len(val_df)}")
            print(f"  重量均值: {train_df['weight_difference'].mean():.1f} vs {val_df['weight_difference'].mean():.1f}")
            print(f"  副功率均值: {train_df['vice_total_energy_kwh'].mean():.1f} vs {val_df['vice_total_energy_kwh'].mean():.1f}")
            print(f"  重量标准差: {train_df['weight_difference'].std():.1f} vs {val_df['weight_difference'].std():.1f}")
    
    return True

if __name__ == "__main__":
    success = main()
