# 模型复现指南

## 🎯 复现目标

本指南帮助您完全复现85.4%准确率的副功率预测模型。

## 📋 环境准备

### 1. Python环境
```bash
conda create -n vice_power_env python=3.8
conda activate vice_power_env
```

### 2. 安装依赖
```bash
pip install scikit-learn==1.3.0
pip install pandas==2.0.3
pip install numpy==1.24.3
pip install joblib==1.3.2
pip install xgboost==1.7.6
pip install lightgbm==4.0.0
```

## 🔄 复现步骤

### 1. 数据准备
```bash
# 确保数据文件在正确位置
cp data/all_folders_summary.csv ./
```

### 2. 运行训练代码
```bash
python code/focused_improvement_analysis.py
```

### 3. 验证结果
训练完成后，检查输出结果：
- 最佳模型: SVR
- ±10kWh准确率: 应接近85.4%
- MAE: 应接近7.96kWh

## 📊 预期输出

```
🎯 最佳模型: svr
   ±10kWh准确率: 85.4%
   ✅ 达到70%目标！
```

## 🔧 关键参数

### SVR模型参数
```python
SVR(kernel='rbf', C=100, gamma='scale', epsilon=0.1)
```

### 特征选择
- 选择30个最佳特征
- 使用f_regression评分函数

### 数据分割
- 80/20时间序列分割
- 训练集: 1,695样本
- 测试集: 424样本

## ⚠️ 常见问题

### Q: 准确率达不到85.4%？
A: 检查数据完整性和特征工程步骤

### Q: 模型训练时间过长？
A: 可以减少n_estimators或使用更少的特征

### Q: 内存不足？
A: 减少数据量或使用增量学习

## 📞 技术支持

如遇到复现问题，请检查：
1. 数据文件完整性
2. 环境依赖版本
3. 随机种子设置
4. 特征工程步骤
